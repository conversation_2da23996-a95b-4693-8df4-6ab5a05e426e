// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		B7809AB92AE7527A00C9AB1E /* WFPttClientProxy.h in Headers */ = {isa = PBXBuildFile; fileRef = B7809AB72AE7527A00C9AB1E /* WFPttClientProxy.h */; };
		B7809ABA2AE7527A00C9AB1E /* WFPttClientProxy.m in Sources */ = {isa = PBXBuildFile; fileRef = B7809AB82AE7527A00C9AB1E /* WFPttClientProxy.m */; };
		B79859242AE6972200169D79 /* WFPttClientModule.h in Headers */ = {isa = PBXBuildFile; fileRef = B79859222AE6972200169D79 /* WFPttClientModule.h */; };
		B79859252AE6972200169D79 /* WFPttClientModule.m in Sources */ = {isa = PBXBuildFile; fileRef = B79859232AE6972200169D79 /* WFPttClientModule.m */; };
		B79859432AE6A53600169D79 /* WFChatClient.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = B79859422AE6A53600169D79 /* WFChatClient.xcframework */; };
		B79859472AE6A6B300169D79 /* PttClient.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = B79859462AE6A6B300169D79 /* PttClient.xcframework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		B7809AB72AE7527A00C9AB1E /* WFPttClientProxy.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WFPttClientProxy.h; sourceTree = "<group>"; };
		B7809AB82AE7527A00C9AB1E /* WFPttClientProxy.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WFPttClientProxy.m; sourceTree = "<group>"; };
		B798590F2AE6957300169D79 /* WFPttClientUniPlugin.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = WFPttClientUniPlugin.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B79859222AE6972200169D79 /* WFPttClientModule.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WFPttClientModule.h; sourceTree = "<group>"; };
		B79859232AE6972200169D79 /* WFPttClientModule.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WFPttClientModule.m; sourceTree = "<group>"; };
		B79859422AE6A53600169D79 /* WFChatClient.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = WFChatClient.xcframework; path = ../WF_SDK/WFChatClient.xcframework; sourceTree = "<absolute>"; };
		B79859462AE6A6B300169D79 /* PttClient.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = PttClient.xcframework; path = ../WF_SDK/PttClient.xcframework; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		B798590C2AE6957300169D79 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B79859432AE6A53600169D79 /* WFChatClient.xcframework in Frameworks */,
				B79859472AE6A6B300169D79 /* PttClient.xcframework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		B79859052AE6957300169D79 = {
			isa = PBXGroup;
			children = (
				B79859112AE6957300169D79 /* WFPttClientUniPlugin */,
				B79859102AE6957300169D79 /* Products */,
				B79859412AE6A53600169D79 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		B79859102AE6957300169D79 /* Products */ = {
			isa = PBXGroup;
			children = (
				B798590F2AE6957300169D79 /* WFPttClientUniPlugin.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		B79859112AE6957300169D79 /* WFPttClientUniPlugin */ = {
			isa = PBXGroup;
			children = (
				B79859222AE6972200169D79 /* WFPttClientModule.h */,
				B79859232AE6972200169D79 /* WFPttClientModule.m */,
				B7809AB72AE7527A00C9AB1E /* WFPttClientProxy.h */,
				B7809AB82AE7527A00C9AB1E /* WFPttClientProxy.m */,
			);
			path = WFPttClientUniPlugin;
			sourceTree = "<group>";
		};
		B79859412AE6A53600169D79 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				B79859462AE6A6B300169D79 /* PttClient.xcframework */,
				B79859422AE6A53600169D79 /* WFChatClient.xcframework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		B798590A2AE6957300169D79 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B7809AB92AE7527A00C9AB1E /* WFPttClientProxy.h in Headers */,
				B79859242AE6972200169D79 /* WFPttClientModule.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		B798590E2AE6957300169D79 /* WFPttClientUniPlugin */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B79859162AE6957300169D79 /* Build configuration list for PBXNativeTarget "WFPttClientUniPlugin" */;
			buildPhases = (
				B798590A2AE6957300169D79 /* Headers */,
				B798590B2AE6957300169D79 /* Sources */,
				B798590C2AE6957300169D79 /* Frameworks */,
				B798590D2AE6957300169D79 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = WFPttClientUniPlugin;
			productName = WFPttClientUniPlugin;
			productReference = B798590F2AE6957300169D79 /* WFPttClientUniPlugin.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		B79859062AE6957300169D79 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					B798590E2AE6957300169D79 = {
						CreatedOnToolsVersion = 15.0.1;
					};
				};
			};
			buildConfigurationList = B79859092AE6957300169D79 /* Build configuration list for PBXProject "WFPttClientUniPlugin" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = B79859052AE6957300169D79;
			productRefGroup = B79859102AE6957300169D79 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				B798590E2AE6957300169D79 /* WFPttClientUniPlugin */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		B798590D2AE6957300169D79 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		B798590B2AE6957300169D79 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B79859252AE6972200169D79 /* WFPttClientModule.m in Sources */,
				B7809ABA2AE7527A00C9AB1E /* WFPttClientProxy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		B79859142AE6957300169D79 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		B79859152AE6957300169D79 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		B79859172AE6957300169D79 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = Y8356M2VAP;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				GENERATE_INFOPLIST_FILE = YES;
				HEADER_SEARCH_PATHS = "\"$(SRCROOT)/../../SDK/inc\"/**";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				PRODUCT_BUNDLE_IDENTIFIER = cn.wildfirechat.WFPttClientUniPlugin;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		B79859182AE6957300169D79 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = Y8356M2VAP;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				GENERATE_INFOPLIST_FILE = YES;
				HEADER_SEARCH_PATHS = "\"$(SRCROOT)/../../SDK/inc\"/**";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				PRODUCT_BUNDLE_IDENTIFIER = cn.wildfirechat.WFPttClientUniPlugin;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		B79859092AE6957300169D79 /* Build configuration list for PBXProject "WFPttClientUniPlugin" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B79859142AE6957300169D79 /* Debug */,
				B79859152AE6957300169D79 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B79859162AE6957300169D79 /* Build configuration list for PBXNativeTarget "WFPttClientUniPlugin" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B79859172AE6957300169D79 /* Debug */,
				B79859182AE6957300169D79 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = B79859062AE6957300169D79 /* Project object */;
}
