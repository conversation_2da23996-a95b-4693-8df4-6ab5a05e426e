// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		240905031C200AEF0070786F /* CoreBluetooth.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 240905021C200AEF0070786F /* CoreBluetooth.framework */; };
		242725CB1D2666ED00EBD79E /* JavaScriptCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 242725CA1D2666ED00EBD79E /* JavaScriptCore.framework */; };
		242725CD1D26686700EBD79E /* CoreMotion.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 242725CC1D26686700EBD79E /* CoreMotion.framework */; };
		2430A7011E11603E00D3D42D /* libcrypto.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 2430A6FF1E11603E00D3D42D /* libcrypto.a */; };
		2430A7021E11603E00D3D42D /* libssl.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 2430A7001E11603E00D3D42D /* libssl.a */; };
		2447371B1D0830BB00D0F08F /* WebKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2447371A1D0830BB00D0F08F /* WebKit.framework */; };
		247F85DB1FA32B2C006ECAC6 /* liblibPDRCore.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 247F85DA1FA32B2C006ECAC6 /* liblibPDRCore.a */; };
		24A7515F1D9CCCC600C8B0F9 /* QuickLook.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 24A7515E1D9CCCC600C8B0F9 /* QuickLook.framework */; };
		24AFD8021CB50C3200C0F062 /* TencentOpenAPI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD7FE1CB50C3200C0F062 /* TencentOpenAPI.framework */; };
		24AFD8401CB50C4000C0F062 /* libalixpayment.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD8031CB50C3F00C0F062 /* libalixpayment.a */; };
		24AFD8431CB50C4000C0F062 /* libBaiduMobStatForSDK.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD8061CB50C3F00C0F062 /* libBaiduMobStatForSDK.a */; };
		24AFD8461CB50C4000C0F062 /* libcoreSupport.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD8091CB50C3F00C0F062 /* libcoreSupport.a */; };
		24AFD8491CB50C4000C0F062 /* libIAPPay.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD80C1CB50C3F00C0F062 /* libIAPPay.a */; };
		24AFD84C1CB50C4000C0F062 /* liblibAccelerometer.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD80F1CB50C3F00C0F062 /* liblibAccelerometer.a */; };
		24AFD84D1CB50C4000C0F062 /* liblibBarcode.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD8101CB50C3F00C0F062 /* liblibBarcode.a */; };
		24AFD84E1CB50C4000C0F062 /* liblibCache.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD8111CB50C3F00C0F062 /* liblibCache.a */; };
		24AFD84F1CB50C4000C0F062 /* liblibCamera.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD8121CB50C3F00C0F062 /* liblibCamera.a */; };
		24AFD8501CB50C4000C0F062 /* liblibContacts.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD8131CB50C3F00C0F062 /* liblibContacts.a */; };
		24AFD8521CB50C4000C0F062 /* liblibIO.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD8151CB50C4000C0F062 /* liblibIO.a */; };
		24AFD8531CB50C4000C0F062 /* liblibLog.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD8161CB50C4000C0F062 /* liblibLog.a */; };
		24AFD8541CB50C4000C0F062 /* liblibMap.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD8171CB50C4000C0F062 /* liblibMap.a */; };
		24AFD8551CB50C4000C0F062 /* liblibMedia.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD8181CB50C4000C0F062 /* liblibMedia.a */; };
		24AFD8561CB50C4000C0F062 /* liblibMessage.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD8191CB50C4000C0F062 /* liblibMessage.a */; };
		24AFD8571CB50C4000C0F062 /* liblibNativeObj.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD81A1CB50C4000C0F062 /* liblibNativeObj.a */; };
		24AFD8581CB50C4000C0F062 /* liblibNativeUI.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD81B1CB50C4000C0F062 /* liblibNativeUI.a */; };
		24AFD8591CB50C4000C0F062 /* liblibNavigator.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD81C1CB50C4000C0F062 /* liblibNavigator.a */; };
		24AFD85A1CB50C4000C0F062 /* liblibOauth.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD81D1CB50C4000C0F062 /* liblibOauth.a */; };
		24AFD85B1CB50C4000C0F062 /* liblibOrientation.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD81E1CB50C4000C0F062 /* liblibOrientation.a */; };
		24AFD85C1CB50C4000C0F062 /* liblibPayment.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD81F1CB50C4000C0F062 /* liblibPayment.a */; };
		24AFD85E1CB50C4000C0F062 /* liblibPGInvocation.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD8211CB50C4000C0F062 /* liblibPGInvocation.a */; };
		24AFD85F1CB50C4000C0F062 /* liblibPGProximity.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD8221CB50C4000C0F062 /* liblibPGProximity.a */; };
		24AFD8601CB50C4000C0F062 /* liblibPush.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD8231CB50C4000C0F062 /* liblibPush.a */; };
		24AFD8611CB50C4000C0F062 /* liblibShare.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD8241CB50C4000C0F062 /* liblibShare.a */; };
		24AFD8621CB50C4000C0F062 /* liblibSpeech.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD8251CB50C4000C0F062 /* liblibSpeech.a */; };
		24AFD8631CB50C4000C0F062 /* liblibStatistic.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD8261CB50C4000C0F062 /* liblibStatistic.a */; };
		24AFD8641CB50C4000C0F062 /* liblibStorage.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD8271CB50C4000C0F062 /* liblibStorage.a */; };
		24AFD8651CB50C4000C0F062 /* liblibUI.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD8281CB50C4000C0F062 /* liblibUI.a */; };
		24AFD8681CB50C4000C0F062 /* liblibXHR.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD82B1CB50C4000C0F062 /* liblibXHR.a */; };
		24AFD8691CB50C4000C0F062 /* liblibZip.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD82C1CB50C4000C0F062 /* liblibZip.a */; };
		24AFD86B1CB50C4000C0F062 /* libopencore-amrnb.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD82E1CB50C4000C0F062 /* libopencore-amrnb.a */; };
		24AFD86F1CB50C4000C0F062 /* libQQOauth.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD8321CB50C4000C0F062 /* libQQOauth.a */; };
		24AFD8701CB50C4000C0F062 /* libQQShare.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD8331CB50C4000C0F062 /* libQQShare.a */; };
		24AFD8721CB50C4000C0F062 /* libSinaShare.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD8351CB50C4000C0F062 /* libSinaShare.a */; };
		24AFD8731CB50C4000C0F062 /* libSinaWBOauth.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD8361CB50C4000C0F062 /* libSinaWBOauth.a */; };
		24AFD8761CB50C4000C0F062 /* libTouchJSON.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD8391CB50C4000C0F062 /* libTouchJSON.a */; };
		24AFD8791CB50C4000C0F062 /* libWeiboSDK.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD83C1CB50C4000C0F062 /* libWeiboSDK.a */; };
		24AFD87A1CB50C4000C0F062 /* libweixinShare.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD83D1CB50C4000C0F062 /* libweixinShare.a */; };
		24AFD87B1CB50C4000C0F062 /* libWXOauth.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD83E1CB50C4000C0F062 /* libWXOauth.a */; };
		24AFD87C1CB50C4000C0F062 /* libwxpay.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD83F1CB50C4000C0F062 /* libwxpay.a */; };
		24BD5AE81C99491D00B05AA2 /* libsqlite3.0.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 24BD5AE71C99491D00B05AA2 /* libsqlite3.0.tbd */; };
		24BD5AEA1C99492A00B05AA2 /* libiconv.2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 24BD5AE91C99492A00B05AA2 /* libiconv.2.tbd */; };
		24BD5AEE1C99494200B05AA2 /* libxml2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 24BD5AED1C99494200B05AA2 /* libxml2.tbd */; };
		24BD5AF01C99494A00B05AA2 /* libz.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 24BD5AEF1C99494A00B05AA2 /* libz.tbd */; };
		24BD5AF21C994A1700B05AA2 /* libicucore.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 24BD5AF11C994A1700B05AA2 /* libicucore.tbd */; };
		24BD5AF61C994BB200B05AA2 /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 24BD5AF51C994BB200B05AA2 /* libc++.tbd */; };
		24F990131DFBDA3300848C2B /* CoreData.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 24F990121DFBDA3300848C2B /* CoreData.framework */; };
		2F0BA3F9215B48A700F67004 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 2F0BA3F8215B48A700F67004 /* Images.xcassets */; };
		2F0BA410215B784100F67004 /* Contacts.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2F0BA40F215B784000F67004 /* Contacts.framework */; };
		2F0BA412215B814100F67004 /* liblibVideo.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 2F0BA411215B814000F67004 /* liblibVideo.a */; };
		2F0BA416215B8B1400F67004 /* VideoToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2F0BA415215B8B1300F67004 /* VideoToolbox.framework */; };
		2F0BA418215B8B5C00F67004 /* libbz2.1.0.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 2F0BA417215B8B5C00F67004 /* libbz2.1.0.tbd */; };
		2F0BA4DC215BA12300F67004 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 2F0BA4DB215BA12300F67004 /* LaunchScreen.storyboard */; };
		2F434B062C787ED80020481E /* AlipaySDK.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2F434B052C787ED70020481E /* AlipaySDK.xcframework */; };
		2F4864C2293E10EE00142360 /* DCUniRecord.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2F4864C1293E10EE00142360 /* DCUniRecord.framework */; };
		2F5FAE692865DB7800430AFA /* BMKLocationKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2F5FAE672865DB7800430AFA /* BMKLocationKit.framework */; };
		2F5FAE6A2865DB7800430AFA /* KSCrash.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2F5FAE682865DB7800430AFA /* KSCrash.framework */; };
		2F9FE7EA2BD8E834001137CC /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 2F9FE7E92BD8E834001137CC /* PrivacyInfo.xcprivacy */; };
		2FBB55F529530500002214BF /* liblibWeex.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 2FBB55F429530500002214BF /* liblibWeex.a */; };
		2FD11BBB215C79C5000A23AD /* liblibAdSupport.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 2FD11BBA215C79C5000A23AD /* liblibAdSupport.a */; };
		2FDE6A37296D742B004C7701 /* GTSDK.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2FDE6A36296D742B004C7701 /* GTSDK.xcframework */; };
		2FDE6A3A296D74B3004C7701 /* DCloud.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2FDE6A39296D74B3004C7701 /* DCloud.swift */; };
		4F48903E24656F73003B56F0 /* mapapi.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 4F48902024656F73003B56F0 /* mapapi.bundle */; };
		4F48903F24656F73003B56F0 /* __uniappes6.js in Resources */ = {isa = PBXBuildFile; fileRef = 4F48902124656F73003B56F0 /* __uniappes6.js */; };
		4F48904024656F73003B56F0 /* MiPassport.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 4F48902224656F73003B56F0 /* MiPassport.bundle */; };
		4F48904124656F73003B56F0 /* AlipaySDK.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 4F48902324656F73003B56F0 /* AlipaySDK.bundle */; };
		4F48904224656F73003B56F0 /* uni-jsframework.js in Resources */ = {isa = PBXBuildFile; fileRef = 4F48902424656F73003B56F0 /* uni-jsframework.js */; };
		4F48904324656F73003B56F0 /* WeiboSDK.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 4F48902524656F73003B56F0 /* WeiboSDK.bundle */; };
		4F48904524656F73003B56F0 /* bds_license.dat in Resources */ = {isa = PBXBuildFile; fileRef = 4F48902824656F73003B56F0 /* bds_license.dat */; };
		4F48904624656F73003B56F0 /* bds_easr_basic_model.dat in Resources */ = {isa = PBXBuildFile; fileRef = 4F48902924656F73003B56F0 /* bds_easr_basic_model.dat */; };
		4F48904724656F73003B56F0 /* bds_easr_mfe_cmvn.dat in Resources */ = {isa = PBXBuildFile; fileRef = 4F48902A24656F73003B56F0 /* bds_easr_mfe_cmvn.dat */; };
		4F48904824656F73003B56F0 /* temp_license_2018-02-24.dat in Resources */ = {isa = PBXBuildFile; fileRef = 4F48902B24656F73003B56F0 /* temp_license_2018-02-24.dat */; };
		4F48904924656F73003B56F0 /* bds_easr_mfe_dnn.dat in Resources */ = {isa = PBXBuildFile; fileRef = 4F48902C24656F73003B56F0 /* bds_easr_mfe_dnn.dat */; };
		4F48904A24656F73003B56F0 /* bds_easr_gramm.dat in Resources */ = {isa = PBXBuildFile; fileRef = 4F48902D24656F73003B56F0 /* bds_easr_gramm.dat */; };
		4F48904B24656F73003B56F0 /* bds_easr_wakeup_words.dat in Resources */ = {isa = PBXBuildFile; fileRef = 4F48902E24656F73003B56F0 /* bds_easr_wakeup_words.dat */; };
		4F48904C24656F73003B56F0 /* bds_easr_dnn_wakeup_model.dat in Resources */ = {isa = PBXBuildFile; fileRef = 4F48902F24656F73003B56F0 /* bds_easr_dnn_wakeup_model.dat */; };
		4F48904D24656F73003B56F0 /* DCTZImagePickerController.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 4F48903024656F73003B56F0 /* DCTZImagePickerController.bundle */; };
		4F48904E24656F73003B56F0 /* PandoraApi.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 4F48903124656F73003B56F0 /* PandoraApi.bundle */; };
		4F48904F24656F73003B56F0 /* unincomponents.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 4F48903224656F73003B56F0 /* unincomponents.ttf */; };
		4F48905024656F73003B56F0 /* QHADVideoPlayer.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 4F48903324656F73003B56F0 /* QHADVideoPlayer.bundle */; };
		4F48905124656F73003B56F0 /* AMap.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 4F48903424656F73003B56F0 /* AMap.bundle */; };
		4F48905224656F73003B56F0 /* uni-jsframework-dev.js in Resources */ = {isa = PBXBuildFile; fileRef = 4F48903524656F73003B56F0 /* uni-jsframework-dev.js */; };
		4F48905324656F73003B56F0 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 4F48903624656F73003B56F0 /* <EMAIL> */; };
		4F48905424656F73003B56F0 /* qucsdkResources.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 4F48903724656F73003B56F0 /* qucsdkResources.bundle */; };
		4F48905624656F73003B56F0 /* DCPGVideo.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 4F48903924656F73003B56F0 /* DCPGVideo.bundle */; };
		4F48905724656F73003B56F0 /* DCMediaVideo.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 4F48903A24656F73003B56F0 /* DCMediaVideo.bundle */; };
		4F48905824656F73003B56F0 /* DCSVProgressHUD.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 4F48903B24656F73003B56F0 /* DCSVProgressHUD.bundle */; };
		4F48905924656F73003B56F0 /* weex-polyfill.js in Resources */ = {isa = PBXBuildFile; fileRef = 4F48903C24656F73003B56F0 /* weex-polyfill.js */; };
		4F48905A24656F73003B56F0 /* weexUniJs.js in Resources */ = {isa = PBXBuildFile; fileRef = 4F48903D24656F73003B56F0 /* weexUniJs.js */; };
		4F4891202465734F003B56F0 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 4F48911E2465734F003B56F0 /* <EMAIL> */; };
		4F4891212465734F003B56F0 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 4F48911F2465734F003B56F0 /* <EMAIL> */; };
		4F4DAE6B25DE60DB0094E580 /* libUniPush.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 4F4DAE6A25DE60DB0094E580 /* libUniPush.a */; };
		4F5762F5261461A000A5C0BA /* MetalKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4F5762F4261461A000A5C0BA /* MetalKit.framework */; };
		4F5762F7261461AB00A5C0BA /* GLKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4F5762F6261461AB00A5C0BA /* GLKit.framework */; };
		4F8C6937265E502C00C2A73C /* Masonry.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4F8C6936265E502C00C2A73C /* Masonry.framework */; };
		4FA53F7926E5D09500BAD6A0 /* uni-jsframework-vue3.js in Resources */ = {isa = PBXBuildFile; fileRef = 4FA53F7826E5D09500BAD6A0 /* uni-jsframework-vue3.js */; };
		4FE3666C254FE70E00DCD173 /* libuchardet.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 4FE3666B254FE70E00DCD173 /* libuchardet.a */; };
		67229AD1230171AE0093F29A /* libDCUniAmap.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 67229AC9230171AE0093F29A /* libDCUniAmap.a */; };
		67229AD2230171AE0093F29A /* libDCUniVideo.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 67229ACA230171AE0093F29A /* libDCUniVideo.a */; };
		67229AD6230171AE0093F29A /* libDCUniGPUImage.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 67229ACE230171AE0093F29A /* libDCUniGPUImage.a */; };
		67229AD8230171AE0093F29A /* libDCUniMap.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 67229AD0230171AE0093F29A /* libDCUniMap.a */; };
		672CE2B322DC9118005A0D88 /* DCUniVideoPublic.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 672CE2B222DC9118005A0D88 /* DCUniVideoPublic.framework */; };
		672CE2B522DC916C005A0D88 /* libDCUniZXing.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 672CE2B422DC916C005A0D88 /* libDCUniZXing.a */; };
		672DEB2C23056152003F27CC /* libDCUniBarcode.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 67229ACB230171AE0093F29A /* libDCUniBarcode.a */; };
		6731F394232F4CE2007838BC /* libresolv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 6731F393232F4CE2007838BC /* libresolv.tbd */; };
		6731F396232F4D05007838BC /* UserNotifications.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6731F395232F4D05007838BC /* UserNotifications.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		6731F398232F4D8E007838BC /* Photos.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6731F397232F4D8E007838BC /* Photos.framework */; };
		673E6E7321E44ABF00C021FE /* liblibFingerprint.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 673E6E7221E44ABF00C021FE /* liblibFingerprint.a */; };
		6743942A23C98EB30085145E /* LaunchScreenAD.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 6743942923C98EB30085145E /* LaunchScreenAD.storyboard */; };
		67566B1F2251DC3A00BDF218 /* liblibSqlite.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 67566B1E2251DC3A00BDF218 /* liblibSqlite.a */; };
		6756AB6821F58ACD00765F52 /* liblibBeacon.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 6756AB6721F58ACC00765F52 /* liblibBeacon.a */; };
		6756AB6A21F58CA300765F52 /* liblibBlueTooth.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 6756AB6921F58CA300765F52 /* liblibBlueTooth.a */; };
		67A9340023A8B922004A4DF4 /* libSDWebImage.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 24AFD8341CB50C4000C0F062 /* libSDWebImage.a */; };
		67AB0B3D21EEEA6B0029B229 /* libAMapImp.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 67AB0B3C21EEEA6B0029B229 /* libAMapImp.a */; };
		67AB0B3F21EEEAA60029B229 /* AMapSearchKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 67AB0B3E21EEEAA60029B229 /* AMapSearchKit.framework */; };
		67AB0B4121EEEACA0029B229 /* MAMapKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 67AB0B4021EEEACA0029B229 /* MAMapKit.framework */; };
		67AB0B4321EEEAE50029B229 /* AMapFoundationKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 67AB0B4221EEEAE50029B229 /* AMapFoundationKit.framework */; };
		67AB0B9721EF10E70029B229 /* liblibGeolocation.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 67AB0B9621EF10E60029B229 /* liblibGeolocation.a */; };
		67AB0B9921EF10FE0029B229 /* libBaiduKeyVerify.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 67AB0B9821EF10FE0029B229 /* libBaiduKeyVerify.a */; };
		67AB0BA121EF111D0029B229 /* BaiduMapAPI_Search.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 67AB0B9B21EF111D0029B229 /* BaiduMapAPI_Search.framework */; };
		67AB0BA221EF111D0029B229 /* BaiduMapAPI_Utils.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 67AB0B9C21EF111D0029B229 /* BaiduMapAPI_Utils.framework */; };
		67AB0BA321EF111D0029B229 /* BaiduMapAPI_Map.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 67AB0B9D21EF111D0029B229 /* BaiduMapAPI_Map.framework */; };
		67AB0BA421EF111D0029B229 /* BaiduMapAPI_Base.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 67AB0B9E21EF111D0029B229 /* BaiduMapAPI_Base.framework */; };
		67B7CAA221DCE8180083E96A /* control.xml in Resources */ = {isa = PBXBuildFile; fileRef = 67B7CAA121DCE8180083E96A /* control.xml */; };
		67D6390921C12945005D1E8F /* libBaiduSpeechSDK.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 67D6390721C12944005D1E8F /* libBaiduSpeechSDK.a */; };
		67D6390A21C12945005D1E8F /* libbaiduSpeech.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 67D6390821C12944005D1E8F /* libbaiduSpeech.a */; };
		67E9CDCF22968D2E0076E0FB /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 67E9CDCD22968D2E0076E0FB /* Localizable.strings */; };
		7A1967C3212536EC00B330A9 /* libmp3lame.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 7A1967C2212536EC00B330A9 /* libmp3lame.a */; };
		7A1967C52125371000B330A9 /* storage.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7A1967C42125371000B330A9 /* storage.framework */; };
		7A1967C721253B5F00B330A9 /* libWeChatSDK_pay.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 7A1967C621253B5F00B330A9 /* libWeChatSDK_pay.a */; };
		7A49810B2126B01200D20880 /* libiconv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 7A49810A2126B01200D20880 /* libiconv.tbd */; };
		7A49810D2126B01900D20880 /* Accelerate.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7A49810C2126B01900D20880 /* Accelerate.framework */; };
		7ACF69AB19FF899A007C64F1 /* CFNetwork.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7ACF69AA19FF899A007C64F1 /* CFNetwork.framework */; };
		7ACF69AD19FF89B1007C64F1 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7ACF69AC19FF89B1007C64F1 /* Security.framework */; };
		8E163D021A8D208500308A8B /* AssetsLibrary.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8E163D011A8D208500308A8B /* AssetsLibrary.framework */; };
		8E6E37AC1B0E1B580036EB48 /* ImageIO.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8E6E37AB1B0E1B580036EB48 /* ImageIO.framework */; };
		8EED629C198A1D13000A4449 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8EED629B198A1D13000A4449 /* Foundation.framework */; };
		8EED629E198A1D13000A4449 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8EED629D198A1D13000A4449 /* CoreGraphics.framework */; };
		8EED62A0198A1D13000A4449 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8EED629F198A1D13000A4449 /* UIKit.framework */; };
		8EED62A6198A1D13000A4449 /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 8EED62A4198A1D13000A4449 /* InfoPlist.strings */; };
		8EED62A8198A1D13000A4449 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 8EED62A7198A1D13000A4449 /* main.m */; };
		8EED62AC198A1D13000A4449 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 8EED62AB198A1D13000A4449 /* AppDelegate.m */; };
		8EED62B5198A1D14000A4449 /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 8EED62B4198A1D14000A4449 /* ViewController.m */; };
		8EED6412198A2622000A4449 /* MobileCoreServices.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8EED6411198A2622000A4449 /* MobileCoreServices.framework */; };
		8EED6414198A262C000A4449 /* CoreMedia.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8EED6413198A262C000A4449 /* CoreMedia.framework */; };
		8EED6416198A2635000A4449 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8EED6415198A2635000A4449 /* CoreVideo.framework */; };
		8EED641C198A2654000A4449 /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8EED641B198A2654000A4449 /* SystemConfiguration.framework */; };
		8EED641E198A265F000A4449 /* CoreTelephony.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8EED641D198A265F000A4449 /* CoreTelephony.framework */; };
		8EED6420198A2668000A4449 /* MediaPlayer.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8EED641F198A2668000A4449 /* MediaPlayer.framework */; };
		8EED6422198A2678000A4449 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8EED6421198A2678000A4449 /* AudioToolbox.framework */; };
		8EED6590198A3DF7000A4449 /* Pandora in Resources */ = {isa = PBXBuildFile; fileRef = 8EED658F198A3DF7000A4449 /* Pandora */; };
		8EED6592198A5737000A4449 /* AddressBook.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8EED6591198A5737000A4449 /* AddressBook.framework */; };
		8EED6594198A5743000A4449 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8EED6593198A5743000A4449 /* AVFoundation.framework */; };
		8EED6596198A574C000A4449 /* CoreLocation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8EED6595198A574C000A4449 /* CoreLocation.framework */; };
		8EED6598198A5754000A4449 /* MessageUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8EED6597198A5754000A4449 /* MessageUI.framework */; };
		8EED659C198A5773000A4449 /* AddressBookUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8EED659B198A5773000A4449 /* AddressBookUI.framework */; };
		8EED659E198A5782000A4449 /* Social.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8EED659D198A5782000A4449 /* Social.framework */; };
		8EED65A0198A5789000A4449 /* Accounts.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8EED659F198A5789000A4449 /* Accounts.framework */; };
		8EED65A6198A626B000A4449 /* MapKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8EED65A5198A626B000A4449 /* MapKit.framework */; };
		8EED65A8198A6273000A4449 /* OpenGLES.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8EED65A7198A6273000A4449 /* OpenGLES.framework */; };
		95BC64EE2754A50000CB55B9 /* UMAPM.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 95BC64EB2754A50000CB55B9 /* UMAPM.framework */; };
		B7A71F472CCA6A810012E129 /* WFAVUniPlugin.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B7A71F402CCA65ED0012E129 /* WFAVUniPlugin.framework */; };
		B7A71F492CCA6A810012E129 /* WFClientUniPlugin.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B7A71F3A2CCA65DD0012E129 /* WFClientUniPlugin.framework */; };
		B7A71F532CCA6B0F0012E129 /* Bugly.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B7A71F4B2CCA6B0F0012E129 /* Bugly.framework */; };
		B7A71F552CCA6B1B0012E129 /* WebRTC.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = B7A71F4D2CCA6B0F0012E129 /* WebRTC.xcframework */; };
		B7A71F562CCA6B1B0012E129 /* WebRTC.xcframework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = B7A71F4D2CCA6B0F0012E129 /* WebRTC.xcframework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		B7A71F582CCA6B1D0012E129 /* WFAVEngineKit.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = B7A71F4E2CCA6B0F0012E129 /* WFAVEngineKit.xcframework */; };
		B7A71F592CCA6B1D0012E129 /* WFAVEngineKit.xcframework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = B7A71F4E2CCA6B0F0012E129 /* WFAVEngineKit.xcframework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		B7A71F5A2CCA6B1F0012E129 /* WFChatClient.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = B7A71F4F2CCA6B0F0012E129 /* WFChatClient.xcframework */; };
		B7A71F5B2CCA6B1F0012E129 /* WFChatClient.xcframework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = B7A71F4F2CCA6B0F0012E129 /* WFChatClient.xcframework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		B7A71F5C2CCA6B770012E129 /* PttClient.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = B7A71F4C2CCA6B0F0012E129 /* PttClient.xcframework */; };
		B7A71F5D2CCA6B770012E129 /* PttClient.xcframework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = B7A71F4C2CCA6B0F0012E129 /* PttClient.xcframework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		B7A71F642CCA6CC90012E129 /* WFPttClientUniPlugin.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B7A71F632CCA6CBA0012E129 /* WFPttClientUniPlugin.framework */; };
		B7A71F652CCA6CC90012E129 /* WFPttClientUniPlugin.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = B7A71F632CCA6CBA0012E129 /* WFPttClientUniPlugin.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		B7A71F392CCA65DD0012E129 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = B7A71F352CCA65DD0012E129 /* WFClientUniPlugin.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 9759EDBE245048A40076CC51;
			remoteInfo = WFClientUniPlugin;
		};
		B7A71F3F2CCA65ED0012E129 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = B7A71F3B2CCA65ED0012E129 /* WFAVUniPlugin.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = B72583622B07139800C021C0;
			remoteInfo = WFAVUniPlugin;
		};
		B7A71F622CCA6CBA0012E129 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = B7A71F5E2CCA6CBA0012E129 /* WFPttClientUniPlugin.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = B798590F2AE6957300169D79;
			remoteInfo = WFPttClientUniPlugin;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		B7A71F572CCA6B1B0012E129 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				B7A71F592CCA6B1D0012E129 /* WFAVEngineKit.xcframework in Embed Frameworks */,
				B7A71F562CCA6B1B0012E129 /* WebRTC.xcframework in Embed Frameworks */,
				B7A71F5B2CCA6B1F0012E129 /* WFChatClient.xcframework in Embed Frameworks */,
				B7A71F5D2CCA6B770012E129 /* PttClient.xcframework in Embed Frameworks */,
				B7A71F652CCA6CC90012E129 /* WFPttClientUniPlugin.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		240905021C200AEF0070786F /* CoreBluetooth.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreBluetooth.framework; path = System/Library/Frameworks/CoreBluetooth.framework; sourceTree = SDKROOT; };
		242725CA1D2666ED00EBD79E /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		242725CC1D26686700EBD79E /* CoreMotion.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMotion.framework; path = System/Library/Frameworks/CoreMotion.framework; sourceTree = SDKROOT; };
		2430A6FF1E11603E00D3D42D /* libcrypto.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libcrypto.a; path = ../SDK/Libs/libcrypto.a; sourceTree = "<group>"; };
		2430A7001E11603E00D3D42D /* libssl.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libssl.a; path = ../SDK/Libs/libssl.a; sourceTree = "<group>"; };
		2447371A1D0830BB00D0F08F /* WebKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WebKit.framework; path = System/Library/Frameworks/WebKit.framework; sourceTree = SDKROOT; };
		247F85DA1FA32B2C006ECAC6 /* liblibPDRCore.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibPDRCore.a; path = ../SDK/Libs/liblibPDRCore.a; sourceTree = "<group>"; };
		24A7515E1D9CCCC600C8B0F9 /* QuickLook.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuickLook.framework; path = System/Library/Frameworks/QuickLook.framework; sourceTree = SDKROOT; };
		24AFD7FE1CB50C3200C0F062 /* TencentOpenAPI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = TencentOpenAPI.framework; path = ../SDK/Libs/TencentOpenAPI.framework; sourceTree = "<group>"; };
		24AFD8031CB50C3F00C0F062 /* libalixpayment.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libalixpayment.a; path = ../SDK/Libs/libalixpayment.a; sourceTree = "<group>"; };
		24AFD8061CB50C3F00C0F062 /* libBaiduMobStatForSDK.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libBaiduMobStatForSDK.a; path = ../SDK/Libs/libBaiduMobStatForSDK.a; sourceTree = "<group>"; };
		24AFD8091CB50C3F00C0F062 /* libcoreSupport.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libcoreSupport.a; path = ../SDK/Libs/libcoreSupport.a; sourceTree = "<group>"; };
		24AFD80A1CB50C3F00C0F062 /* libGeTuiPush.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libGeTuiPush.a; path = ../SDK/Libs/libGeTuiPush.a; sourceTree = "<group>"; };
		24AFD80C1CB50C3F00C0F062 /* libIAPPay.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libIAPPay.a; path = ../SDK/Libs/libIAPPay.a; sourceTree = "<group>"; };
		24AFD80F1CB50C3F00C0F062 /* liblibAccelerometer.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibAccelerometer.a; path = ../SDK/Libs/liblibAccelerometer.a; sourceTree = "<group>"; };
		24AFD8101CB50C3F00C0F062 /* liblibBarcode.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibBarcode.a; path = ../SDK/Libs/liblibBarcode.a; sourceTree = "<group>"; };
		24AFD8111CB50C3F00C0F062 /* liblibCache.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibCache.a; path = ../SDK/Libs/liblibCache.a; sourceTree = "<group>"; };
		24AFD8121CB50C3F00C0F062 /* liblibCamera.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibCamera.a; path = ../SDK/Libs/liblibCamera.a; sourceTree = "<group>"; };
		24AFD8131CB50C3F00C0F062 /* liblibContacts.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibContacts.a; path = ../SDK/Libs/liblibContacts.a; sourceTree = "<group>"; };
		24AFD8151CB50C4000C0F062 /* liblibIO.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibIO.a; path = ../SDK/Libs/liblibIO.a; sourceTree = "<group>"; };
		24AFD8161CB50C4000C0F062 /* liblibLog.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibLog.a; path = ../SDK/Libs/liblibLog.a; sourceTree = "<group>"; };
		24AFD8171CB50C4000C0F062 /* liblibMap.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibMap.a; path = ../SDK/Libs/liblibMap.a; sourceTree = "<group>"; };
		24AFD8181CB50C4000C0F062 /* liblibMedia.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibMedia.a; path = ../SDK/Libs/liblibMedia.a; sourceTree = "<group>"; };
		24AFD8191CB50C4000C0F062 /* liblibMessage.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibMessage.a; path = ../SDK/Libs/liblibMessage.a; sourceTree = "<group>"; };
		24AFD81A1CB50C4000C0F062 /* liblibNativeObj.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibNativeObj.a; path = ../SDK/Libs/liblibNativeObj.a; sourceTree = "<group>"; };
		24AFD81B1CB50C4000C0F062 /* liblibNativeUI.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibNativeUI.a; path = ../SDK/Libs/liblibNativeUI.a; sourceTree = "<group>"; };
		24AFD81C1CB50C4000C0F062 /* liblibNavigator.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibNavigator.a; path = ../SDK/Libs/liblibNavigator.a; sourceTree = "<group>"; };
		24AFD81D1CB50C4000C0F062 /* liblibOauth.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibOauth.a; path = ../SDK/Libs/liblibOauth.a; sourceTree = "<group>"; };
		24AFD81E1CB50C4000C0F062 /* liblibOrientation.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibOrientation.a; path = ../SDK/Libs/liblibOrientation.a; sourceTree = "<group>"; };
		24AFD81F1CB50C4000C0F062 /* liblibPayment.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibPayment.a; path = ../SDK/Libs/liblibPayment.a; sourceTree = "<group>"; };
		24AFD8211CB50C4000C0F062 /* liblibPGInvocation.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibPGInvocation.a; path = ../SDK/Libs/liblibPGInvocation.a; sourceTree = "<group>"; };
		24AFD8221CB50C4000C0F062 /* liblibPGProximity.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibPGProximity.a; path = ../SDK/Libs/liblibPGProximity.a; sourceTree = "<group>"; };
		24AFD8231CB50C4000C0F062 /* liblibPush.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibPush.a; path = ../SDK/Libs/liblibPush.a; sourceTree = "<group>"; };
		24AFD8241CB50C4000C0F062 /* liblibShare.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibShare.a; path = ../SDK/Libs/liblibShare.a; sourceTree = "<group>"; };
		24AFD8251CB50C4000C0F062 /* liblibSpeech.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibSpeech.a; path = ../SDK/Libs/liblibSpeech.a; sourceTree = "<group>"; };
		24AFD8261CB50C4000C0F062 /* liblibStatistic.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibStatistic.a; path = ../SDK/Libs/liblibStatistic.a; sourceTree = "<group>"; };
		24AFD8271CB50C4000C0F062 /* liblibStorage.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibStorage.a; path = ../SDK/Libs/liblibStorage.a; sourceTree = "<group>"; };
		24AFD8281CB50C4000C0F062 /* liblibUI.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibUI.a; path = ../SDK/Libs/liblibUI.a; sourceTree = "<group>"; };
		24AFD82B1CB50C4000C0F062 /* liblibXHR.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibXHR.a; path = ../SDK/Libs/liblibXHR.a; sourceTree = "<group>"; };
		24AFD82C1CB50C4000C0F062 /* liblibZip.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibZip.a; path = ../SDK/Libs/liblibZip.a; sourceTree = "<group>"; };
		24AFD82E1CB50C4000C0F062 /* libopencore-amrnb.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = "libopencore-amrnb.a"; path = "../SDK/Libs/libopencore-amrnb.a"; sourceTree = "<group>"; };
		24AFD8321CB50C4000C0F062 /* libQQOauth.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libQQOauth.a; path = ../SDK/Libs/libQQOauth.a; sourceTree = "<group>"; };
		24AFD8331CB50C4000C0F062 /* libQQShare.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libQQShare.a; path = ../SDK/Libs/libQQShare.a; sourceTree = "<group>"; };
		24AFD8341CB50C4000C0F062 /* libSDWebImage.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libSDWebImage.a; path = ../SDK/Libs/libSDWebImage.a; sourceTree = "<group>"; };
		24AFD8351CB50C4000C0F062 /* libSinaShare.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libSinaShare.a; path = ../SDK/Libs/libSinaShare.a; sourceTree = "<group>"; };
		24AFD8361CB50C4000C0F062 /* libSinaWBOauth.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libSinaWBOauth.a; path = ../SDK/Libs/libSinaWBOauth.a; sourceTree = "<group>"; };
		24AFD8391CB50C4000C0F062 /* libTouchJSON.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libTouchJSON.a; path = ../SDK/Libs/libTouchJSON.a; sourceTree = "<group>"; };
		24AFD83C1CB50C4000C0F062 /* libWeiboSDK.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libWeiboSDK.a; path = ../SDK/Libs/libWeiboSDK.a; sourceTree = "<group>"; };
		24AFD83D1CB50C4000C0F062 /* libweixinShare.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libweixinShare.a; path = ../SDK/Libs/libweixinShare.a; sourceTree = "<group>"; };
		24AFD83E1CB50C4000C0F062 /* libWXOauth.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libWXOauth.a; path = ../SDK/Libs/libWXOauth.a; sourceTree = "<group>"; };
		24AFD83F1CB50C4000C0F062 /* libwxpay.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libwxpay.a; path = ../SDK/Libs/libwxpay.a; sourceTree = "<group>"; };
		24BD5AE71C99491D00B05AA2 /* libsqlite3.0.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libsqlite3.0.tbd; path = usr/lib/libsqlite3.0.tbd; sourceTree = SDKROOT; };
		24BD5AE91C99492A00B05AA2 /* libiconv.2.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libiconv.2.tbd; path = usr/lib/libiconv.2.tbd; sourceTree = SDKROOT; };
		24BD5AED1C99494200B05AA2 /* libxml2.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libxml2.tbd; path = usr/lib/libxml2.tbd; sourceTree = SDKROOT; };
		24BD5AEF1C99494A00B05AA2 /* libz.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libz.tbd; path = usr/lib/libz.tbd; sourceTree = SDKROOT; };
		24BD5AF11C994A1700B05AA2 /* libicucore.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libicucore.tbd; path = usr/lib/libicucore.tbd; sourceTree = SDKROOT; };
		24BD5AF51C994BB200B05AA2 /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		24F990121DFBDA3300848C2B /* CoreData.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreData.framework; path = System/Library/Frameworks/CoreData.framework; sourceTree = SDKROOT; };
		2F0BA3F8215B48A700F67004 /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = ../HBuilder/Images.xcassets; sourceTree = "<group>"; };
		2F0BA40D215B6E6800F67004 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		2F0BA40F215B784000F67004 /* Contacts.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Contacts.framework; path = System/Library/Frameworks/Contacts.framework; sourceTree = SDKROOT; };
		2F0BA411215B814000F67004 /* liblibVideo.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibVideo.a; path = ../SDK/Libs/liblibVideo.a; sourceTree = "<group>"; };
		2F0BA415215B8B1300F67004 /* VideoToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = VideoToolbox.framework; path = System/Library/Frameworks/VideoToolbox.framework; sourceTree = SDKROOT; };
		2F0BA417215B8B5C00F67004 /* libbz2.1.0.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libbz2.1.0.tbd; path = usr/lib/libbz2.1.0.tbd; sourceTree = SDKROOT; };
		2F0BA4DB215BA12300F67004 /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = LaunchScreen.storyboard; sourceTree = "<group>"; };
		2F434B052C787ED70020481E /* AlipaySDK.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = AlipaySDK.xcframework; path = ../SDK/Libs/AlipaySDK.xcframework; sourceTree = "<group>"; };
		2F4864C1293E10EE00142360 /* DCUniRecord.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = DCUniRecord.framework; path = ../SDK/Libs/DCUniRecord.framework; sourceTree = "<group>"; };
		2F5FAE672865DB7800430AFA /* BMKLocationKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = BMKLocationKit.framework; path = ../SDK/Libs/BMKLocationKit.framework; sourceTree = "<group>"; };
		2F5FAE682865DB7800430AFA /* KSCrash.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = KSCrash.framework; path = ../SDK/Libs/KSCrash.framework; sourceTree = "<group>"; };
		2F9FE7E92BD8E834001137CC /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		2FBB55F429530500002214BF /* liblibWeex.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibWeex.a; path = ../SDK/Libs/liblibWeex.a; sourceTree = "<group>"; };
		2FD11BBA215C79C5000A23AD /* liblibAdSupport.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibAdSupport.a; path = ../SDK/Libs/liblibAdSupport.a; sourceTree = "<group>"; };
		2FDE6A36296D742B004C7701 /* GTSDK.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = GTSDK.xcframework; path = ../SDK/Libs/GTSDK.xcframework; sourceTree = "<group>"; };
		2FDE6A38296D74B3004C7701 /* HBuilder-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "HBuilder-Bridging-Header.h"; sourceTree = "<group>"; };
		2FDE6A39296D74B3004C7701 /* DCloud.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DCloud.swift; sourceTree = "<group>"; };
		4F48902024656F73003B56F0 /* mapapi.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = mapapi.bundle; sourceTree = "<group>"; };
		4F48902124656F73003B56F0 /* __uniappes6.js */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.javascript; path = __uniappes6.js; sourceTree = "<group>"; };
		4F48902224656F73003B56F0 /* MiPassport.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = MiPassport.bundle; sourceTree = "<group>"; };
		4F48902324656F73003B56F0 /* AlipaySDK.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = AlipaySDK.bundle; sourceTree = "<group>"; };
		4F48902424656F73003B56F0 /* uni-jsframework.js */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.javascript; path = "uni-jsframework.js"; sourceTree = "<group>"; };
		4F48902524656F73003B56F0 /* WeiboSDK.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = WeiboSDK.bundle; sourceTree = "<group>"; };
		4F48902824656F73003B56F0 /* bds_license.dat */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = bds_license.dat; sourceTree = "<group>"; };
		4F48902924656F73003B56F0 /* bds_easr_basic_model.dat */ = {isa = PBXFileReference; lastKnownFileType = file; path = bds_easr_basic_model.dat; sourceTree = "<group>"; };
		4F48902A24656F73003B56F0 /* bds_easr_mfe_cmvn.dat */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = bds_easr_mfe_cmvn.dat; sourceTree = "<group>"; };
		4F48902B24656F73003B56F0 /* temp_license_2018-02-24.dat */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = "temp_license_2018-02-24.dat"; sourceTree = "<group>"; };
		4F48902C24656F73003B56F0 /* bds_easr_mfe_dnn.dat */ = {isa = PBXFileReference; lastKnownFileType = file; path = bds_easr_mfe_dnn.dat; sourceTree = "<group>"; };
		4F48902D24656F73003B56F0 /* bds_easr_gramm.dat */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = bds_easr_gramm.dat; sourceTree = "<group>"; };
		4F48902E24656F73003B56F0 /* bds_easr_wakeup_words.dat */ = {isa = PBXFileReference; lastKnownFileType = file; path = bds_easr_wakeup_words.dat; sourceTree = "<group>"; };
		4F48902F24656F73003B56F0 /* bds_easr_dnn_wakeup_model.dat */ = {isa = PBXFileReference; lastKnownFileType = file; path = bds_easr_dnn_wakeup_model.dat; sourceTree = "<group>"; };
		4F48903024656F73003B56F0 /* DCTZImagePickerController.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = DCTZImagePickerController.bundle; sourceTree = "<group>"; };
		4F48903124656F73003B56F0 /* PandoraApi.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = PandoraApi.bundle; sourceTree = "<group>"; };
		4F48903224656F73003B56F0 /* unincomponents.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = unincomponents.ttf; sourceTree = "<group>"; };
		4F48903324656F73003B56F0 /* QHADVideoPlayer.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = QHADVideoPlayer.bundle; sourceTree = "<group>"; };
		4F48903424656F73003B56F0 /* AMap.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = AMap.bundle; sourceTree = "<group>"; };
		4F48903524656F73003B56F0 /* uni-jsframework-dev.js */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.javascript; path = "uni-jsframework-dev.js"; sourceTree = "<group>"; };
		4F48903624656F73003B56F0 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		4F48903724656F73003B56F0 /* qucsdkResources.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = qucsdkResources.bundle; sourceTree = "<group>"; };
		4F48903824656F73003B56F0 /* BUAdSDK.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = BUAdSDK.bundle; sourceTree = "<group>"; };
		4F48903924656F73003B56F0 /* DCPGVideo.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = DCPGVideo.bundle; sourceTree = "<group>"; };
		4F48903A24656F73003B56F0 /* DCMediaVideo.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = DCMediaVideo.bundle; sourceTree = "<group>"; };
		4F48903B24656F73003B56F0 /* DCSVProgressHUD.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = DCSVProgressHUD.bundle; sourceTree = "<group>"; };
		4F48903C24656F73003B56F0 /* weex-polyfill.js */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.javascript; path = "weex-polyfill.js"; sourceTree = "<group>"; };
		4F48903D24656F73003B56F0 /* weexUniJs.js */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.javascript; path = weexUniJs.js; sourceTree = "<group>"; };
		4F48905C24656F98003B56F0 /* PDRCoreAppWindow.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PDRCoreAppWindow.h; sourceTree = "<group>"; };
		4F48905D24656F98003B56F0 /* PDRNView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PDRNView.h; sourceTree = "<group>"; };
		4F48905E24656F98003B56F0 /* PDRCommonString.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PDRCommonString.h; sourceTree = "<group>"; };
		4F48905F24656F98003B56F0 /* PDRCoreWindowManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PDRCoreWindowManager.h; sourceTree = "<group>"; };
		4F48906024656F98003B56F0 /* PGPlugin.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PGPlugin.h; sourceTree = "<group>"; };
		4F48906124656F98003B56F0 /* H5WEEngineExport.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = H5WEEngineExport.h; sourceTree = "<group>"; };
		4F48906224656F98003B56F0 /* PDRCoreAppInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PDRCoreAppInfo.h; sourceTree = "<group>"; };
		4F48906324656F98003B56F0 /* PDRToolSystem.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PDRToolSystem.h; sourceTree = "<group>"; };
		4F48907424656F98003B56F0 /* PDRCoreAppFrame.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PDRCoreAppFrame.h; sourceTree = "<group>"; };
		4F48907624656F98003B56F0 /* MASCompositeConstraint.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MASCompositeConstraint.h; sourceTree = "<group>"; };
		4F48907724656F98003B56F0 /* MASConstraint+Private.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "MASConstraint+Private.h"; sourceTree = "<group>"; };
		4F48907824656F98003B56F0 /* MASLayoutConstraint.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MASLayoutConstraint.h; sourceTree = "<group>"; };
		4F48907924656F98003B56F0 /* NSArray+MASShorthandAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSArray+MASShorthandAdditions.h"; sourceTree = "<group>"; };
		4F48907A24656F98003B56F0 /* MASConstraintMaker.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MASConstraintMaker.h; sourceTree = "<group>"; };
		4F48907B24656F98003B56F0 /* View+MASAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "View+MASAdditions.h"; sourceTree = "<group>"; };
		4F48907C24656F98003B56F0 /* NSArray+MASAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSArray+MASAdditions.h"; sourceTree = "<group>"; };
		4F48907D24656F98003B56F0 /* MASUtilities.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MASUtilities.h; sourceTree = "<group>"; };
		4F48907E24656F98003B56F0 /* MASViewAttribute.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MASViewAttribute.h; sourceTree = "<group>"; };
		4F48907F24656F98003B56F0 /* MASViewConstraint.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MASViewConstraint.h; sourceTree = "<group>"; };
		4F48908024656F98003B56F0 /* MASConstraint.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MASConstraint.h; sourceTree = "<group>"; };
		4F48908124656F98003B56F0 /* NSLayoutConstraint+MASDebugAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSLayoutConstraint+MASDebugAdditions.h"; sourceTree = "<group>"; };
		4F48908224656F98003B56F0 /* View+MASShorthandAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "View+MASShorthandAdditions.h"; sourceTree = "<group>"; };
		4F48908324656F98003B56F0 /* Masonry.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Masonry.h; sourceTree = "<group>"; };
		4F48908424656F98003B56F0 /* ViewController+MASAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "ViewController+MASAdditions.h"; sourceTree = "<group>"; };
		4F48908A24656F98003B56F0 /* PGMethod.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PGMethod.h; sourceTree = "<group>"; };
		4F48908B24656F98003B56F0 /* PDRCore.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PDRCore.h; sourceTree = "<group>"; };
		4F48908C24656F98003B56F0 /* PGObject.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PGObject.h; sourceTree = "<group>"; };
		4F48908D24656F98003B56F0 /* PDRToolSystemEx.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PDRToolSystemEx.h; sourceTree = "<group>"; };
		4F48908E24656F98003B56F0 /* PDRCoreAppManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PDRCoreAppManager.h; sourceTree = "<group>"; };
		4F48908F24656F98003B56F0 /* PDRCoreSettings.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PDRCoreSettings.h; sourceTree = "<group>"; };
		4F48909024656F98003B56F0 /* H5CoreScreenEdgePan.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = H5CoreScreenEdgePan.h; sourceTree = "<group>"; };
		4F48909124656F98003B56F0 /* H5UniversalApp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = H5UniversalApp.h; sourceTree = "<group>"; };
		4F48909324656F98003B56F0 /* H5CoreOverrideResourceOptions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = H5CoreOverrideResourceOptions.h; sourceTree = "<group>"; };
		4F48909524656F98003B56F0 /* WXModuleProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXModuleProtocol.h; sourceTree = "<group>"; };
		4F48909624656F98003B56F0 /* WXResourceLoader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXResourceLoader.h; sourceTree = "<group>"; };
		4F48909724656F98003B56F0 /* WXApmForInstance.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXApmForInstance.h; sourceTree = "<group>"; };
		4F48909824656F98003B56F0 /* WXWebSocketHandler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXWebSocketHandler.h; sourceTree = "<group>"; };
		4F48909924656F98003B56F0 /* WXImgLoaderProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXImgLoaderProtocol.h; sourceTree = "<group>"; };
		4F48909A24656F98003B56F0 /* WXComponentManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXComponentManager.h; sourceTree = "<group>"; };
		4F48909B24656F98003B56F0 /* WXScrollerProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXScrollerProtocol.h; sourceTree = "<group>"; };
		4F48909C24656F98003B56F0 /* WXRichText.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXRichText.h; sourceTree = "<group>"; };
		4F48909D24656F98003B56F0 /* WXView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXView.h; sourceTree = "<group>"; };
		4F48909E24656F98003B56F0 /* WXValidateProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXValidateProtocol.h; sourceTree = "<group>"; };
		4F48909F24656F98003B56F0 /* WXDefine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXDefine.h; sourceTree = "<group>"; };
		4F4890A024656F98003B56F0 /* JSContext+Weex.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "JSContext+Weex.h"; sourceTree = "<group>"; };
		4F4890A124656F98003B56F0 /* WXExtendCallNativeProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXExtendCallNativeProtocol.h; sourceTree = "<group>"; };
		4F4890A224656F98003B56F0 /* WXSDKEngine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXSDKEngine.h; sourceTree = "<group>"; };
		4F4890A324656F98003B56F0 /* style.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = style.h; sourceTree = "<group>"; };
		4F4890A424656F98003B56F0 /* WXResourceRequestHandler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXResourceRequestHandler.h; sourceTree = "<group>"; };
		4F4890A524656F98003B56F0 /* WXDisplayLinkManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXDisplayLinkManager.h; sourceTree = "<group>"; };
		4F4890A624656F98003B56F0 /* WXUtility.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXUtility.h; sourceTree = "<group>"; };
		4F4890A724656F98003B56F0 /* WXResourceRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXResourceRequest.h; sourceTree = "<group>"; };
		4F4890A824656F98003B56F0 /* WXConvert.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXConvert.h; sourceTree = "<group>"; };
		4F4890A924656F98003B56F0 /* WXPageEventNotifyEvent.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXPageEventNotifyEvent.h; sourceTree = "<group>"; };
		4F4890AA24656F98003B56F0 /* WXSDKInstance.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXSDKInstance.h; sourceTree = "<group>"; };
		4F4890AB24656F98003B56F0 /* WXSDKManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXSDKManager.h; sourceTree = "<group>"; };
		4F4890AC24656F98003B56F0 /* WXDebugTool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXDebugTool.h; sourceTree = "<group>"; };
		4F4890AD24656F98003B56F0 /* WXType.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXType.h; sourceTree = "<group>"; };
		4F4890AE24656F98003B56F0 /* WXApmProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXApmProtocol.h; sourceTree = "<group>"; };
		4F4890AF24656F98003B56F0 /* WXJSFrameworkLoadProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXJSFrameworkLoadProtocol.h; sourceTree = "<group>"; };
		4F4890B024656F98003B56F0 /* WXExceptionUtils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXExceptionUtils.h; sourceTree = "<group>"; };
		4F4890B124656F98003B56F0 /* WXIndicatorComponent.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXIndicatorComponent.h; sourceTree = "<group>"; };
		4F4890B224656F98003B56F0 /* WXErrorView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXErrorView.h; sourceTree = "<group>"; };
		4F4890B324656F98003B56F0 /* NSObject+WXSwizzle.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSObject+WXSwizzle.h"; sourceTree = "<group>"; };
		4F4890B424656F98003B56F0 /* WXMonitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXMonitor.h; sourceTree = "<group>"; };
		4F4890B524656F98003B56F0 /* WXSDKInstance+DCExtend.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "WXSDKInstance+DCExtend.h"; sourceTree = "<group>"; };
		4F4890B624656F98003B56F0 /* WXRecyclerComponent.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXRecyclerComponent.h; sourceTree = "<group>"; };
		4F4890B724656F98003B56F0 /* WXLog.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXLog.h; sourceTree = "<group>"; };
		4F4890B824656F98003B56F0 /* WXScrollerComponent.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXScrollerComponent.h; sourceTree = "<group>"; };
		4F4890B924656F98003B56F0 /* WXNetworkProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXNetworkProtocol.h; sourceTree = "<group>"; };
		4F4890BA24656F98003B56F0 /* WXTracingManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXTracingManager.h; sourceTree = "<group>"; };
		4F4890BB24656F98003B56F0 /* WXComponent.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXComponent.h; sourceTree = "<group>"; };
		4F4890BC24656F98003B56F0 /* WXPrerenderManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXPrerenderManager.h; sourceTree = "<group>"; };
		4F4890BD24656F98003B56F0 /* WXComponent+Layout.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "WXComponent+Layout.h"; sourceTree = "<group>"; };
		4F4890BE24656F98003B56F0 /* layout.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = layout.h; sourceTree = "<group>"; };
		4F4890BF24656F98003B56F0 /* WXSDKInstance+Bridge.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "WXSDKInstance+Bridge.h"; sourceTree = "<group>"; };
		4F4890C024656F98003B56F0 /* UniPluginProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UniPluginProtocol.h; sourceTree = "<group>"; };
		4F4890C124656F98003B56F0 /* WXModalUIModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXModalUIModule.h; sourceTree = "<group>"; };
		4F4890C224656F98003B56F0 /* WXAnalyzerCenter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXAnalyzerCenter.h; sourceTree = "<group>"; };
		4F4890C324656F98003B56F0 /* WXVoiceOverModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXVoiceOverModule.h; sourceTree = "<group>"; };
		4F4890C424656F98003B56F0 /* WXRootView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXRootView.h; sourceTree = "<group>"; };
		4F4890C524656F98003B56F0 /* WXAppConfiguration.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXAppConfiguration.h; sourceTree = "<group>"; };
		4F4890C624656F98003B56F0 /* WXListComponent.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXListComponent.h; sourceTree = "<group>"; };
		4F4890C724656F98003B56F0 /* WXSDKError.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXSDKError.h; sourceTree = "<group>"; };
		4F4890C824656F98003B56F0 /* WXRootViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXRootViewController.h; sourceTree = "<group>"; };
		4F4890C924656F98003B56F0 /* WXNavigationProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXNavigationProtocol.h; sourceTree = "<group>"; };
		4F4890CA24656F98003B56F0 /* WXAnalyzerProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXAnalyzerProtocol.h; sourceTree = "<group>"; };
		4F4890CB24656F98003B56F0 /* WeexSDK.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WeexSDK.h; sourceTree = "<group>"; };
		4F4890CC24656F98003B56F0 /* WXBridgeManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXBridgeManager.h; sourceTree = "<group>"; };
		4F4890CD24656F98003B56F0 /* WXResourceResponse.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXResourceResponse.h; sourceTree = "<group>"; };
		4F4890CE24656F98003B56F0 /* WXBaseViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXBaseViewController.h; sourceTree = "<group>"; };
		4F4890CF24656F98003B56F0 /* WXAppMonitorProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXAppMonitorProtocol.h; sourceTree = "<group>"; };
		4F4890D024656F98003B56F0 /* WeexProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WeexProtocol.h; sourceTree = "<group>"; };
		4F4890D124656F98003B56F0 /* WXJSExceptionProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXJSExceptionProtocol.h; sourceTree = "<group>"; };
		4F4890D224656F98003B56F0 /* WXJSExceptionInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXJSExceptionInfo.h; sourceTree = "<group>"; };
		4F4890D324656F98003B56F0 /* WXTracingProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXTracingProtocol.h; sourceTree = "<group>"; };
		4F4890D424656F98003B56F0 /* flex_enum.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = flex_enum.h; sourceTree = "<group>"; };
		4F4890D524656F98003B56F0 /* WXConfigCenterProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXConfigCenterProtocol.h; sourceTree = "<group>"; };
		4F4890D624656F98003B56F0 /* WXEventModuleProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXEventModuleProtocol.h; sourceTree = "<group>"; };
		4F4890D724656F98003B56F0 /* WXURLRewriteProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXURLRewriteProtocol.h; sourceTree = "<group>"; };
		4F4890D824656F98003B56F0 /* WXAComponent.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXAComponent.h; sourceTree = "<group>"; };
		4F4890D924656F98003B56F0 /* WXBridgeProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXBridgeProtocol.h; sourceTree = "<group>"; };
		4F4890DC24656F98003B56F0 /* SDAnimatedImageRep.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDAnimatedImageRep.h; sourceTree = "<group>"; };
		4F4890DD24656F98003B56F0 /* SDDiskCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDDiskCache.h; sourceTree = "<group>"; };
		4F4890DE24656F98003B56F0 /* SDImageIOCoder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageIOCoder.h; sourceTree = "<group>"; };
		4F4890DF24656F98003B56F0 /* NSButton+WebCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSButton+WebCache.h"; sourceTree = "<group>"; };
		4F4890E024656F98003B56F0 /* SDImageGraphics.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageGraphics.h; sourceTree = "<group>"; };
		4F4890E124656F98003B56F0 /* UIImageView+WebCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImageView+WebCache.h"; sourceTree = "<group>"; };
		4F4890E224656F98003B56F0 /* NSData+ImageContentType.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSData+ImageContentType.h"; sourceTree = "<group>"; };
		4F4890E324656F98003B56F0 /* SDImageTransformer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageTransformer.h; sourceTree = "<group>"; };
		4F4890E424656F98003B56F0 /* SDImageCachesManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageCachesManager.h; sourceTree = "<group>"; };
		4F4890E524656F98003B56F0 /* SDWebImageTransition.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageTransition.h; sourceTree = "<group>"; };
		4F4890E624656F98003B56F0 /* SDImageLoadersManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageLoadersManager.h; sourceTree = "<group>"; };
		4F4890E724656F98003B56F0 /* SDWebImageDownloaderOperation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloaderOperation.h; sourceTree = "<group>"; };
		4F4890E824656F98003B56F0 /* SDImageFrame.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageFrame.h; sourceTree = "<group>"; };
		4F4890E924656F98003B56F0 /* SDImageGIFCoder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageGIFCoder.h; sourceTree = "<group>"; };
		4F4890EA24656F98003B56F0 /* SDImageCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageCache.h; sourceTree = "<group>"; };
		4F4890EB24656F98003B56F0 /* SDWebImageDownloaderConfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloaderConfig.h; sourceTree = "<group>"; };
		4F4890EC24656F98003B56F0 /* SDImageCacheConfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageCacheConfig.h; sourceTree = "<group>"; };
		4F4890ED24656F98003B56F0 /* SDWebImageCacheKeyFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageCacheKeyFilter.h; sourceTree = "<group>"; };
		4F4890EE24656F98003B56F0 /* UIImage+MemoryCacheCost.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+MemoryCacheCost.h"; sourceTree = "<group>"; };
		4F4890EF24656F98003B56F0 /* SDImageCacheDefine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageCacheDefine.h; sourceTree = "<group>"; };
		4F4890F024656F98003B56F0 /* UIButton+WebCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIButton+WebCache.h"; sourceTree = "<group>"; };
		4F4890F124656F98003B56F0 /* SDWebImageDownloaderRequestModifier.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloaderRequestModifier.h; sourceTree = "<group>"; };
		4F4890F224656F98003B56F0 /* UIImage+Metadata.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+Metadata.h"; sourceTree = "<group>"; };
		4F4890F324656F98003B56F0 /* SDWebImageOptionsProcessor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageOptionsProcessor.h; sourceTree = "<group>"; };
		4F4890F424656F98003B56F0 /* UIView+WebCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+WebCache.h"; sourceTree = "<group>"; };
		4F4890F524656F98003B56F0 /* UIView+WebCacheOperation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+WebCacheOperation.h"; sourceTree = "<group>"; };
		4F4890F624656F98003B56F0 /* SDWebImageDefine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageDefine.h; sourceTree = "<group>"; };
		4F4890F724656F98003B56F0 /* SDImageCoder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageCoder.h; sourceTree = "<group>"; };
		4F4890F824656F98003B56F0 /* SDAnimatedImageView+WebCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SDAnimatedImageView+WebCache.h"; sourceTree = "<group>"; };
		4F4890F924656F98003B56F0 /* NSImage+Compatibility.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSImage+Compatibility.h"; sourceTree = "<group>"; };
		4F4890FA24656F98003B56F0 /* SDAnimatedImageView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDAnimatedImageView.h; sourceTree = "<group>"; };
		4F4890FB24656F98003B56F0 /* SDAnimatedImage.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDAnimatedImage.h; sourceTree = "<group>"; };
		4F4890FC24656F98003B56F0 /* UIImageView+HighlightedWebCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImageView+HighlightedWebCache.h"; sourceTree = "<group>"; };
		4F4890FD24656F98003B56F0 /* SDWebImageManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageManager.h; sourceTree = "<group>"; };
		4F4890FE24656F98003B56F0 /* SDWebImageOperation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageOperation.h; sourceTree = "<group>"; };
		4F4890FF24656F98003B56F0 /* SDWebImageIndicator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageIndicator.h; sourceTree = "<group>"; };
		4F48910024656F98003B56F0 /* SDWebImageCacheSerializer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageCacheSerializer.h; sourceTree = "<group>"; };
		4F48910124656F98003B56F0 /* SDImageLoader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageLoader.h; sourceTree = "<group>"; };
		4F48910224656F98003B56F0 /* SDWebImageDownloader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloader.h; sourceTree = "<group>"; };
		4F48910324656F98003B56F0 /* UIImage+Transform.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+Transform.h"; sourceTree = "<group>"; };
		4F48910424656F98003B56F0 /* UIImage+ForceDecode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+ForceDecode.h"; sourceTree = "<group>"; };
		4F48910524656F98003B56F0 /* SDImageCoderHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageCoderHelper.h; sourceTree = "<group>"; };
		4F48910624656F98003B56F0 /* SDWebImagePrefetcher.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImagePrefetcher.h; sourceTree = "<group>"; };
		4F48910724656F98003B56F0 /* SDImageAPNGCoder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageAPNGCoder.h; sourceTree = "<group>"; };
		4F48910824656F98003B56F0 /* SDWebImageError.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageError.h; sourceTree = "<group>"; };
		4F48910924656F98003B56F0 /* SDWebImageCompat.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageCompat.h; sourceTree = "<group>"; };
		4F48910A24656F98003B56F0 /* SDMemoryCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDMemoryCache.h; sourceTree = "<group>"; };
		4F48910B24656F98003B56F0 /* UIImage+MultiFormat.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+MultiFormat.h"; sourceTree = "<group>"; };
		4F48910C24656F98003B56F0 /* SDImageCodersManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageCodersManager.h; sourceTree = "<group>"; };
		4F48910D24656F98003B56F0 /* UIImage+GIF.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+GIF.h"; sourceTree = "<group>"; };
		4F48910F24656F98003B56F0 /* SDImageWebPCoder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageWebPCoder.h; sourceTree = "<group>"; };
		4F48911024656F98003B56F0 /* UIImage+WebP.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+WebP.h"; sourceTree = "<group>"; };
		4F48911224656F98003B56F0 /* SDWeakProxy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWeakProxy.h; sourceTree = "<group>"; };
		4F48911324656F98003B56F0 /* SDInternalMacros.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDInternalMacros.h; sourceTree = "<group>"; };
		4F48911424656F98003B56F0 /* NSBezierPath+RoundedCorners.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSBezierPath+RoundedCorners.h"; sourceTree = "<group>"; };
		4F48911524656F98003B56F0 /* SDImageAPNGCoderInternal.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageAPNGCoderInternal.h; sourceTree = "<group>"; };
		4F48911624656F98003B56F0 /* SDAsyncBlockOperation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDAsyncBlockOperation.h; sourceTree = "<group>"; };
		4F48911724656F98003B56F0 /* SDImageGIFCoderInternal.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageGIFCoderInternal.h; sourceTree = "<group>"; };
		4F48911824656F98003B56F0 /* UIColor+HexString.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIColor+HexString.h"; sourceTree = "<group>"; };
		4F48911924656F98003B56F0 /* SDmetamacros.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDmetamacros.h; sourceTree = "<group>"; };
		4F48911A24656F98003B56F0 /* SDImageCachesManagerOperation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageCachesManagerOperation.h; sourceTree = "<group>"; };
		4F48911B24656F98003B56F0 /* SDImageAssetManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageAssetManager.h; sourceTree = "<group>"; };
		4F48911C24656F98003B56F0 /* PDRCoreDefs.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PDRCoreDefs.h; sourceTree = "<group>"; };
		4F48911D24656F98003B56F0 /* PDRCoreApp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PDRCoreApp.h; sourceTree = "<group>"; };
		4F48911E2465734F003B56F0 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		4F48911F2465734F003B56F0 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		4F4DAE6A25DE60DB0094E580 /* libUniPush.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libUniPush.a; path = ../SDK/Libs/libUniPush.a; sourceTree = "<group>"; };
		4F4DDB5825AC4F010008AE37 /* DCTZPhotoPickerController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DCTZPhotoPickerController.h; sourceTree = "<group>"; };
		4F4DDB5925AC4F010008AE37 /* DCTZImageManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DCTZImageManager.h; sourceTree = "<group>"; };
		4F4DDB5A25AC4F010008AE37 /* DCTZAssetCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DCTZAssetCell.h; sourceTree = "<group>"; };
		4F4DDB5B25AC4F010008AE37 /* DCTZAssetModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DCTZAssetModel.h; sourceTree = "<group>"; };
		4F4DDB5C25AC4F010008AE37 /* DCTZGifPhotoPreviewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DCTZGifPhotoPreviewController.h; sourceTree = "<group>"; };
		4F4DDB5D25AC4F010008AE37 /* DCTZLocationManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DCTZLocationManager.h; sourceTree = "<group>"; };
		4F4DDB5E25AC4F010008AE37 /* UIView+DCLayout.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIView+DCLayout.h"; sourceTree = "<group>"; };
		4F4DDB5F25AC4F010008AE37 /* DCTZImageCropManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DCTZImageCropManager.h; sourceTree = "<group>"; };
		4F4DDB6025AC4F010008AE37 /* DCTZImagePickerController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DCTZImagePickerController.h; sourceTree = "<group>"; };
		4F4DDB6125AC4F010008AE37 /* NSBundle+DCTZImagePicker.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSBundle+DCTZImagePicker.h"; sourceTree = "<group>"; };
		4F4DDB6225AC4F010008AE37 /* DCTZVideoPlayerController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DCTZVideoPlayerController.h; sourceTree = "<group>"; };
		4F4DDB6325AC4F010008AE37 /* DCUIViewControllerHUD.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DCUIViewControllerHUD.h; sourceTree = "<group>"; };
		4F4DDB6425AC4F010008AE37 /* DCTZPhotoPreviewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DCTZPhotoPreviewCell.h; sourceTree = "<group>"; };
		4F4DDB6525AC4F010008AE37 /* DCTZPhotoPreviewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DCTZPhotoPreviewController.h; sourceTree = "<group>"; };
		4F4DDB6625AC4F010008AE37 /* DCTZProgressView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DCTZProgressView.h; sourceTree = "<group>"; };
		4F4DDB6825AC4F010008AE37 /* DCSVIndefiniteAnimatedView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DCSVIndefiniteAnimatedView.h; sourceTree = "<group>"; };
		4F4DDB6925AC4F010008AE37 /* DCSVProgressAnimatedView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DCSVProgressAnimatedView.h; sourceTree = "<group>"; };
		4F4DDB6A25AC4F010008AE37 /* DCSVProgressHUD.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DCSVProgressHUD.h; sourceTree = "<group>"; };
		4F4DDB6B25AC4F010008AE37 /* DCSVRadialGradientLayer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DCSVRadialGradientLayer.h; sourceTree = "<group>"; };
		4F5762F4261461A000A5C0BA /* MetalKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MetalKit.framework; path = System/Library/Frameworks/MetalKit.framework; sourceTree = SDKROOT; };
		4F5762F6261461AB00A5C0BA /* GLKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = GLKit.framework; path = System/Library/Frameworks/GLKit.framework; sourceTree = SDKROOT; };
		4F8C6936265E502C00C2A73C /* Masonry.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Masonry.framework; path = ../SDK/Libs/Masonry.framework; sourceTree = "<group>"; };
		4FA53F7826E5D09500BAD6A0 /* uni-jsframework-vue3.js */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.javascript; path = "uni-jsframework-vue3.js"; sourceTree = "<group>"; };
		4FE3666B254FE70E00DCD173 /* libuchardet.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libuchardet.a; path = ../SDK/Libs/libuchardet.a; sourceTree = "<group>"; };
		67229AC9230171AE0093F29A /* libDCUniAmap.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libDCUniAmap.a; path = ../SDK/Libs/libDCUniAmap.a; sourceTree = "<group>"; };
		67229ACA230171AE0093F29A /* libDCUniVideo.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libDCUniVideo.a; path = ../SDK/Libs/libDCUniVideo.a; sourceTree = "<group>"; };
		67229ACB230171AE0093F29A /* libDCUniBarcode.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libDCUniBarcode.a; path = ../SDK/Libs/libDCUniBarcode.a; sourceTree = "<group>"; };
		67229ACD230171AE0093F29A /* libDCUniBMap.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libDCUniBMap.a; path = ../SDK/Libs/libDCUniBMap.a; sourceTree = "<group>"; };
		67229ACE230171AE0093F29A /* libDCUniGPUImage.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libDCUniGPUImage.a; path = ../SDK/Libs/libDCUniGPUImage.a; sourceTree = "<group>"; };
		67229ACF230171AE0093F29A /* libDCUniLivePush.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libDCUniLivePush.a; path = ../SDK/Libs/libDCUniLivePush.a; sourceTree = "<group>"; };
		67229AD0230171AE0093F29A /* libDCUniMap.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libDCUniMap.a; path = ../SDK/Libs/libDCUniMap.a; sourceTree = "<group>"; };
		67254219242474E20086649B /* libH5WEUIWebview.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libH5WEUIWebview.a; path = ../SDK/Libs/libH5WEUIWebview.a; sourceTree = "<group>"; };
		672CE2B222DC9118005A0D88 /* DCUniVideoPublic.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = DCUniVideoPublic.framework; path = ../SDK/Libs/DCUniVideoPublic.framework; sourceTree = "<group>"; };
		672CE2B422DC916C005A0D88 /* libDCUniZXing.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libDCUniZXing.a; path = ../SDK/Libs/libDCUniZXing.a; sourceTree = "<group>"; };
		6731F393232F4CE2007838BC /* libresolv.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libresolv.tbd; path = usr/lib/libresolv.tbd; sourceTree = SDKROOT; };
		6731F395232F4D05007838BC /* UserNotifications.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UserNotifications.framework; path = System/Library/Frameworks/UserNotifications.framework; sourceTree = SDKROOT; };
		6731F397232F4D8E007838BC /* Photos.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Photos.framework; path = System/Library/Frameworks/Photos.framework; sourceTree = SDKROOT; };
		6731F399232F4F03007838BC /* HBuilder.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = HBuilder.entitlements; path = HBuilder/HBuilder.entitlements; sourceTree = "<group>"; };
		673E6E7221E44ABF00C021FE /* liblibFingerprint.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibFingerprint.a; path = ../SDK/Libs/liblibFingerprint.a; sourceTree = "<group>"; };
		6743942923C98EB30085145E /* LaunchScreenAD.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = LaunchScreenAD.storyboard; sourceTree = "<group>"; };
		67566B1E2251DC3A00BDF218 /* liblibSqlite.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibSqlite.a; path = ../SDK/Libs/liblibSqlite.a; sourceTree = "<group>"; };
		6756AB6721F58ACC00765F52 /* liblibBeacon.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibBeacon.a; path = ../SDK/Libs/liblibBeacon.a; sourceTree = "<group>"; };
		6756AB6921F58CA300765F52 /* liblibBlueTooth.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibBlueTooth.a; path = ../SDK/Libs/liblibBlueTooth.a; sourceTree = "<group>"; };
		67AB0B3C21EEEA6B0029B229 /* libAMapImp.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libAMapImp.a; path = ../SDK/Libs/libAMapImp.a; sourceTree = "<group>"; };
		67AB0B3E21EEEAA60029B229 /* AMapSearchKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AMapSearchKit.framework; path = ../SDK/Libs/AMapSearchKit.framework; sourceTree = "<group>"; };
		67AB0B4021EEEACA0029B229 /* MAMapKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MAMapKit.framework; path = ../SDK/Libs/MAMapKit.framework; sourceTree = "<group>"; };
		67AB0B4221EEEAE50029B229 /* AMapFoundationKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AMapFoundationKit.framework; path = ../SDK/Libs/AMapFoundationKit.framework; sourceTree = "<group>"; };
		67AB0B9621EF10E60029B229 /* liblibGeolocation.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblibGeolocation.a; path = ../SDK/Libs/liblibGeolocation.a; sourceTree = "<group>"; };
		67AB0B9821EF10FE0029B229 /* libBaiduKeyVerify.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libBaiduKeyVerify.a; path = ../SDK/Libs/libBaiduKeyVerify.a; sourceTree = "<group>"; };
		67AB0B9B21EF111D0029B229 /* BaiduMapAPI_Search.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = BaiduMapAPI_Search.framework; path = ../SDK/Libs/BaiduMapAPI_Search.framework; sourceTree = "<group>"; };
		67AB0B9C21EF111D0029B229 /* BaiduMapAPI_Utils.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = BaiduMapAPI_Utils.framework; path = ../SDK/Libs/BaiduMapAPI_Utils.framework; sourceTree = "<group>"; };
		67AB0B9D21EF111D0029B229 /* BaiduMapAPI_Map.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = BaiduMapAPI_Map.framework; path = ../SDK/Libs/BaiduMapAPI_Map.framework; sourceTree = "<group>"; };
		67AB0B9E21EF111D0029B229 /* BaiduMapAPI_Base.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = BaiduMapAPI_Base.framework; path = ../SDK/Libs/BaiduMapAPI_Base.framework; sourceTree = "<group>"; };
		67B7CAA121DCE8180083E96A /* control.xml */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; path = control.xml; sourceTree = "<group>"; };
		67D6390721C12944005D1E8F /* libBaiduSpeechSDK.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libBaiduSpeechSDK.a; path = ../SDK/Libs/libBaiduSpeechSDK.a; sourceTree = "<group>"; };
		67D6390821C12944005D1E8F /* libbaiduSpeech.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libbaiduSpeech.a; path = ../SDK/Libs/libbaiduSpeech.a; sourceTree = "<group>"; };
		67E9CDCE22968D2E0076E0FB /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/Localizable.strings"; sourceTree = "<group>"; };
		67E9CDD022968D350076E0FB /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/Localizable.strings; sourceTree = "<group>"; };
		7A1967C2212536EC00B330A9 /* libmp3lame.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libmp3lame.a; path = ../SDK/Libs/libmp3lame.a; sourceTree = "<group>"; };
		7A1967C42125371000B330A9 /* storage.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = storage.framework; path = ../SDK/Libs/storage.framework; sourceTree = "<group>"; };
		7A1967C621253B5F00B330A9 /* libWeChatSDK_pay.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libWeChatSDK_pay.a; path = ../SDK/Libs/libWeChatSDK_pay.a; sourceTree = "<group>"; };
		7A49810A2126B01200D20880 /* libiconv.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libiconv.tbd; path = usr/lib/libiconv.tbd; sourceTree = SDKROOT; };
		7A49810C2126B01900D20880 /* Accelerate.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Accelerate.framework; path = System/Library/Frameworks/Accelerate.framework; sourceTree = SDKROOT; };
		7ACF69AA19FF899A007C64F1 /* CFNetwork.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CFNetwork.framework; path = System/Library/Frameworks/CFNetwork.framework; sourceTree = SDKROOT; };
		7ACF69AC19FF89B1007C64F1 /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		8E163D011A8D208500308A8B /* AssetsLibrary.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AssetsLibrary.framework; path = System/Library/Frameworks/AssetsLibrary.framework; sourceTree = SDKROOT; };
		8E6E37AB1B0E1B580036EB48 /* ImageIO.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ImageIO.framework; path = System/Library/Frameworks/ImageIO.framework; sourceTree = SDKROOT; };
		8EED6298198A1D13000A4449 /* HBuilder.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = HBuilder.app; sourceTree = BUILT_PRODUCTS_DIR; };
		8EED629B198A1D13000A4449 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		8EED629D198A1D13000A4449 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		8EED629F198A1D13000A4449 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		8EED62A3198A1D13000A4449 /* HBuilder-Hello-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "HBuilder-Hello-Info.plist"; sourceTree = "<group>"; };
		8EED62A5198A1D13000A4449 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		8EED62A7198A1D13000A4449 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		8EED62A9198A1D13000A4449 /* HBuilder-Hello-Prefix.pch */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "HBuilder-Hello-Prefix.pch"; sourceTree = "<group>"; };
		8EED62AA198A1D13000A4449 /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		8EED62AB198A1D13000A4449 /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		8EED62B3198A1D14000A4449 /* ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		8EED62B4198A1D14000A4449 /* ViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ViewController.m; sourceTree = "<group>"; };
		8EED6411198A2622000A4449 /* MobileCoreServices.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MobileCoreServices.framework; path = System/Library/Frameworks/MobileCoreServices.framework; sourceTree = SDKROOT; };
		8EED6413198A262C000A4449 /* CoreMedia.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMedia.framework; path = System/Library/Frameworks/CoreMedia.framework; sourceTree = SDKROOT; };
		8EED6415198A2635000A4449 /* CoreVideo.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreVideo.framework; path = System/Library/Frameworks/CoreVideo.framework; sourceTree = SDKROOT; };
		8EED641B198A2654000A4449 /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		8EED641D198A265F000A4449 /* CoreTelephony.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreTelephony.framework; path = System/Library/Frameworks/CoreTelephony.framework; sourceTree = SDKROOT; };
		8EED641F198A2668000A4449 /* MediaPlayer.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MediaPlayer.framework; path = System/Library/Frameworks/MediaPlayer.framework; sourceTree = SDKROOT; };
		8EED6421198A2678000A4449 /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = System/Library/Frameworks/AudioToolbox.framework; sourceTree = SDKROOT; };
		8EED658F198A3DF7000A4449 /* Pandora */ = {isa = PBXFileReference; lastKnownFileType = folder; path = Pandora; sourceTree = "<group>"; };
		8EED6591198A5737000A4449 /* AddressBook.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AddressBook.framework; path = System/Library/Frameworks/AddressBook.framework; sourceTree = SDKROOT; };
		8EED6593198A5743000A4449 /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		8EED6595198A574C000A4449 /* CoreLocation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreLocation.framework; path = System/Library/Frameworks/CoreLocation.framework; sourceTree = SDKROOT; };
		8EED6597198A5754000A4449 /* MessageUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MessageUI.framework; path = System/Library/Frameworks/MessageUI.framework; sourceTree = SDKROOT; };
		8EED659B198A5773000A4449 /* AddressBookUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AddressBookUI.framework; path = System/Library/Frameworks/AddressBookUI.framework; sourceTree = SDKROOT; };
		8EED659D198A5782000A4449 /* Social.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Social.framework; path = System/Library/Frameworks/Social.framework; sourceTree = SDKROOT; };
		8EED659F198A5789000A4449 /* Accounts.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Accounts.framework; path = System/Library/Frameworks/Accounts.framework; sourceTree = SDKROOT; };
		8EED65A5198A626B000A4449 /* MapKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MapKit.framework; path = System/Library/Frameworks/MapKit.framework; sourceTree = SDKROOT; };
		8EED65A7198A6273000A4449 /* OpenGLES.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenGLES.framework; path = System/Library/Frameworks/OpenGLES.framework; sourceTree = SDKROOT; };
		95BC64EB2754A50000CB55B9 /* UMAPM.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UMAPM.framework; path = ../SDK/Libs/UMAPM.framework; sourceTree = "<group>"; };
		B7A71F352CCA65DD0012E129 /* WFClientUniPlugin.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = WFClientUniPlugin.xcodeproj; path = WFClientUniPlugin/WFClientUniPlugin.xcodeproj; sourceTree = "<group>"; };
		B7A71F3B2CCA65ED0012E129 /* WFAVUniPlugin.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = WFAVUniPlugin.xcodeproj; path = WFAVUniPlugin/WFAVUniPlugin.xcodeproj; sourceTree = "<group>"; };
		B7A71F4B2CCA6B0F0012E129 /* Bugly.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Bugly.framework; path = WF_SDK/Bugly.framework; sourceTree = "<group>"; };
		B7A71F4C2CCA6B0F0012E129 /* PttClient.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = PttClient.xcframework; path = WF_SDK/PttClient.xcframework; sourceTree = "<group>"; };
		B7A71F4D2CCA6B0F0012E129 /* WebRTC.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = WebRTC.xcframework; path = WF_SDK/WebRTC.xcframework; sourceTree = "<group>"; };
		B7A71F4E2CCA6B0F0012E129 /* WFAVEngineKit.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = WFAVEngineKit.xcframework; path = WF_SDK/WFAVEngineKit.xcframework; sourceTree = "<group>"; };
		B7A71F4F2CCA6B0F0012E129 /* WFChatClient.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = WFChatClient.xcframework; path = WF_SDK/WFChatClient.xcframework; sourceTree = "<group>"; };
		B7A71F5E2CCA6CBA0012E129 /* WFPttClientUniPlugin.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = WFPttClientUniPlugin.xcodeproj; path = WFPttClientUniPlugin/WFPttClientUniPlugin.xcodeproj; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		8EED6295198A1D13000A4449 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2F434B062C787ED80020481E /* AlipaySDK.xcframework in Frameworks */,
				2F4864C2293E10EE00142360 /* DCUniRecord.framework in Frameworks */,
				4F8C6937265E502C00C2A73C /* Masonry.framework in Frameworks */,
				4F5762F7261461AB00A5C0BA /* GLKit.framework in Frameworks */,
				4F5762F5261461A000A5C0BA /* MetalKit.framework in Frameworks */,
				4F4DAE6B25DE60DB0094E580 /* libUniPush.a in Frameworks */,
				4FE3666C254FE70E00DCD173 /* libuchardet.a in Frameworks */,
				67A9340023A8B922004A4DF4 /* libSDWebImage.a in Frameworks */,
				6731F398232F4D8E007838BC /* Photos.framework in Frameworks */,
				6731F396232F4D05007838BC /* UserNotifications.framework in Frameworks */,
				6731F394232F4CE2007838BC /* libresolv.tbd in Frameworks */,
				672DEB2C23056152003F27CC /* libDCUniBarcode.a in Frameworks */,
				67229AD1230171AE0093F29A /* libDCUniAmap.a in Frameworks */,
				67229AD2230171AE0093F29A /* libDCUniVideo.a in Frameworks */,
				67229AD6230171AE0093F29A /* libDCUniGPUImage.a in Frameworks */,
				67229AD8230171AE0093F29A /* libDCUniMap.a in Frameworks */,
				672CE2B322DC9118005A0D88 /* DCUniVideoPublic.framework in Frameworks */,
				672CE2B522DC916C005A0D88 /* libDCUniZXing.a in Frameworks */,
				24AFD8531CB50C4000C0F062 /* liblibLog.a in Frameworks */,
				67AB0B4321EEEAE50029B229 /* AMapFoundationKit.framework in Frameworks */,
				2F0BA418215B8B5C00F67004 /* libbz2.1.0.tbd in Frameworks */,
				2FD11BBB215C79C5000A23AD /* liblibAdSupport.a in Frameworks */,
				2F0BA416215B8B1400F67004 /* VideoToolbox.framework in Frameworks */,
				24BD5AF61C994BB200B05AA2 /* libc++.tbd in Frameworks */,
				2F0BA410215B784100F67004 /* Contacts.framework in Frameworks */,
				8EED6594198A5743000A4449 /* AVFoundation.framework in Frameworks */,
				67AB0B3F21EEEAA60029B229 /* AMapSearchKit.framework in Frameworks */,
				67AB0B9721EF10E70029B229 /* liblibGeolocation.a in Frameworks */,
				7A49810D2126B01900D20880 /* Accelerate.framework in Frameworks */,
				2FDE6A37296D742B004C7701 /* GTSDK.xcframework in Frameworks */,
				67AB0B9921EF10FE0029B229 /* libBaiduKeyVerify.a in Frameworks */,
				7A49810B2126B01200D20880 /* libiconv.tbd in Frameworks */,
				24F990131DFBDA3300848C2B /* CoreData.framework in Frameworks */,
				B7A71F642CCA6CC90012E129 /* WFPttClientUniPlugin.framework in Frameworks */,
				8EED629E198A1D13000A4449 /* CoreGraphics.framework in Frameworks */,
				8EED629C198A1D13000A4449 /* Foundation.framework in Frameworks */,
				8EED62A0198A1D13000A4449 /* UIKit.framework in Frameworks */,
				24A7515F1D9CCCC600C8B0F9 /* QuickLook.framework in Frameworks */,
				242725CD1D26686700EBD79E /* CoreMotion.framework in Frameworks */,
				7ACF69AB19FF899A007C64F1 /* CFNetwork.framework in Frameworks */,
				7ACF69AD19FF89B1007C64F1 /* Security.framework in Frameworks */,
				67AB0BA221EF111D0029B229 /* BaiduMapAPI_Utils.framework in Frameworks */,
				8EED641C198A2654000A4449 /* SystemConfiguration.framework in Frameworks */,
				242725CB1D2666ED00EBD79E /* JavaScriptCore.framework in Frameworks */,
				67AB0BA121EF111D0029B229 /* BaiduMapAPI_Search.framework in Frameworks */,
				2447371B1D0830BB00D0F08F /* WebKit.framework in Frameworks */,
				24AFD8401CB50C4000C0F062 /* libalixpayment.a in Frameworks */,
				24AFD8431CB50C4000C0F062 /* libBaiduMobStatForSDK.a in Frameworks */,
				7A1967C3212536EC00B330A9 /* libmp3lame.a in Frameworks */,
				24AFD8461CB50C4000C0F062 /* libcoreSupport.a in Frameworks */,
				24AFD8491CB50C4000C0F062 /* libIAPPay.a in Frameworks */,
				67AB0BA421EF111D0029B229 /* BaiduMapAPI_Base.framework in Frameworks */,
				24AFD84C1CB50C4000C0F062 /* liblibAccelerometer.a in Frameworks */,
				67AB0B4121EEEACA0029B229 /* MAMapKit.framework in Frameworks */,
				24AFD84D1CB50C4000C0F062 /* liblibBarcode.a in Frameworks */,
				67566B1F2251DC3A00BDF218 /* liblibSqlite.a in Frameworks */,
				24AFD84E1CB50C4000C0F062 /* liblibCache.a in Frameworks */,
				B7A71F582CCA6B1D0012E129 /* WFAVEngineKit.xcframework in Frameworks */,
				24AFD84F1CB50C4000C0F062 /* liblibCamera.a in Frameworks */,
				24AFD8501CB50C4000C0F062 /* liblibContacts.a in Frameworks */,
				6756AB6821F58ACD00765F52 /* liblibBeacon.a in Frameworks */,
				24AFD8521CB50C4000C0F062 /* liblibIO.a in Frameworks */,
				B7A71F492CCA6A810012E129 /* WFClientUniPlugin.framework in Frameworks */,
				24AFD8541CB50C4000C0F062 /* liblibMap.a in Frameworks */,
				24AFD8551CB50C4000C0F062 /* liblibMedia.a in Frameworks */,
				24AFD8561CB50C4000C0F062 /* liblibMessage.a in Frameworks */,
				24AFD8571CB50C4000C0F062 /* liblibNativeObj.a in Frameworks */,
				6756AB6A21F58CA300765F52 /* liblibBlueTooth.a in Frameworks */,
				24AFD8581CB50C4000C0F062 /* liblibNativeUI.a in Frameworks */,
				B7A71F5A2CCA6B1F0012E129 /* WFChatClient.xcframework in Frameworks */,
				24AFD8591CB50C4000C0F062 /* liblibNavigator.a in Frameworks */,
				24AFD85A1CB50C4000C0F062 /* liblibOauth.a in Frameworks */,
				2430A7011E11603E00D3D42D /* libcrypto.a in Frameworks */,
				24AFD85B1CB50C4000C0F062 /* liblibOrientation.a in Frameworks */,
				7A1967C721253B5F00B330A9 /* libWeChatSDK_pay.a in Frameworks */,
				24AFD85C1CB50C4000C0F062 /* liblibPayment.a in Frameworks */,
				24AFD85E1CB50C4000C0F062 /* liblibPGInvocation.a in Frameworks */,
				24AFD85F1CB50C4000C0F062 /* liblibPGProximity.a in Frameworks */,
				24AFD8601CB50C4000C0F062 /* liblibPush.a in Frameworks */,
				B7A71F5C2CCA6B770012E129 /* PttClient.xcframework in Frameworks */,
				24AFD8611CB50C4000C0F062 /* liblibShare.a in Frameworks */,
				67D6390921C12945005D1E8F /* libBaiduSpeechSDK.a in Frameworks */,
				24AFD8621CB50C4000C0F062 /* liblibSpeech.a in Frameworks */,
				2F5FAE692865DB7800430AFA /* BMKLocationKit.framework in Frameworks */,
				24AFD8631CB50C4000C0F062 /* liblibStatistic.a in Frameworks */,
				24AFD8641CB50C4000C0F062 /* liblibStorage.a in Frameworks */,
				2FBB55F529530500002214BF /* liblibWeex.a in Frameworks */,
				24AFD8651CB50C4000C0F062 /* liblibUI.a in Frameworks */,
				24AFD8681CB50C4000C0F062 /* liblibXHR.a in Frameworks */,
				24AFD8691CB50C4000C0F062 /* liblibZip.a in Frameworks */,
				24AFD86B1CB50C4000C0F062 /* libopencore-amrnb.a in Frameworks */,
				B7A71F532CCA6B0F0012E129 /* Bugly.framework in Frameworks */,
				67AB0BA321EF111D0029B229 /* BaiduMapAPI_Map.framework in Frameworks */,
				673E6E7321E44ABF00C021FE /* liblibFingerprint.a in Frameworks */,
				24AFD86F1CB50C4000C0F062 /* libQQOauth.a in Frameworks */,
				24AFD8701CB50C4000C0F062 /* libQQShare.a in Frameworks */,
				24AFD8721CB50C4000C0F062 /* libSinaShare.a in Frameworks */,
				7A1967C52125371000B330A9 /* storage.framework in Frameworks */,
				24AFD8731CB50C4000C0F062 /* libSinaWBOauth.a in Frameworks */,
				B7A71F552CCA6B1B0012E129 /* WebRTC.xcframework in Frameworks */,
				24AFD8761CB50C4000C0F062 /* libTouchJSON.a in Frameworks */,
				24AFD8791CB50C4000C0F062 /* libWeiboSDK.a in Frameworks */,
				24AFD87A1CB50C4000C0F062 /* libweixinShare.a in Frameworks */,
				24AFD87B1CB50C4000C0F062 /* libWXOauth.a in Frameworks */,
				24AFD87C1CB50C4000C0F062 /* libwxpay.a in Frameworks */,
				247F85DB1FA32B2C006ECAC6 /* liblibPDRCore.a in Frameworks */,
				24AFD8021CB50C3200C0F062 /* TencentOpenAPI.framework in Frameworks */,
				24BD5AF21C994A1700B05AA2 /* libicucore.tbd in Frameworks */,
				24BD5AF01C99494A00B05AA2 /* libz.tbd in Frameworks */,
				24BD5AEE1C99494200B05AA2 /* libxml2.tbd in Frameworks */,
				67D6390A21C12945005D1E8F /* libbaiduSpeech.a in Frameworks */,
				2F5FAE6A2865DB7800430AFA /* KSCrash.framework in Frameworks */,
				2F0BA412215B814100F67004 /* liblibVideo.a in Frameworks */,
				24BD5AEA1C99492A00B05AA2 /* libiconv.2.tbd in Frameworks */,
				24BD5AE81C99491D00B05AA2 /* libsqlite3.0.tbd in Frameworks */,
				2430A7021E11603E00D3D42D /* libssl.a in Frameworks */,
				240905031C200AEF0070786F /* CoreBluetooth.framework in Frameworks */,
				8E6E37AC1B0E1B580036EB48 /* ImageIO.framework in Frameworks */,
				8E163D021A8D208500308A8B /* AssetsLibrary.framework in Frameworks */,
				8EED6596198A574C000A4449 /* CoreLocation.framework in Frameworks */,
				8EED65A8198A6273000A4449 /* OpenGLES.framework in Frameworks */,
				8EED65A6198A626B000A4449 /* MapKit.framework in Frameworks */,
				8EED65A0198A5789000A4449 /* Accounts.framework in Frameworks */,
				8EED659E198A5782000A4449 /* Social.framework in Frameworks */,
				8EED659C198A5773000A4449 /* AddressBookUI.framework in Frameworks */,
				8EED6598198A5754000A4449 /* MessageUI.framework in Frameworks */,
				B7A71F472CCA6A810012E129 /* WFAVUniPlugin.framework in Frameworks */,
				67AB0B3D21EEEA6B0029B229 /* libAMapImp.a in Frameworks */,
				8EED6592198A5737000A4449 /* AddressBook.framework in Frameworks */,
				95BC64EE2754A50000CB55B9 /* UMAPM.framework in Frameworks */,
				8EED6422198A2678000A4449 /* AudioToolbox.framework in Frameworks */,
				8EED6420198A2668000A4449 /* MediaPlayer.framework in Frameworks */,
				8EED641E198A265F000A4449 /* CoreTelephony.framework in Frameworks */,
				8EED6416198A2635000A4449 /* CoreVideo.framework in Frameworks */,
				8EED6414198A262C000A4449 /* CoreMedia.framework in Frameworks */,
				8EED6412198A2622000A4449 /* MobileCoreServices.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		4F48901F24656F72003B56F0 /* Bundles */ = {
			isa = PBXGroup;
			children = (
				4FA53F7826E5D09500BAD6A0 /* uni-jsframework-vue3.js */,
				4F48903524656F73003B56F0 /* uni-jsframework-dev.js */,
				4F48902424656F73003B56F0 /* uni-jsframework.js */,
				4F48903C24656F73003B56F0 /* weex-polyfill.js */,
				4F48903D24656F73003B56F0 /* weexUniJs.js */,
				4F48902124656F73003B56F0 /* __uniappes6.js */,
				4F48903624656F73003B56F0 /* <EMAIL> */,
				4F48903224656F73003B56F0 /* unincomponents.ttf */,
				4F48902024656F73003B56F0 /* mapapi.bundle */,
				4F48902224656F73003B56F0 /* MiPassport.bundle */,
				4F48902324656F73003B56F0 /* AlipaySDK.bundle */,
				4F48902524656F73003B56F0 /* WeiboSDK.bundle */,
				4F48902724656F73003B56F0 /* BDSClientEASRResources */,
				4F48903024656F73003B56F0 /* DCTZImagePickerController.bundle */,
				4F48903124656F73003B56F0 /* PandoraApi.bundle */,
				4F48903324656F73003B56F0 /* QHADVideoPlayer.bundle */,
				4F48903424656F73003B56F0 /* AMap.bundle */,
				4F48903724656F73003B56F0 /* qucsdkResources.bundle */,
				4F48903824656F73003B56F0 /* BUAdSDK.bundle */,
				4F48903924656F73003B56F0 /* DCPGVideo.bundle */,
				4F48903A24656F73003B56F0 /* DCMediaVideo.bundle */,
				4F48903B24656F73003B56F0 /* DCSVProgressHUD.bundle */,
			);
			name = Bundles;
			path = ../../SDK/Bundles;
			sourceTree = "<group>";
		};
		4F48902724656F73003B56F0 /* BDSClientEASRResources */ = {
			isa = PBXGroup;
			children = (
				4F48902824656F73003B56F0 /* bds_license.dat */,
				4F48902924656F73003B56F0 /* bds_easr_basic_model.dat */,
				4F48902A24656F73003B56F0 /* bds_easr_mfe_cmvn.dat */,
				4F48902B24656F73003B56F0 /* temp_license_2018-02-24.dat */,
				4F48902C24656F73003B56F0 /* bds_easr_mfe_dnn.dat */,
				4F48902D24656F73003B56F0 /* bds_easr_gramm.dat */,
				4F48902E24656F73003B56F0 /* bds_easr_wakeup_words.dat */,
				4F48902F24656F73003B56F0 /* bds_easr_dnn_wakeup_model.dat */,
			);
			path = BDSClientEASRResources;
			sourceTree = "<group>";
		};
		4F48905B24656F98003B56F0 /* inc */ = {
			isa = PBXGroup;
			children = (
				4F48905C24656F98003B56F0 /* PDRCoreAppWindow.h */,
				4F48905D24656F98003B56F0 /* PDRNView.h */,
				4F48905E24656F98003B56F0 /* PDRCommonString.h */,
				4F48905F24656F98003B56F0 /* PDRCoreWindowManager.h */,
				4F48906024656F98003B56F0 /* PGPlugin.h */,
				4F48906124656F98003B56F0 /* H5WEEngineExport.h */,
				4F48906224656F98003B56F0 /* PDRCoreAppInfo.h */,
				4F48906324656F98003B56F0 /* PDRToolSystem.h */,
				4F4DDB6725AC4F010008AE37 /* DCSVProgressHUD */,
				4F4DDB5725AC4F010008AE37 /* DCTZImagePickerController */,
				4F48907424656F98003B56F0 /* PDRCoreAppFrame.h */,
				4F48907524656F98003B56F0 /* Masonry */,
				4F48908A24656F98003B56F0 /* PGMethod.h */,
				4F48908B24656F98003B56F0 /* PDRCore.h */,
				4F48908C24656F98003B56F0 /* PGObject.h */,
				4F48908D24656F98003B56F0 /* PDRToolSystemEx.h */,
				4F48908E24656F98003B56F0 /* PDRCoreAppManager.h */,
				4F48908F24656F98003B56F0 /* PDRCoreSettings.h */,
				4F48909024656F98003B56F0 /* H5CoreScreenEdgePan.h */,
				4F48909124656F98003B56F0 /* H5UniversalApp.h */,
				4F48909324656F98003B56F0 /* H5CoreOverrideResourceOptions.h */,
				4F48909424656F98003B56F0 /* weexHeader */,
				4F4890DA24656F98003B56F0 /* SDWebImage */,
				4F48911C24656F98003B56F0 /* PDRCoreDefs.h */,
				4F48911D24656F98003B56F0 /* PDRCoreApp.h */,
			);
			name = inc;
			path = ../../SDK/inc;
			sourceTree = "<group>";
		};
		4F48907524656F98003B56F0 /* Masonry */ = {
			isa = PBXGroup;
			children = (
				4F48907624656F98003B56F0 /* MASCompositeConstraint.h */,
				4F48907724656F98003B56F0 /* MASConstraint+Private.h */,
				4F48907824656F98003B56F0 /* MASLayoutConstraint.h */,
				4F48907924656F98003B56F0 /* NSArray+MASShorthandAdditions.h */,
				4F48907A24656F98003B56F0 /* MASConstraintMaker.h */,
				4F48907B24656F98003B56F0 /* View+MASAdditions.h */,
				4F48907C24656F98003B56F0 /* NSArray+MASAdditions.h */,
				4F48907D24656F98003B56F0 /* MASUtilities.h */,
				4F48907E24656F98003B56F0 /* MASViewAttribute.h */,
				4F48907F24656F98003B56F0 /* MASViewConstraint.h */,
				4F48908024656F98003B56F0 /* MASConstraint.h */,
				4F48908124656F98003B56F0 /* NSLayoutConstraint+MASDebugAdditions.h */,
				4F48908224656F98003B56F0 /* View+MASShorthandAdditions.h */,
				4F48908324656F98003B56F0 /* Masonry.h */,
				4F48908424656F98003B56F0 /* ViewController+MASAdditions.h */,
			);
			path = Masonry;
			sourceTree = "<group>";
		};
		4F48909424656F98003B56F0 /* weexHeader */ = {
			isa = PBXGroup;
			children = (
				4F48909524656F98003B56F0 /* WXModuleProtocol.h */,
				4F48909624656F98003B56F0 /* WXResourceLoader.h */,
				4F48909724656F98003B56F0 /* WXApmForInstance.h */,
				4F48909824656F98003B56F0 /* WXWebSocketHandler.h */,
				4F48909924656F98003B56F0 /* WXImgLoaderProtocol.h */,
				4F48909A24656F98003B56F0 /* WXComponentManager.h */,
				4F48909B24656F98003B56F0 /* WXScrollerProtocol.h */,
				4F48909C24656F98003B56F0 /* WXRichText.h */,
				4F48909D24656F98003B56F0 /* WXView.h */,
				4F48909E24656F98003B56F0 /* WXValidateProtocol.h */,
				4F48909F24656F98003B56F0 /* WXDefine.h */,
				4F4890A024656F98003B56F0 /* JSContext+Weex.h */,
				4F4890A124656F98003B56F0 /* WXExtendCallNativeProtocol.h */,
				4F4890A224656F98003B56F0 /* WXSDKEngine.h */,
				4F4890A324656F98003B56F0 /* style.h */,
				4F4890A424656F98003B56F0 /* WXResourceRequestHandler.h */,
				4F4890A524656F98003B56F0 /* WXDisplayLinkManager.h */,
				4F4890A624656F98003B56F0 /* WXUtility.h */,
				4F4890A724656F98003B56F0 /* WXResourceRequest.h */,
				4F4890A824656F98003B56F0 /* WXConvert.h */,
				4F4890A924656F98003B56F0 /* WXPageEventNotifyEvent.h */,
				4F4890AA24656F98003B56F0 /* WXSDKInstance.h */,
				4F4890AB24656F98003B56F0 /* WXSDKManager.h */,
				4F4890AC24656F98003B56F0 /* WXDebugTool.h */,
				4F4890AD24656F98003B56F0 /* WXType.h */,
				4F4890AE24656F98003B56F0 /* WXApmProtocol.h */,
				4F4890AF24656F98003B56F0 /* WXJSFrameworkLoadProtocol.h */,
				4F4890B024656F98003B56F0 /* WXExceptionUtils.h */,
				4F4890B124656F98003B56F0 /* WXIndicatorComponent.h */,
				4F4890B224656F98003B56F0 /* WXErrorView.h */,
				4F4890B324656F98003B56F0 /* NSObject+WXSwizzle.h */,
				4F4890B424656F98003B56F0 /* WXMonitor.h */,
				4F4890B524656F98003B56F0 /* WXSDKInstance+DCExtend.h */,
				4F4890B624656F98003B56F0 /* WXRecyclerComponent.h */,
				4F4890B724656F98003B56F0 /* WXLog.h */,
				4F4890B824656F98003B56F0 /* WXScrollerComponent.h */,
				4F4890B924656F98003B56F0 /* WXNetworkProtocol.h */,
				4F4890BA24656F98003B56F0 /* WXTracingManager.h */,
				4F4890BB24656F98003B56F0 /* WXComponent.h */,
				4F4890BC24656F98003B56F0 /* WXPrerenderManager.h */,
				4F4890BD24656F98003B56F0 /* WXComponent+Layout.h */,
				4F4890BE24656F98003B56F0 /* layout.h */,
				4F4890BF24656F98003B56F0 /* WXSDKInstance+Bridge.h */,
				4F4890C024656F98003B56F0 /* UniPluginProtocol.h */,
				4F4890C124656F98003B56F0 /* WXModalUIModule.h */,
				4F4890C224656F98003B56F0 /* WXAnalyzerCenter.h */,
				4F4890C324656F98003B56F0 /* WXVoiceOverModule.h */,
				4F4890C424656F98003B56F0 /* WXRootView.h */,
				4F4890C524656F98003B56F0 /* WXAppConfiguration.h */,
				4F4890C624656F98003B56F0 /* WXListComponent.h */,
				4F4890C724656F98003B56F0 /* WXSDKError.h */,
				4F4890C824656F98003B56F0 /* WXRootViewController.h */,
				4F4890C924656F98003B56F0 /* WXNavigationProtocol.h */,
				4F4890CA24656F98003B56F0 /* WXAnalyzerProtocol.h */,
				4F4890CB24656F98003B56F0 /* WeexSDK.h */,
				4F4890CC24656F98003B56F0 /* WXBridgeManager.h */,
				4F4890CD24656F98003B56F0 /* WXResourceResponse.h */,
				4F4890CE24656F98003B56F0 /* WXBaseViewController.h */,
				4F4890CF24656F98003B56F0 /* WXAppMonitorProtocol.h */,
				4F4890D024656F98003B56F0 /* WeexProtocol.h */,
				4F4890D124656F98003B56F0 /* WXJSExceptionProtocol.h */,
				4F4890D224656F98003B56F0 /* WXJSExceptionInfo.h */,
				4F4890D324656F98003B56F0 /* WXTracingProtocol.h */,
				4F4890D424656F98003B56F0 /* flex_enum.h */,
				4F4890D524656F98003B56F0 /* WXConfigCenterProtocol.h */,
				4F4890D624656F98003B56F0 /* WXEventModuleProtocol.h */,
				4F4890D724656F98003B56F0 /* WXURLRewriteProtocol.h */,
				4F4890D824656F98003B56F0 /* WXAComponent.h */,
				4F4890D924656F98003B56F0 /* WXBridgeProtocol.h */,
			);
			path = weexHeader;
			sourceTree = "<group>";
		};
		4F4890DA24656F98003B56F0 /* SDWebImage */ = {
			isa = PBXGroup;
			children = (
				4F4890DB24656F98003B56F0 /* Core */,
				4F48910E24656F98003B56F0 /* webp */,
				4F48911124656F98003B56F0 /* Private */,
			);
			path = SDWebImage;
			sourceTree = "<group>";
		};
		4F4890DB24656F98003B56F0 /* Core */ = {
			isa = PBXGroup;
			children = (
				4F4890DC24656F98003B56F0 /* SDAnimatedImageRep.h */,
				4F4890DD24656F98003B56F0 /* SDDiskCache.h */,
				4F4890DE24656F98003B56F0 /* SDImageIOCoder.h */,
				4F4890DF24656F98003B56F0 /* NSButton+WebCache.h */,
				4F4890E024656F98003B56F0 /* SDImageGraphics.h */,
				4F4890E124656F98003B56F0 /* UIImageView+WebCache.h */,
				4F4890E224656F98003B56F0 /* NSData+ImageContentType.h */,
				4F4890E324656F98003B56F0 /* SDImageTransformer.h */,
				4F4890E424656F98003B56F0 /* SDImageCachesManager.h */,
				4F4890E524656F98003B56F0 /* SDWebImageTransition.h */,
				4F4890E624656F98003B56F0 /* SDImageLoadersManager.h */,
				4F4890E724656F98003B56F0 /* SDWebImageDownloaderOperation.h */,
				4F4890E824656F98003B56F0 /* SDImageFrame.h */,
				4F4890E924656F98003B56F0 /* SDImageGIFCoder.h */,
				4F4890EA24656F98003B56F0 /* SDImageCache.h */,
				4F4890EB24656F98003B56F0 /* SDWebImageDownloaderConfig.h */,
				4F4890EC24656F98003B56F0 /* SDImageCacheConfig.h */,
				4F4890ED24656F98003B56F0 /* SDWebImageCacheKeyFilter.h */,
				4F4890EE24656F98003B56F0 /* UIImage+MemoryCacheCost.h */,
				4F4890EF24656F98003B56F0 /* SDImageCacheDefine.h */,
				4F4890F024656F98003B56F0 /* UIButton+WebCache.h */,
				4F4890F124656F98003B56F0 /* SDWebImageDownloaderRequestModifier.h */,
				4F4890F224656F98003B56F0 /* UIImage+Metadata.h */,
				4F4890F324656F98003B56F0 /* SDWebImageOptionsProcessor.h */,
				4F4890F424656F98003B56F0 /* UIView+WebCache.h */,
				4F4890F524656F98003B56F0 /* UIView+WebCacheOperation.h */,
				4F4890F624656F98003B56F0 /* SDWebImageDefine.h */,
				4F4890F724656F98003B56F0 /* SDImageCoder.h */,
				4F4890F824656F98003B56F0 /* SDAnimatedImageView+WebCache.h */,
				4F4890F924656F98003B56F0 /* NSImage+Compatibility.h */,
				4F4890FA24656F98003B56F0 /* SDAnimatedImageView.h */,
				4F4890FB24656F98003B56F0 /* SDAnimatedImage.h */,
				4F4890FC24656F98003B56F0 /* UIImageView+HighlightedWebCache.h */,
				4F4890FD24656F98003B56F0 /* SDWebImageManager.h */,
				4F4890FE24656F98003B56F0 /* SDWebImageOperation.h */,
				4F4890FF24656F98003B56F0 /* SDWebImageIndicator.h */,
				4F48910024656F98003B56F0 /* SDWebImageCacheSerializer.h */,
				4F48910124656F98003B56F0 /* SDImageLoader.h */,
				4F48910224656F98003B56F0 /* SDWebImageDownloader.h */,
				4F48910324656F98003B56F0 /* UIImage+Transform.h */,
				4F48910424656F98003B56F0 /* UIImage+ForceDecode.h */,
				4F48910524656F98003B56F0 /* SDImageCoderHelper.h */,
				4F48910624656F98003B56F0 /* SDWebImagePrefetcher.h */,
				4F48910724656F98003B56F0 /* SDImageAPNGCoder.h */,
				4F48910824656F98003B56F0 /* SDWebImageError.h */,
				4F48910924656F98003B56F0 /* SDWebImageCompat.h */,
				4F48910A24656F98003B56F0 /* SDMemoryCache.h */,
				4F48910B24656F98003B56F0 /* UIImage+MultiFormat.h */,
				4F48910C24656F98003B56F0 /* SDImageCodersManager.h */,
				4F48910D24656F98003B56F0 /* UIImage+GIF.h */,
			);
			path = Core;
			sourceTree = "<group>";
		};
		4F48910E24656F98003B56F0 /* webp */ = {
			isa = PBXGroup;
			children = (
				4F48910F24656F98003B56F0 /* SDImageWebPCoder.h */,
				4F48911024656F98003B56F0 /* UIImage+WebP.h */,
			);
			path = webp;
			sourceTree = "<group>";
		};
		4F48911124656F98003B56F0 /* Private */ = {
			isa = PBXGroup;
			children = (
				4F48911224656F98003B56F0 /* SDWeakProxy.h */,
				4F48911324656F98003B56F0 /* SDInternalMacros.h */,
				4F48911424656F98003B56F0 /* NSBezierPath+RoundedCorners.h */,
				4F48911524656F98003B56F0 /* SDImageAPNGCoderInternal.h */,
				4F48911624656F98003B56F0 /* SDAsyncBlockOperation.h */,
				4F48911724656F98003B56F0 /* SDImageGIFCoderInternal.h */,
				4F48911824656F98003B56F0 /* UIColor+HexString.h */,
				4F48911924656F98003B56F0 /* SDmetamacros.h */,
				4F48911A24656F98003B56F0 /* SDImageCachesManagerOperation.h */,
				4F48911B24656F98003B56F0 /* SDImageAssetManager.h */,
			);
			path = Private;
			sourceTree = "<group>";
		};
		4F4DDB5725AC4F010008AE37 /* DCTZImagePickerController */ = {
			isa = PBXGroup;
			children = (
				4F4DDB5825AC4F010008AE37 /* DCTZPhotoPickerController.h */,
				4F4DDB5925AC4F010008AE37 /* DCTZImageManager.h */,
				4F4DDB5A25AC4F010008AE37 /* DCTZAssetCell.h */,
				4F4DDB5B25AC4F010008AE37 /* DCTZAssetModel.h */,
				4F4DDB5C25AC4F010008AE37 /* DCTZGifPhotoPreviewController.h */,
				4F4DDB5D25AC4F010008AE37 /* DCTZLocationManager.h */,
				4F4DDB5E25AC4F010008AE37 /* UIView+DCLayout.h */,
				4F4DDB5F25AC4F010008AE37 /* DCTZImageCropManager.h */,
				4F4DDB6025AC4F010008AE37 /* DCTZImagePickerController.h */,
				4F4DDB6125AC4F010008AE37 /* NSBundle+DCTZImagePicker.h */,
				4F4DDB6225AC4F010008AE37 /* DCTZVideoPlayerController.h */,
				4F4DDB6325AC4F010008AE37 /* DCUIViewControllerHUD.h */,
				4F4DDB6425AC4F010008AE37 /* DCTZPhotoPreviewCell.h */,
				4F4DDB6525AC4F010008AE37 /* DCTZPhotoPreviewController.h */,
				4F4DDB6625AC4F010008AE37 /* DCTZProgressView.h */,
			);
			path = DCTZImagePickerController;
			sourceTree = "<group>";
		};
		4F4DDB6725AC4F010008AE37 /* DCSVProgressHUD */ = {
			isa = PBXGroup;
			children = (
				4F4DDB6825AC4F010008AE37 /* DCSVIndefiniteAnimatedView.h */,
				4F4DDB6925AC4F010008AE37 /* DCSVProgressAnimatedView.h */,
				4F4DDB6A25AC4F010008AE37 /* DCSVProgressHUD.h */,
				4F4DDB6B25AC4F010008AE37 /* DCSVRadialGradientLayer.h */,
			);
			path = DCSVProgressHUD;
			sourceTree = "<group>";
		};
		8EED628F198A1D13000A4449 = {
			isa = PBXGroup;
			children = (
				B7A71F5E2CCA6CBA0012E129 /* WFPttClientUniPlugin.xcodeproj */,
				B7A71F3B2CCA65ED0012E129 /* WFAVUniPlugin.xcodeproj */,
				B7A71F352CCA65DD0012E129 /* WFClientUniPlugin.xcodeproj */,
				2F9FE7E92BD8E834001137CC /* PrivacyInfo.xcprivacy */,
				6731F399232F4F03007838BC /* HBuilder.entitlements */,
				8EED62A1198A1D13000A4449 /* HBuilder-Hello */,
				8EED629A198A1D13000A4449 /* Frameworks */,
				8EED6299198A1D13000A4449 /* Products */,
			);
			sourceTree = "<group>";
		};
		8EED6299198A1D13000A4449 /* Products */ = {
			isa = PBXGroup;
			children = (
				8EED6298198A1D13000A4449 /* HBuilder.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		8EED629A198A1D13000A4449 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				B7A71F4B2CCA6B0F0012E129 /* Bugly.framework */,
				B7A71F4C2CCA6B0F0012E129 /* PttClient.xcframework */,
				B7A71F4D2CCA6B0F0012E129 /* WebRTC.xcframework */,
				B7A71F4E2CCA6B0F0012E129 /* WFAVEngineKit.xcframework */,
				B7A71F4F2CCA6B0F0012E129 /* WFChatClient.xcframework */,
				2F434B052C787ED70020481E /* AlipaySDK.xcframework */,
				2FDE6A36296D742B004C7701 /* GTSDK.xcframework */,
				2FBB55F429530500002214BF /* liblibWeex.a */,
				2F4864C1293E10EE00142360 /* DCUniRecord.framework */,
				2F5FAE672865DB7800430AFA /* BMKLocationKit.framework */,
				2F5FAE682865DB7800430AFA /* KSCrash.framework */,
				95BC64EB2754A50000CB55B9 /* UMAPM.framework */,
				4F8C6936265E502C00C2A73C /* Masonry.framework */,
				4F5762F6261461AB00A5C0BA /* GLKit.framework */,
				4F5762F4261461A000A5C0BA /* MetalKit.framework */,
				4F4DAE6A25DE60DB0094E580 /* libUniPush.a */,
				4FE3666B254FE70E00DCD173 /* libuchardet.a */,
				67254219242474E20086649B /* libH5WEUIWebview.a */,
				6731F397232F4D8E007838BC /* Photos.framework */,
				6731F395232F4D05007838BC /* UserNotifications.framework */,
				6731F393232F4CE2007838BC /* libresolv.tbd */,
				67229AC9230171AE0093F29A /* libDCUniAmap.a */,
				67229ACB230171AE0093F29A /* libDCUniBarcode.a */,
				67229ACD230171AE0093F29A /* libDCUniBMap.a */,
				67229ACE230171AE0093F29A /* libDCUniGPUImage.a */,
				67229ACF230171AE0093F29A /* libDCUniLivePush.a */,
				67229AD0230171AE0093F29A /* libDCUniMap.a */,
				67229ACA230171AE0093F29A /* libDCUniVideo.a */,
				672CE2B422DC916C005A0D88 /* libDCUniZXing.a */,
				672CE2B222DC9118005A0D88 /* DCUniVideoPublic.framework */,
				6756AB6721F58ACC00765F52 /* liblibBeacon.a */,
				6756AB6921F58CA300765F52 /* liblibBlueTooth.a */,
				67D6390821C12944005D1E8F /* libbaiduSpeech.a */,
				67D6390721C12944005D1E8F /* libBaiduSpeechSDK.a */,
				2FD11BBA215C79C5000A23AD /* liblibAdSupport.a */,
				2F0BA417215B8B5C00F67004 /* libbz2.1.0.tbd */,
				2F0BA415215B8B1300F67004 /* VideoToolbox.framework */,
				2F0BA411215B814000F67004 /* liblibVideo.a */,
				2F0BA40F215B784000F67004 /* Contacts.framework */,
				7A49810C2126B01900D20880 /* Accelerate.framework */,
				7A49810A2126B01200D20880 /* libiconv.tbd */,
				247F85DA1FA32B2C006ECAC6 /* liblibPDRCore.a */,
				673E6E7221E44ABF00C021FE /* liblibFingerprint.a */,
				67566B1E2251DC3A00BDF218 /* liblibSqlite.a */,
				67AB0B9821EF10FE0029B229 /* libBaiduKeyVerify.a */,
				67AB0B9E21EF111D0029B229 /* BaiduMapAPI_Base.framework */,
				67AB0B9D21EF111D0029B229 /* BaiduMapAPI_Map.framework */,
				67AB0B9B21EF111D0029B229 /* BaiduMapAPI_Search.framework */,
				67AB0B9C21EF111D0029B229 /* BaiduMapAPI_Utils.framework */,
				67AB0B9621EF10E60029B229 /* liblibGeolocation.a */,
				67AB0B3C21EEEA6B0029B229 /* libAMapImp.a */,
				67AB0B4221EEEAE50029B229 /* AMapFoundationKit.framework */,
				67AB0B4021EEEACA0029B229 /* MAMapKit.framework */,
				67AB0B3E21EEEAA60029B229 /* AMapSearchKit.framework */,
				2430A6FF1E11603E00D3D42D /* libcrypto.a */,
				2430A7001E11603E00D3D42D /* libssl.a */,
				24F990121DFBDA3300848C2B /* CoreData.framework */,
				24A7515E1D9CCCC600C8B0F9 /* QuickLook.framework */,
				242725CC1D26686700EBD79E /* CoreMotion.framework */,
				242725CA1D2666ED00EBD79E /* JavaScriptCore.framework */,
				2447371A1D0830BB00D0F08F /* WebKit.framework */,
				24AFD8031CB50C3F00C0F062 /* libalixpayment.a */,
				7A1967C42125371000B330A9 /* storage.framework */,
				7A1967C2212536EC00B330A9 /* libmp3lame.a */,
				24AFD8061CB50C3F00C0F062 /* libBaiduMobStatForSDK.a */,
				24AFD8091CB50C3F00C0F062 /* libcoreSupport.a */,
				24AFD80A1CB50C3F00C0F062 /* libGeTuiPush.a */,
				24AFD80C1CB50C3F00C0F062 /* libIAPPay.a */,
				24AFD80F1CB50C3F00C0F062 /* liblibAccelerometer.a */,
				24AFD8101CB50C3F00C0F062 /* liblibBarcode.a */,
				24AFD8111CB50C3F00C0F062 /* liblibCache.a */,
				24AFD8121CB50C3F00C0F062 /* liblibCamera.a */,
				24AFD8131CB50C3F00C0F062 /* liblibContacts.a */,
				24AFD8151CB50C4000C0F062 /* liblibIO.a */,
				24AFD8161CB50C4000C0F062 /* liblibLog.a */,
				24AFD8171CB50C4000C0F062 /* liblibMap.a */,
				24AFD8181CB50C4000C0F062 /* liblibMedia.a */,
				24AFD8191CB50C4000C0F062 /* liblibMessage.a */,
				24AFD81A1CB50C4000C0F062 /* liblibNativeObj.a */,
				24AFD81B1CB50C4000C0F062 /* liblibNativeUI.a */,
				24AFD81C1CB50C4000C0F062 /* liblibNavigator.a */,
				24AFD81D1CB50C4000C0F062 /* liblibOauth.a */,
				24AFD81E1CB50C4000C0F062 /* liblibOrientation.a */,
				24AFD81F1CB50C4000C0F062 /* liblibPayment.a */,
				24AFD8211CB50C4000C0F062 /* liblibPGInvocation.a */,
				24AFD8221CB50C4000C0F062 /* liblibPGProximity.a */,
				24AFD8231CB50C4000C0F062 /* liblibPush.a */,
				24AFD8241CB50C4000C0F062 /* liblibShare.a */,
				24AFD8251CB50C4000C0F062 /* liblibSpeech.a */,
				24AFD8261CB50C4000C0F062 /* liblibStatistic.a */,
				24AFD8271CB50C4000C0F062 /* liblibStorage.a */,
				24AFD8281CB50C4000C0F062 /* liblibUI.a */,
				24AFD82B1CB50C4000C0F062 /* liblibXHR.a */,
				24AFD82C1CB50C4000C0F062 /* liblibZip.a */,
				24AFD82E1CB50C4000C0F062 /* libopencore-amrnb.a */,
				24AFD8321CB50C4000C0F062 /* libQQOauth.a */,
				24AFD8331CB50C4000C0F062 /* libQQShare.a */,
				24AFD8341CB50C4000C0F062 /* libSDWebImage.a */,
				24AFD8351CB50C4000C0F062 /* libSinaShare.a */,
				24AFD8361CB50C4000C0F062 /* libSinaWBOauth.a */,
				24AFD8391CB50C4000C0F062 /* libTouchJSON.a */,
				24AFD83C1CB50C4000C0F062 /* libWeiboSDK.a */,
				7A1967C621253B5F00B330A9 /* libWeChatSDK_pay.a */,
				24AFD83D1CB50C4000C0F062 /* libweixinShare.a */,
				24AFD83E1CB50C4000C0F062 /* libWXOauth.a */,
				24AFD83F1CB50C4000C0F062 /* libwxpay.a */,
				24AFD7FE1CB50C3200C0F062 /* TencentOpenAPI.framework */,
				24BD5AF51C994BB200B05AA2 /* libc++.tbd */,
				24BD5AF11C994A1700B05AA2 /* libicucore.tbd */,
				24BD5AEF1C99494A00B05AA2 /* libz.tbd */,
				24BD5AED1C99494200B05AA2 /* libxml2.tbd */,
				24BD5AE91C99492A00B05AA2 /* libiconv.2.tbd */,
				24BD5AE71C99491D00B05AA2 /* libsqlite3.0.tbd */,
				240905021C200AEF0070786F /* CoreBluetooth.framework */,
				8E6E37AB1B0E1B580036EB48 /* ImageIO.framework */,
				8E163D011A8D208500308A8B /* AssetsLibrary.framework */,
				7ACF69AC19FF89B1007C64F1 /* Security.framework */,
				7ACF69AA19FF899A007C64F1 /* CFNetwork.framework */,
				8EED65A7198A6273000A4449 /* OpenGLES.framework */,
				8EED65A5198A626B000A4449 /* MapKit.framework */,
				8EED659F198A5789000A4449 /* Accounts.framework */,
				8EED659D198A5782000A4449 /* Social.framework */,
				8EED659B198A5773000A4449 /* AddressBookUI.framework */,
				8EED6597198A5754000A4449 /* MessageUI.framework */,
				8EED6595198A574C000A4449 /* CoreLocation.framework */,
				8EED6593198A5743000A4449 /* AVFoundation.framework */,
				8EED6591198A5737000A4449 /* AddressBook.framework */,
				8EED6421198A2678000A4449 /* AudioToolbox.framework */,
				8EED629D198A1D13000A4449 /* CoreGraphics.framework */,
				8EED6413198A262C000A4449 /* CoreMedia.framework */,
				8EED641D198A265F000A4449 /* CoreTelephony.framework */,
				8EED6415198A2635000A4449 /* CoreVideo.framework */,
				8EED629B198A1D13000A4449 /* Foundation.framework */,
				8EED641F198A2668000A4449 /* MediaPlayer.framework */,
				8EED6411198A2622000A4449 /* MobileCoreServices.framework */,
				8EED641B198A2654000A4449 /* SystemConfiguration.framework */,
				8EED629F198A1D13000A4449 /* UIKit.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		8EED62A1198A1D13000A4449 /* HBuilder-Hello */ = {
			isa = PBXGroup;
			children = (
				8EED62AA198A1D13000A4449 /* AppDelegate.h */,
				8EED62AB198A1D13000A4449 /* AppDelegate.m */,
				4F48905B24656F98003B56F0 /* inc */,
				8EED658F198A3DF7000A4449 /* Pandora */,
				8EED62A2198A1D13000A4449 /* Supporting Files */,
				8EED62B3198A1D14000A4449 /* ViewController.h */,
				8EED62B4198A1D14000A4449 /* ViewController.m */,
				2FDE6A39296D74B3004C7701 /* DCloud.swift */,
				2FDE6A38296D74B3004C7701 /* HBuilder-Bridging-Header.h */,
			);
			path = "HBuilder-Hello";
			sourceTree = "<group>";
		};
		8EED62A2198A1D13000A4449 /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				2F0BA4DB215BA12300F67004 /* LaunchScreen.storyboard */,
				6743942923C98EB30085145E /* LaunchScreenAD.storyboard */,
				4F48911E2465734F003B56F0 /* <EMAIL> */,
				4F48911F2465734F003B56F0 /* <EMAIL> */,
				4F48901F24656F72003B56F0 /* Bundles */,
				2F0BA3F8215B48A700F67004 /* Images.xcassets */,
				8EED62A3198A1D13000A4449 /* HBuilder-Hello-Info.plist */,
				67B7CAA121DCE8180083E96A /* control.xml */,
				8EED62A4198A1D13000A4449 /* InfoPlist.strings */,
				8EED62A7198A1D13000A4449 /* main.m */,
				8EED62A9198A1D13000A4449 /* HBuilder-Hello-Prefix.pch */,
				67E9CDCD22968D2E0076E0FB /* Localizable.strings */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		B7A71F362CCA65DD0012E129 /* Products */ = {
			isa = PBXGroup;
			children = (
				B7A71F3A2CCA65DD0012E129 /* WFClientUniPlugin.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		B7A71F3C2CCA65ED0012E129 /* Products */ = {
			isa = PBXGroup;
			children = (
				B7A71F402CCA65ED0012E129 /* WFAVUniPlugin.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		B7A71F5F2CCA6CBA0012E129 /* Products */ = {
			isa = PBXGroup;
			children = (
				B7A71F632CCA6CBA0012E129 /* WFPttClientUniPlugin.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		8EED6297198A1D13000A4449 /* HBuilder */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8EED62CD198A1D14000A4449 /* Build configuration list for PBXNativeTarget "HBuilder" */;
			buildPhases = (
				8EED6294198A1D13000A4449 /* Sources */,
				8EED6295198A1D13000A4449 /* Frameworks */,
				8EED6296198A1D13000A4449 /* Resources */,
				B7A71F572CCA6B1B0012E129 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = HBuilder;
			productName = "HBuilder-Hello";
			productReference = 8EED6298198A1D13000A4449 /* HBuilder.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		8EED6290198A1D13000A4449 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0510;
				ORGANIZATIONNAME = DCloud;
				TargetAttributes = {
					8EED6297198A1D13000A4449 = {
						LastSwiftMigration = 1320;
						SystemCapabilities = {
							com.apple.Push = {
								enabled = 1;
							};
						};
					};
				};
			};
			buildConfigurationList = 8EED6293198A1D13000A4449 /* Build configuration list for PBXProject "HBuilder-Hello" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
				Base,
				"zh-Hans",
			);
			mainGroup = 8EED628F198A1D13000A4449;
			productRefGroup = 8EED6299198A1D13000A4449 /* Products */;
			projectDirPath = "";
			projectReferences = (
				{
					ProductGroup = B7A71F3C2CCA65ED0012E129 /* Products */;
					ProjectRef = B7A71F3B2CCA65ED0012E129 /* WFAVUniPlugin.xcodeproj */;
				},
				{
					ProductGroup = B7A71F362CCA65DD0012E129 /* Products */;
					ProjectRef = B7A71F352CCA65DD0012E129 /* WFClientUniPlugin.xcodeproj */;
				},
				{
					ProductGroup = B7A71F5F2CCA6CBA0012E129 /* Products */;
					ProjectRef = B7A71F5E2CCA6CBA0012E129 /* WFPttClientUniPlugin.xcodeproj */;
				},
			);
			projectRoot = "";
			targets = (
				8EED6297198A1D13000A4449 /* HBuilder */,
			);
		};
/* End PBXProject section */

/* Begin PBXReferenceProxy section */
		B7A71F3A2CCA65DD0012E129 /* WFClientUniPlugin.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = WFClientUniPlugin.framework;
			remoteRef = B7A71F392CCA65DD0012E129 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		B7A71F402CCA65ED0012E129 /* WFAVUniPlugin.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = WFAVUniPlugin.framework;
			remoteRef = B7A71F3F2CCA65ED0012E129 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		B7A71F632CCA6CBA0012E129 /* WFPttClientUniPlugin.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = WFPttClientUniPlugin.framework;
			remoteRef = B7A71F622CCA6CBA0012E129 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
/* End PBXReferenceProxy section */

/* Begin PBXResourcesBuildPhase section */
		8EED6296198A1D13000A4449 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2F0BA3F9215B48A700F67004 /* Images.xcassets in Resources */,
				4F48904124656F73003B56F0 /* AlipaySDK.bundle in Resources */,
				4F4891202465734F003B56F0 /* <EMAIL> in Resources */,
				4F48905924656F73003B56F0 /* weex-polyfill.js in Resources */,
				8EED62A6198A1D13000A4449 /* InfoPlist.strings in Resources */,
				4F48904724656F73003B56F0 /* bds_easr_mfe_cmvn.dat in Resources */,
				6743942A23C98EB30085145E /* LaunchScreenAD.storyboard in Resources */,
				4F48904024656F73003B56F0 /* MiPassport.bundle in Resources */,
				4F48905324656F73003B56F0 /* <EMAIL> in Resources */,
				4F48904C24656F73003B56F0 /* bds_easr_dnn_wakeup_model.dat in Resources */,
				4F48905124656F73003B56F0 /* AMap.bundle in Resources */,
				4F48904A24656F73003B56F0 /* bds_easr_gramm.dat in Resources */,
				2F0BA4DC215BA12300F67004 /* LaunchScreen.storyboard in Resources */,
				4F48904E24656F73003B56F0 /* PandoraApi.bundle in Resources */,
				4F48904324656F73003B56F0 /* WeiboSDK.bundle in Resources */,
				4F48904224656F73003B56F0 /* uni-jsframework.js in Resources */,
				4F48904524656F73003B56F0 /* bds_license.dat in Resources */,
				4F48903F24656F73003B56F0 /* __uniappes6.js in Resources */,
				2F9FE7EA2BD8E834001137CC /* PrivacyInfo.xcprivacy in Resources */,
				4F48904824656F73003B56F0 /* temp_license_2018-02-24.dat in Resources */,
				4F48905224656F73003B56F0 /* uni-jsframework-dev.js in Resources */,
				4F48903E24656F73003B56F0 /* mapapi.bundle in Resources */,
				4F48904D24656F73003B56F0 /* DCTZImagePickerController.bundle in Resources */,
				4F48905724656F73003B56F0 /* DCMediaVideo.bundle in Resources */,
				4F48904624656F73003B56F0 /* bds_easr_basic_model.dat in Resources */,
				4F4891212465734F003B56F0 /* <EMAIL> in Resources */,
				4F48905024656F73003B56F0 /* QHADVideoPlayer.bundle in Resources */,
				4F48904B24656F73003B56F0 /* bds_easr_wakeup_words.dat in Resources */,
				4F48905424656F73003B56F0 /* qucsdkResources.bundle in Resources */,
				4F48905824656F73003B56F0 /* DCSVProgressHUD.bundle in Resources */,
				4F48905624656F73003B56F0 /* DCPGVideo.bundle in Resources */,
				4F48904924656F73003B56F0 /* bds_easr_mfe_dnn.dat in Resources */,
				67E9CDCF22968D2E0076E0FB /* Localizable.strings in Resources */,
				67B7CAA221DCE8180083E96A /* control.xml in Resources */,
				4FA53F7926E5D09500BAD6A0 /* uni-jsframework-vue3.js in Resources */,
				4F48905A24656F73003B56F0 /* weexUniJs.js in Resources */,
				4F48904F24656F73003B56F0 /* unincomponents.ttf in Resources */,
				8EED6590198A3DF7000A4449 /* Pandora in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		8EED6294198A1D13000A4449 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8EED62B5198A1D14000A4449 /* ViewController.m in Sources */,
				8EED62AC198A1D13000A4449 /* AppDelegate.m in Sources */,
				2FDE6A3A296D74B3004C7701 /* DCloud.swift in Sources */,
				8EED62A8198A1D13000A4449 /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		67E9CDCD22968D2E0076E0FB /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				67E9CDCE22968D2E0076E0FB /* zh-Hans */,
				67E9CDD022968D350076E0FB /* en */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
		8EED62A4198A1D13000A4449 /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				8EED62A5198A1D13000A4449 /* en */,
				2F0BA40D215B6E6800F67004 /* zh-Hans */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		8EED62CB198A1D14000A4449 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 6.0;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		8EED62CC198A1D14000A4449 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 6.0;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		8EED62CE198A1D14000A4449 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CODE_SIGN_ENTITLEMENTS = HBuilder/HBuilder.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 20501;
				DEVELOPMENT_TEAM = NXP2SHDJL7;
				ENABLE_BITCODE = NO;
				ENABLE_TESTABILITY = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(DEVELOPER_FRAMEWORKS_DIR)",
					"$(PROJECT_DIR)/../SDK/libs/**",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/WF_SDK",
				);
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "HBuilder-Hello/HBuilder-Hello-Prefix.pch";
				GCC_PREPROCESSOR_DEFINITIONS = (
					PDR_PLUS_MAP,
					PDR_PLUS_GETUI,
					PDR_PLUS_UMENG,
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"$(SRCROOT)/../SDK/inc/**",
				);
				INFOPLIST_FILE = "HBuilder-Hello/HBuilder-Hello-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SRCROOT)/../SDK/libs/**",
					"$(PROJECT_DIR)",
				);
				MARKETING_VERSION = 2.5.1;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = cn.wildfirechat.UniDemo;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				"PROVISIONING_PROFILE[arch=*]" = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "HBuilder-Hello/HBuilder-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_WORKSPACE = YES;
				VALID_ARCHS = "armv7 arm64 i386 x86_64";
				WRAPPER_EXTENSION = app;
			};
			name = Debug;
		};
		8EED62CF198A1D14000A4449 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CODE_SIGN_ENTITLEMENTS = HBuilder/HBuilder.entitlements;
				CODE_SIGN_IDENTITY = "Apple Distribution";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "Apple Distribution";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 20501;
				DEVELOPMENT_TEAM = Y8356M2VAP;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(DEVELOPER_FRAMEWORKS_DIR)",
					"$(PROJECT_DIR)/../SDK/libs/**",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/WF_SDK",
				);
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "HBuilder-Hello/HBuilder-Hello-Prefix.pch";
				GCC_PREPROCESSOR_DEFINITIONS = (
					PDR_PLUS_MAP,
					PDR_PLUS_GETUI,
					PDR_PLUS_UMENG,
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"$(SRCROOT)/../SDK/inc/**",
				);
				INFOPLIST_FILE = "HBuilder-Hello/HBuilder-Hello-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SRCROOT)/../SDK/libs/**",
					"$(PROJECT_DIR)",
				);
				MARKETING_VERSION = 2.5.1;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = cn.wildfirechat.UniDemo;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "HBuilder-Hello/HBuilder-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_WORKSPACE = YES;
				VALID_ARCHS = "armv7 arm64 i386 x86_64";
				WRAPPER_EXTENSION = app;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		8EED6293198A1D13000A4449 /* Build configuration list for PBXProject "HBuilder-Hello" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8EED62CB198A1D14000A4449 /* Debug */,
				8EED62CC198A1D14000A4449 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8EED62CD198A1D14000A4449 /* Build configuration list for PBXNativeTarget "HBuilder" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8EED62CE198A1D14000A4449 /* Debug */,
				8EED62CF198A1D14000A4449 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 8EED6290198A1D13000A4449 /* Project object */;
}
