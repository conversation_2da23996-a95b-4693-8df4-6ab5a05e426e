"use weex:vue";

if (typeof Promise !== 'undefined' && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor
    return this.then(
      value => promise.resolve(callback()).then(() => value),
      reason => promise.resolve(callback()).then(() => {
        throw reason
      })
    )
  }
};

if (typeof uni !== 'undefined' && uni && uni.requireGlobal) {
  const global = uni.requireGlobal()
  ArrayBuffer = global.ArrayBuffer
  Int8Array = global.Int8Array
  Uint8Array = global.Uint8Array
  Uint8ClampedArray = global.Uint8ClampedArray
  Int16Array = global.Int16Array
  Uint16Array = global.Uint16Array
  Int32Array = global.Int32Array
  Uint32Array = global.Uint32Array
  Float32Array = global.Float32Array
  Float64Array = global.Float64Array
  BigInt64Array = global.BigInt64Array
  BigUint64Array = global.BigUint64Array
};


(()=>{var y=Object.create;var u=Object.defineProperty;var g=Object.getOwnPropertyDescriptor;var b=Object.getOwnPropertyNames;var k=Object.getPrototypeOf,C=Object.prototype.hasOwnProperty;var v=(t,o)=>()=>(o||t((o={exports:{}}).exports,o),o.exports);var T=(t,o,s,n)=>{if(o&&typeof o=="object"||typeof o=="function")for(let r of b(o))!C.call(t,r)&&r!==s&&u(t,r,{get:()=>o[r],enumerable:!(n=g(o,r))||n.enumerable});return t};var _=(t,o,s)=>(s=t!=null?y(k(t)):{},T(o||!t||!t.__esModule?u(s,"default",{value:t,enumerable:!0}):s,t));var i=v((A,f)=>{f.exports=Vue});var N=_(i());function m(t,o,...s){uni.__log__?uni.__log__(t,o,...s):console[t].apply(console,[...s,o])}var d=(t,o)=>{let s=t.__vccOpts||t;for(let[n,r]of o)s[n]=r;return s};var e=_(i()),w={name:"ApiTestPage",methods:{setUserEnableReceipt(){},modifyMyInfo(){},systemInfo(){m("log","at pages/misc/ApiTestPage.nvue:36","systemInfo",uni.getSystemInfoSync())},setHiddenGroupMemberName(){},pttRequestTalk(){}}};function x(t,o,s,n,r,a){let l=(0,e.resolveComponent)("button");return(0,e.openBlock)(),(0,e.createElementBlock)("scroll-view",{scrollY:!0,showScrollbar:!0,enableBackToTop:!0,bubble:"true",style:{flexDirection:"column"}},[(0,e.createElementVNode)("div",null,[(0,e.createVNode)(l,{onClick:a.setUserEnableReceipt},{default:(0,e.withCtx)(()=>[(0,e.createTextVNode)("setUserEnableReceipt")]),_:1},8,["onClick"]),(0,e.createVNode)(l,{onClick:a.modifyMyInfo},{default:(0,e.withCtx)(()=>[(0,e.createTextVNode)("modifyMyInfo")]),_:1},8,["onClick"]),(0,e.createVNode)(l,{onClick:a.systemInfo},{default:(0,e.withCtx)(()=>[(0,e.createTextVNode)("SystemInfo")]),_:1},8,["onClick"]),(0,e.createVNode)(l,{onClick:a.setHiddenGroupMemberName},{default:(0,e.withCtx)(()=>[(0,e.createTextVNode)("setHiddenGroupMemberName")]),_:1},8,["onClick"]),(0,e.createVNode)(l,{onClick:a.pttRequestTalk},{default:(0,e.withCtx)(()=>[(0,e.createTextVNode)("pttRequestTalk")]),_:1},8,["onClick"])])])}var c=d(w,[["render",x]]);var p=plus.webview.currentWebview();if(p){let t=parseInt(p.id),o="pages/misc/ApiTestPage",s={};try{s=JSON.parse(p.__query__)}catch(r){}c.mpType="page";let n=Vue.createPageApp(c,{$store:getApp({allowDefault:!0}).$store,__pageId:t,__pagePath:o,__pageQuery:s});n.provide("__globalStyles",Vue.useCssStyles([...__uniConfig.styles,...c.styles||[]])),n.mount("#root")}})();
