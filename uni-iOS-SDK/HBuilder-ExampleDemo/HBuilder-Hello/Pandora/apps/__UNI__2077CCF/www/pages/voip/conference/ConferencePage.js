"use weex:vue";

if (typeof Promise !== 'undefined' && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor
    return this.then(
      value => promise.resolve(callback()).then(() => value),
      reason => promise.resolve(callback()).then(() => {
        throw reason
      })
    )
  }
};

if (typeof uni !== 'undefined' && uni && uni.requireGlobal) {
  const global = uni.requireGlobal()
  ArrayBuffer = global.ArrayBuffer
  Int8Array = global.Int8Array
  Uint8Array = global.Uint8Array
  Uint8ClampedArray = global.Uint8ClampedArray
  Int16Array = global.Int16Array
  Uint16Array = global.Uint16Array
  Int32Array = global.Int32Array
  Uint32Array = global.Uint32Array
  Float32Array = global.Float32Array
  Float64Array = global.Float64Array
  BigInt64Array = global.BigInt64Array
  BigUint64Array = global.BigUint64Array
};


(()=>{var de=Object.create;var j=Object.defineProperty,fe=Object.defineProperties,pe=Object.getOwnPropertyDescriptor,ge=Object.getOwnPropertyDescriptors,he=Object.getOwnPropertyNames,z=Object.getOwnPropertySymbols,Ae=Object.getPrototypeOf,q=Object.prototype.hasOwnProperty,me=Object.prototype.propertyIsEnumerable;var X=(t,e,i)=>e in t?j(t,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[e]=i,M=(t,e)=>{for(var i in e||(e={}))q.call(e,i)&&X(t,i,e[i]);if(z)for(var i of z(e))me.call(e,i)&&X(t,i,e[i]);return t},$=(t,e)=>fe(t,ge(e));var Ce=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports);var ve=(t,e,i,o)=>{if(e&&typeof e=="object"||typeof e=="function")for(let s of he(e))!q.call(t,s)&&s!==i&&j(t,s,{get:()=>e[s],enumerable:!(o=pe(e,s))||o.enumerable});return t};var ee=(t,e,i)=>(i=t!=null?de(Ae(t)):{},ve(e||!t||!t.__esModule?j(i,"default",{value:t,enumerable:!0}):i,t));var _=(t,e,i)=>new Promise((o,s)=>{var a=f=>{try{d(i.next(f))}catch(w){s(w)}},l=f=>{try{d(i.throw(f))}catch(w){s(w)}},d=f=>f.done?o(f.value):Promise.resolve(f.value).then(a,l);d((i=i.apply(t,e)).next())});var J=Ce((kt,te)=>{te.exports=Vue});var Pt=ee(J());function B(t){return weex.requireModule(t)}function r(t,e,...i){uni.__log__?uni.__log__(t,e,...i):console[t].apply(console,[...i,e])}function O(t,e){return typeof t=="string"?e:t}var V=(t,e)=>{let i=t.__vccOpts||t;for(let[o,s]of e)i[o]=s;return i};var n=ee(J());var we=Object.defineProperty,Se=(t,e,i)=>e in t?we(t,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[e]=i,A=(t,e,i)=>(Se(t,typeof e!="symbol"?e+"":e,i),i),h=class I{static getWFCPlatform(){if(I.platform>0)return I.platform;let e=uni.getSystemInfoSync();return r("log","at config.js:85","systemInfo",e),e.osName==="ios"||e.platform==="ios"?I.platform=e.deviceType!=="phone"?8:1:e.osName==="android"||e.platform==="android"?I.platform=e.deviceType!=="phone"?9:2:I.platform=0,I.platform}static config(e){Object.keys(e).forEach(i=>{I[i]=e[i]})}static urlRedirect(e){return e}static emojiBaseUrl(){return"https://static.wildfirechat.net/twemoji/assets/"}static stickerBaseUrl(){return"https://static.wildfirechat.net/sticker/"}};A(h,"ENABLE_AUTO_LOGIN",!0);A(h,"ENABLE_MULTI_VOIP_CALL",!0);A(h,"ENABLE_SINGLE_VOIP_CALL",!0);A(h,"DEFAULT_PORTRAIT_URL","data:image/png;base64,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");A(h,"DEFAULT_ORGANIZATION_PORTRAIT_URL","data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAABQCAYAAACOEfKtAAAACXBIWXMAAAsTAAALEwEAmpwYAAABlUlEQVR4nO2aIW7DQBBFc409UJRD9AoFJr1DeUADSrogoDws8EslloKCLeUEOcDWEzWtii133d2xH/h0ZD/9Pzs72lVSMBRGM1gBL/zJQAAUAK1kinCgAGg4UH4nASIsABoOVPkoEmEtBGDcN5PodFgvA+Dm+WMSfUFcDMDm/WK79jObANgC0HAgETZ6oDhEjENEnMKMMWIONAbplkHauIlwlTPuwprhMuFhd74vFHJpccuEDeus6UHHfXMHXcJVVTswARCACQcGIpwqaDP0QAHQcKAYY4wxpkLFJQ3Sp8M6+1ONp5fX3ztublUHMP64xYuqBZgyfpiXmgAUAA0Higibh35FDxQADQc6jltkjAkATDjQb9wiEQ6+AY5dGqQZ1MwKcHu8DNLj23nwz9ZeMyvA7toP0vb4/W5lDjUBeAWg4UARYRf9KtIDewB2ONBv3CIR7gHY4UC/cYtEuAdghwP/Z00UndTMskwY+worzaBmFoAoADBlNgIOFACtZGvBgQKg4UD5PeGJsABoOFDlo0iEVQbgDWanvolAkB8PAAAAAElFTkSuQmCC");A(h,"DEFAULT_DEPARTMENT_PORTRAIT_URL","data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAABQCAYAAACOEfKtAAAAAXNSR0IArs4c6QAAAIRlWElmTU0AKgAAAAgABQESAAMAAAABAAEAAAEaAAUAAAABAAAASgEbAAUAAAABAAAAUgEoAAMAAAABAAIAAIdpAAQAAAABAAAAWgAAAAAAAABIAAAAAQAAAEgAAAABAAOgAQADAAAAAQABAACgAgAEAAAAAQAAAFCgAwAEAAAAAQAAAFAAAAAAwtohTAAAAAlwSFlzAAALEwAACxMBAJqcGAAAAVlpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IlhNUCBDb3JlIDYuMC4wIj4KICAgPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4KICAgICAgPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIKICAgICAgICAgICAgeG1sbnM6dGlmZj0iaHR0cDovL25zLmFkb2JlLmNvbS90aWZmLzEuMC8iPgogICAgICAgICA8dGlmZjpPcmllbnRhdGlvbj4xPC90aWZmOk9yaWVudGF0aW9uPgogICAgICA8L3JkZjpEZXNjcmlwdGlvbj4KICAgPC9yZGY6UkRGPgo8L3g6eG1wbWV0YT4KGV7hBwAAAjlJREFUeAHt28FOwkAQgOGt8UF4Cg0nOJjo1bfgEfRkPekj8BZeOGjigRuRcNeTJ30NsW0g2UzatTNTpE3+JiS7dGba/dgAW0oIbAgggAACCCCAAAIIIIAAAgMTyI59vlcPq2lxDuVDtT3fjnNVwoGCTw9UV1N2us3CnSZhF5sbcjpP6QNgNajri7NWg/v4/A7vxaMv20lfTmSo5wGg85UDEECngDOdGQigU8CZzgwE0CngTGcGAugUcKYzAwF0CjjTmYEAOgWc6b25nPX0unYO5TjpfQBcZtvawY9+QhgV7zHL2r08mRYoLvXnl4+retp06r/u5UPEyQ0ggE4BZzozEECngDOdGQigU8CZ3smtHdbbM1LnXnyJnmZZmBRfsu9TcZZ9Xd4W0tVKxHp7RuP496+s8baPxrq7HflfAW33dwVYHe/lZrwfd9vjN8aVK5ES7xA1Gw9q2MGHiAEtTgEw1jC0ATSgxSkAxhqGNoAGtDgFwFjD0AbQgBanABhrGNoAGtDiFNXKIbHmTf4AlFp7DqVmjBa3tUu5xjVv+UoUvwBN4uJRO4/asjmUmvK8q74WsEqaz85ri8knF5uvsNi0+0vCUGrKMfIeKEWUfQCVYDIcQCmi7AOoBJPhAEoRZR9AJZgMB1CKKPsAKsFkOIBSRNkHUAkmwwGUIso+gEowGQ6gFFH2AVSCyXDT5azZ/E3WcfeHUlMOVAvY9JcEWVfTH0pNzZiIRQABBBBAAAEEEEAAAQQQ6K/ALz1vZTdxNVa5AAAAAElFTkSuQmCC");A(h,"DEFAULT_VIDEO_POSTER","data:image/png;base64,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");A(h,"APP_SERVER","https://app.wildfirechat.net");A(h,"ORGANIZATION_SERVER","https://org.wildfirechat.cn");A(h,"WORKSPACE_URL","https://open.wildfirechat.cn/work.html");A(h,"IM_SERVER_HOST","wildfirechat.net");A(h,"QR_CODE_PREFIX_PC_SESSION","wildfirechat://pcsession/");A(h,"ICE_SERVERS",[{uri:"turn:turn.wildfirechat.net:3478",userName:"wfchat",password:"wfchat123"}]);A(h,"LANGUAGE","zh_CN");A(h,"ENABLE_MULTI_CALL_AUTO_JOIN",!0);A(h,"ENABLE_PTT",!1);A(h,"ENABLE_VOIP",!0);A(h,"SDK_PLATFORM_WINDOWS",3);A(h,"SDK_PLATFORM_OSX",4);A(h,"SDK_PLATFORM_WEB",5);A(h,"SDK_PLATFORM_WX",6);A(h,"AMR_TO_MP3_SERVER_ADDRESS",h.APP_SERVER+"/amr2mp3?path=");A(h,"FILE_HELPER_ID","wfc_file_transfer");A(h,"RECALL_REEDIT_TIME_LIMIT",60);A(h,"platform",-1);var ie=h;var ke=Object.defineProperty,Pe=(t,e,i)=>e in t?ke(t,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[e]=i,c=(t,e,i)=>(Pe(t,typeof e!="symbol"?e+"":e,i),i),y=class{};c(y,"STATUS_IDLE",0);c(y,"STATUS_OUTGOING",1);c(y,"STATUS_INCOMING",2);c(y,"STATUS_CONNECTING",3);c(y,"STATUS_CONNECTED",4);var k=class{};c(k,"NONE",0);c(k,"BIG_STREAM",1);c(k,"SMALL_STREAM",2);var R=class{didCallEndWithReason(e){r("log","at wfc/av/engine/callSessionCallback.js:12","didCallEndWithReason",e)}didChangeState(e){r("log","at wfc/av/engine/callSessionCallback.js:24","didChangeState",e)}didParticipantJoined(e,i=!1){r("log","at wfc/av/engine/callSessionCallback.js:33","didParticipantJoined",e,i)}didParticipantConnected(e,i=!1){r("log","at wfc/av/engine/callSessionCallback.js:43","didParticipantConnected",e,i)}didParticipantLeft(e,i,o=!1){r("log","at wfc/av/engine/callSessionCallback.js:53","didParticipantLeft",e,i,o)}didChangeMode(e){r("log","at wfc/av/engine/callSessionCallback.js:61","didChangeMode",e)}didChangeInitiator(e){r("log","at wfc/av/engine/callSessionCallback.js:69","didChangeInitiator",e)}didCreateLocalVideoTrack(e){r("log","at wfc/av/engine/callSessionCallback.js:77","didCreateLocalVideoTrack",e)}didReceiveRemoteVideoTrack(e,i){r("log","at wfc/av/engine/callSessionCallback.js:86","didReceiveRemoteVideoTrack",e,i)}didRemoveRemoteVideoTrack(e){r("log","at wfc/av/engine/callSessionCallback.js:94","didRemoveRemoteVideoTrack",e)}didAudioDeviceChanged(e){r("log","at wfc/av/engine/callSessionCallback.js:99","didAudioDeviceChanged",e)}didError(e){r("log","at wfc/av/engine/callSessionCallback.js:103","didError",e)}didGetStats(e){}didVideoMuted(e,i){r("log","at wfc/av/engine/callSessionCallback.js:116","didVideoMuted",e,i)}didMuteStateChanged(e){r("log","at wfc/av/engine/callSessionCallback.js:125","didMuteStateChanged",e)}didMediaLostPacket(e,i,o=!1){r("log","at wfc/av/engine/callSessionCallback.js:135","didMediaLostPacket",e,i,o)}didUserMediaLostPacket(e,i,o,s,a=!1){r("log","at wfc/av/engine/callSessionCallback.js:147","didUserMediaLostPacket",e,i,o,s,a)}didChangeType(e,i,o=!1){r("log","at wfc/av/engine/callSessionCallback.js:158","didChangeType",e,i,o)}didReportAudioVolume(e,i){}onRequestChangeMode(e){r("log","at wfc/av/engine/callSessionCallback.js:176","onRequestChangeMode",e)}},S=class{};c(S,"Single",0);c(S,"Group",1);c(S,"ChatRoom",2);c(S,"Channel",3);c(S,"SecretChat",5);var g=class{constructor(){c(this,"callId"),c(this,"title"),c(this,"desc"),c(this,"initiator"),c(this,"inviter"),c(this,"state"),c(this,"startTime"),c(this,"connectedTime"),c(this,"endTime"),c(this,"conversation"),c(this,"audioOnly"),c(this,"endReason"),c(this,"conference"),c(this,"audience"),c(this,"advanced"),c(this,"multiCall"),c(this,"videoMuted"),c(this,"audioMuted")}};c(g,"kWFAVEngineStateIdle",0);c(g,"kWFAVEngineStateOutgoing",1);c(g,"kWFAVEngineStateIncomming",2);c(g,"kWFAVEngineStateConnecting",3);c(g,"kWFAVEngineStateConnected",4);c(g,"kWFAVCallEndReasonUnknown",0);c(g,"kWFAVCallEndReasonBusy",1);c(g,"kWFAVCallEndReasonSignalError",2);c(g,"kWFAVCallEndReasonHangup",3);c(g,"kWFAVCallEndReasonMediaError",4);c(g,"kWFAVCallEndReasonRemoteHangup",5);c(g,"kWFAVCallEndReasonOpenCameraFailure",6);c(g,"kWFAVCallEndReasonTimeout",7);c(g,"kWFAVCallEndReasonAcceptByOtherClient",8);c(g,"kWFAVCallEndReasonAllLeft",9);c(g,"kWFAVCallEndReasonRemoteBusy",10);c(g,"kWFAVCallEndReasonRemoteTimeout",11);c(g,"kWFAVCallEndReasonRemoteNetworkError",12);c(g,"kWFAVCallEndReasonRoomDestroyed",13);c(g,"kWFAVCallEndReasonRoomNotExist",14);c(g,"kWFAVCallEndReasonRoomParticipantsFull",15);var Q=class{constructor(){c(this,"innerAudioContext")}onReceiveCall(e){r("log","at wfc/av/engine/avengineCallback.js:8","onReceiveCall",e),typeof e=="string"&&(e=Object.assign(new g,JSON.parse(e)));let i;e.conversation.type===S.Single?i="/pages/voip/Single":e.conversation.type===S.Group&&(i="/pages/voip/Multi"),i+=`?session=${JSON.stringify(e)}`,i&&uni.navigateTo({url:i,success:o=>{r("log","at wfc/av/engine/avengineCallback.js:24",`navigate to ${i} success`),o.eventChannel.emit("options",{callSession:e})},fail:o=>{r("log","at wfc/av/engine/avengineCallback.js:30",`navigate to ${i} error`,o)}})}shouldStartRing(e){E.innerAudioContext=uni.createInnerAudioContext(),E.innerAudioContext.src=e?"/static/audios/incoming_call_ring.mp3":"/static/audios/outgoing_call_ring.mp3",E.innerAudioContext.autoplay=!0,E.innerAudioContext.loop=!0,E.innerAudioContext.play(),E.innerAudioContext.onPlay(()=>{r("log","at wfc/av/engine/avengineCallback.js:43","\u5F00\u59CB\u64AD\u653E")}),E.innerAudioContext.onError(i=>{r("error","at wfc/av/engine/avengineCallback.js:46","\u64AD\u653E\u54CD\u94C3\u5931\u8D25",i)})}shouldStopRing(){r("log","at wfc/av/engine/avengineCallback.js:51","shouldStopRing"),E.innerAudioContext.stop(),E.innerAudioContext.destroy(),E.innerAudioContext=null}didCallEnded(e,i){r("log","at wfc/av/engine/avengineCallback.js:59","didCallEnded",e,i);let o=getCurrentPages(),s="pages/voip/Single",a="pages/voip/Multi",l="pages/voip/conference/ConferencePage",d=o[o.length-1].route;(d===s||d===a||d===l)&&uni.navigateBack({delta:1,fail:f=>{r("log","at wfc/av/engine/avengineCallback.js:70","nav back to conversationView err",f)}})}},E=new Q,x=class{constructor(){c(this,"userId"),c(this,"callExtra"),c(this,"state"),c(this,"joinTime",0),c(this,"acceptTime",0),c(this,"audioMuted",!1),c(this,"videoMuted",!1),c(this,"audience",!1),c(this,"screenSharing",!1)}},p=ie.ENABLE_VOIP?B("wf-uni-wfc-avclient"):null,H=class{constructor(){c(this,"avengineCallback",E),c(this,"sessionCallback")}isAVEngineKitEnable(){return!!p}init(){p.initAVEngineKit(),plus.globalEvent.addEventListener("wfc-av-event",e=>{m._handleNativeAVEngineEvent(e)}),plus.globalEvent.addEventListener("wfc-av-session-event",e=>{m._handleNativeCallSessionEvent(e)})}_handleNativeAVEngineEvent(e){let i=e.args;if(this.avengineCallback){let o=this.avengineCallback[i[0]];o&&o(...i.slice(1))}else r("warn","at wfc/av/engine/avengineKit.js:47","_handleNativeAVEngineEvent avengineCallback is null",i)}_handleNativeCallSessionEvent(e){let i=e.args;if(i[0]!=="didReportAudioVolume"&&r("log","at wfc/av/engine/avengineKit.js:54","_handleNativeCallSessionEvent",i),i[0]==="resumeVoipPage"){let o=this.currentCallSession();o&&this._resumeVoipPage(o);return}if(this.sessionCallback){let o=this.sessionCallback[i[0]];o&&(i[0]==="didChangeState"||i[0]==="didCallEndWithReason"?o(Number(i[1])):o(...i.slice(1)))}else r("warn","at wfc/av/engine/avengineKit.js:76","_handleNativeCallSessionEvent sessionCallback is null",i)}_resumeVoipPage(e){r("log","at wfc/av/engine/avengineKit.js:81","_resumeVoipPage",e);let i;e.conference?i="/pages/voip/conference/ConferencePage":e.conversation.type===S.Single?i="/pages/voip/Single":e.conversation.type===S.Group&&(i="/pages/voip/Multi"),i+=`?session=${JSON.stringify(e)}`,i&&uni.navigateTo({url:i,success:o=>{r("log","at wfc/av/engine/avengineKit.js:95",`navigate to ${i} success`)},fail:o=>{r("log","at wfc/av/engine/avengineKit.js:98",`navigate to ${i} error`,o)}})}setSessionCallback(e){this.sessionCallback=e}startSingleCall(e,i){let o=p.startSingleCall(e,i);return o?Object.assign(new g,JSON.parse(o)):null}startMultiCall(e,i,o){let s=p.startMultiCall(e,i,o);return s?Object.assign(new g,JSON.parse(s)):null}startConference(e,i,o,s,a,l,d,f,w=!1,T=""){let u=p.startConference(e,i,o,s,a,l,d,f,w,T);return u?Object.assign(new g,JSON.parse(u)):null}joinConference(e,i,o,s,a,l,d,f,w,T,u=""){let Z=p.joinConference(e,i,o,s,a,l,d,f,w,T,u);return Z?Object.assign(new g,JSON.parse(Z)):null}leaveConference(e,i=!1){p.leaveConference(e,i)}kickoffParticipant(e,i,o,s){p.kickoffParticipant(e,i,()=>{o&&o()},a=>{s&&s(a)})}setParticipantVideoType(e,i,o,s){r("log","at wfc/av/engine/avengineKit.js:207","setParticipantVideoType",i,o,s),p.setParticipantVideoType(e,i,o,s)}isSupportMultiCall(){return p.isSupportMultiCall()}isSupportConference(){return p.isSupportConference()}setVideoProfile(e,i=!1){p.setVideoProfile(e,i)}addICEServer(e,i,o){p.addICEServer(e,i,o)}currentCallSession(){let e=p.currentCallSession();return e===""?null:Object.assign(new g,JSON.parse(e))}answerCall(e,i){p.answerCall(e,i)}endCall(e){p.endCall(e)}muteVideo(e,i){p.muteVideo(e,i)}muteAudio(e,i){p.muteAudio(e,i)}switchAudience(e,i){return p.switchAudience(e,i)}downgrade2Voice(e){p.downgrade2Voice(e)}inviteNewParticipant(e,i){p.inviteNewParticipant(e,i)}setLocalVideoView(e,i){p.setLocalVideoView(e,i)}setRemoteVideoView(e,i,o,s=!1){p.setRemoteVideoView(e,i,s,o)}getParticipantProfiles(e){let i=p.getParticipantProfiles(e);if(!i)return[];let o=[];return JSON.parse(i).map(a=>{o.push(Object.assign(new x,a))}),o}getParticipantProfile(e,i,o){let s=p.getParticipantProfile(e,i,o);return s?Object.assign(new x,JSON.parse(s)):null}getMyProfile(e){let i=p.getMyProfile(e);return i?Object.assign(new x,JSON.parse(i)):null}checkOverlayPermission(){return p.checkOverlayPermission()}minimize(e,i=""){p.minimize(e,i),this.sessionCallback=null}setSpeakerOn(e,i){p.setSpeakerOn(e,i)}switchCamera(e){p.switchCamera(e)}},m=new H,ne="/static/image/av/av_conference_video.png",se="/static/image/av/av_conference_video_mute.png",oe="/static/image/av/av_minimize.png";var ye=Object.defineProperty,Ee=(t,e,i)=>e in t?ye(t,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[e]=i,P=(t,e,i)=>(Ee(t,typeof e!="symbol"?e+"":e,i),i),U=class{constructor(e,i,o=0){P(this,"type",S.Single),P(this,"conversationType",this.type),P(this,"target",""),P(this,"line",0),this.type=e,this.conversationType=e,this.target=i,this.line=o}equal(e){return e?this.type===e.type&&this.target===e.target&&this.line===e.line:!1}},D=class{constructor(){P(this,"chatRoomId"),P(this,"title"),P(this,"desc"),P(this,"portrait"),P(this,"extra"),P(this,"state"),P(this,"memberCount"),P(this,"createDt"),P(this,"updateDt")}};var Ie=Object.defineProperty,Te=(t,e,i)=>e in t?Ie(t,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[e]=i,v=(t,e,i)=>(Te(t,typeof e!="symbol"?e+"":e,i),i),Ve={data(){return{}},created(){this.popup=this.getParent()},methods:{getParent(t="uniPopup"){let e=this.$parent,i=e.$options.name;for(;i!==t;){if(e=e.$parent,!e)return!1;i=e.$options.name}return e}}},be=t=>t!==null&&typeof t=="object",xe=["{","}"],K=class{constructor(){this._caches=Object.create(null)}interpolate(e,i,o=xe){if(!i)return[e];let s=this._caches[e];return s||(s=Be(e,o),this._caches[e]=s),Oe(s,i)}},Me=/^(?:\d)+/,_e=/^(?:\w)+/;function Be(t,[e,i]){let o=[],s=0,a="";for(;s<t.length;){let l=t[s++];if(l===e){a&&o.push({type:"text",value:a}),a="";let d="";for(l=t[s++];l!==void 0&&l!==i;)d+=l,l=t[s++];let f=l===i,w=Me.test(d)?"list":f&&_e.test(d)?"named":"unknown";o.push({value:d,type:w})}else a+=l}return a&&o.push({type:"text",value:a}),o}function Oe(t,e){let i=[],o=0,s=Array.isArray(e)?"list":be(e)?"named":"unknown";if(s==="unknown")return i;for(;o<t.length;){let a=t[o];switch(a.type){case"text":i.push(a.value);break;case"list":i.push(e[parseInt(a.value,10)]);break;case"named":s==="named"&&i.push(e[a.value]);break}o++}return i}var G="zh-Hans",ae="zh-Hant",b="en",Re="fr",Ue="es",De=Object.prototype.hasOwnProperty,re=(t,e)=>De.call(t,e),Ne=new K;function Le(t,e){return!!e.find(i=>t.indexOf(i)!==-1)}function Fe(t,e){return e.find(i=>t.indexOf(i)===0)}function le(t,e){if(!t)return;if(t=t.trim().replace(/_/g,"-"),e&&e[t])return t;if(t=t.toLowerCase(),t==="chinese")return G;if(t.indexOf("zh")===0)return t.indexOf("-hans")>-1?G:t.indexOf("-hant")>-1||Le(t,["-tw","-hk","-mo","-cht"])?ae:G;let i=[b,Re,Ue];e&&Object.keys(e).length>0&&(i=Object.keys(e));let o=Fe(t,i);if(o)return o}var Y=class{constructor({locale:e,fallbackLocale:i,messages:o,watcher:s,formater:a}){this.locale=b,this.fallbackLocale=b,this.message={},this.messages={},this.watchers=[],i&&(this.fallbackLocale=i),this.formater=a||Ne,this.messages=o||{},this.setLocale(e||b),s&&this.watchLocale(s)}setLocale(e){let i=this.locale;this.locale=le(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],i!==this.locale&&this.watchers.forEach(o=>{o(this.locale,i)})}getLocale(){return this.locale}watchLocale(e){let i=this.watchers.push(e)-1;return()=>{this.watchers.splice(i,1)}}add(e,i,o=!0){let s=this.messages[e];s?o?Object.assign(s,i):Object.keys(i).forEach(a=>{re(s,a)||(s[a]=i[a])}):this.messages[e]=i}f(e,i,o){return this.formater.interpolate(e,i,o).join("")}t(e,i,o){let s=this.message;return typeof i=="string"?(i=le(i,this.messages),i&&(s=this.messages[i])):o=i,re(s,e)?this.formater.interpolate(s[e],o).join(""):(console.warn(`Cannot translate the value of keypath ${e}. Use the value of keypath as default.`),e)}};function je(t,e){t.$watchLocale?t.$watchLocale(i=>{e.setLocale(i)}):t.$watch(()=>t.$locale,i=>{e.setLocale(i)})}function Je(){return typeof uni!="undefined"&&uni.getLocale?uni.getLocale():typeof global!="undefined"&&global.getLocale?global.getLocale():b}function Qe(t,e={},i,o){if(typeof t!="string"){let l=[e,t];t=l[0],e=l[1]}typeof t!="string"&&(t=Je()),typeof i!="string"&&(i=typeof __uniConfig!="undefined"&&__uniConfig.fallbackLocale||b);let s=new Y({locale:t,fallbackLocale:i,messages:e,watcher:o}),a=(l,d)=>{if(typeof getApp!="function")a=function(f,w){return s.t(f,w)};else{let f=!1;a=function(w,T){let u=getApp().$vm;return u&&(u.$locale,f||(f=!0,je(u,s))),s.t(w,T)}}return a(l,d)};return{i18n:s,f(l,d,f){return s.f(l,d,f)},t(l,d){return a(l,d)},add(l,d,f=!0){return s.add(l,d,f)},watch(l){return s.watchLocale(l)},getLocale(){return s.getLocale()},setLocale(l){return s.setLocale(l)}}}var He={"uni-popup.cancel":"cancel","uni-popup.ok":"ok","uni-popup.placeholder":"pleace enter","uni-popup.title":"Hint","uni-popup.shareTitle":"Share to"},Ge={"uni-popup.cancel":"\u53D6\u6D88","uni-popup.ok":"\u786E\u5B9A","uni-popup.placeholder":"\u8BF7\u8F93\u5165","uni-popup.title":"\u63D0\u793A","uni-popup.shareTitle":"\u5206\u4EAB\u5230"},Ke={"uni-popup.cancel":"\u53D6\u6D88","uni-popup.ok":"\u78BA\u5B9A","uni-popup.placeholder":"\u8ACB\u8F38\u5165","uni-popup.title":"\u63D0\u793A","uni-popup.shareTitle":"\u5206\u4EAB\u5230"},Ye={en:He,"zh-Hans":Ge,"zh-Hant":Ke},We={"uni-popup-dialog":{"":{width:300,borderRadius:11,backgroundColor:"#ffffff"}},"uni-dialog-title":{"":{flexDirection:"row",justifyContent:"center",paddingTop:25}},"uni-dialog-title-text":{"":{fontSize:16,fontWeight:"500"}},"uni-dialog-content":{"":{flexDirection:"row",justifyContent:"center",alignItems:"center",paddingTop:20,paddingRight:20,paddingBottom:20,paddingLeft:20}},"uni-dialog-content-text":{"":{fontSize:14,color:"#6C6C6C"}},"uni-dialog-button-group":{"":{flexDirection:"row",borderTopColor:"#f5f5f5",borderTopStyle:"solid",borderTopWidth:1}},"uni-dialog-button":{"":{flex:1,flexDirection:"row",justifyContent:"center",alignItems:"center",height:45}},"uni-border-left":{"":{borderLeftColor:"#f0f0f0",borderLeftStyle:"solid",borderLeftWidth:1}},"uni-dialog-button-text":{"":{fontSize:16,color:"#333333"}},"uni-button-color":{"":{color:"#007aff"}},"uni-dialog-input":{"":{flex:1,fontSize:14,borderWidth:1,borderStyle:"solid",borderColor:"#eeeeee",height:40,paddingTop:0,paddingRight:10,paddingBottom:0,paddingLeft:10,borderRadius:5,color:"#555555"}},"uni-popup__success":{"":{color:"#4cd964"}},"uni-popup__warn":{"":{color:"#f0ad4e"}},"uni-popup__error":{"":{color:"#dd524d"}},"uni-popup__info":{"":{color:"#909399"}}},{t:N}=Qe(Ye),Ze={name:"uniPopupDialog",mixins:[Ve],emits:["confirm","close"],props:{inputType:{type:String,default:"text"},value:{type:[String,Number],default:""},placeholder:{type:[String,Number],default:""},type:{type:String,default:"error"},mode:{type:String,default:"base"},title:{type:String,default:""},content:{type:String,default:""},beforeClose:{type:Boolean,default:!1},cancelText:{type:String,default:""},confirmText:{type:String,default:""}},data(){return{dialogType:"error",focus:!1,val:""}},computed:{okText(){return this.confirmText||N("uni-popup.ok")},closeText(){return this.cancelText||N("uni-popup.cancel")},placeholderText(){return this.placeholder||N("uni-popup.placeholder")},titleText(){return this.title||N("uni-popup.title")}},watch:{type(t){this.dialogType=t},mode(t){t==="input"&&(this.dialogType="info")},value(t){this.val=t}},created(){this.popup.disableMask(),this.mode==="input"?(this.dialogType="info",this.val=this.value):this.dialogType=this.type},mounted(){this.focus=!0},methods:{onOk(){this.mode==="input"?this.$emit("confirm",this.val):this.$emit("confirm"),!this.beforeClose&&this.popup.close()},closeDialog(){this.$emit("close"),!this.beforeClose&&this.popup.close()},close(){this.popup.close()}}};function ze(t,e,i,o,s,a){return(0,n.openBlock)(),(0,n.createElementBlock)("view",{class:"uni-popup-dialog",renderWhole:!0},[(0,n.createElementVNode)("view",{class:"uni-dialog-title"},[(0,n.createElementVNode)("u-text",{class:(0,n.normalizeClass)(["uni-dialog-title-text",["uni-popup__"+s.dialogType]])},(0,n.toDisplayString)(a.titleText),3)]),i.mode==="base"?((0,n.openBlock)(),(0,n.createElementBlock)("view",{key:0,class:"uni-dialog-content"},[(0,n.renderSlot)(t.$slots,"default",{},()=>[(0,n.createElementVNode)("u-text",{class:"uni-dialog-content-text"},(0,n.toDisplayString)(i.content),1)])])):((0,n.openBlock)(),(0,n.createElementBlock)("view",{key:1,class:"uni-dialog-content"},[(0,n.renderSlot)(t.$slots,"default",{},()=>[(0,n.createElementVNode)("u-input",{class:"uni-dialog-input",modelValue:s.val,onInput:e[0]||(e[0]=l=>s.val=l.detail.value),type:i.inputType,placeholder:a.placeholderText,focus:s.focus},null,40,["modelValue","type","placeholder","focus"])])])),(0,n.createElementVNode)("view",{class:"uni-dialog-button-group"},[(0,n.createElementVNode)("view",{class:"uni-dialog-button",onClick:e[1]||(e[1]=(...l)=>a.closeDialog&&a.closeDialog(...l))},[(0,n.createElementVNode)("u-text",{class:"uni-dialog-button-text"},(0,n.toDisplayString)(a.closeText),1)]),(0,n.createElementVNode)("view",{class:"uni-dialog-button uni-border-left",onClick:e[2]||(e[2]=(...l)=>a.onOk&&a.onOk(...l))},[(0,n.createElementVNode)("u-text",{class:"uni-dialog-button-text uni-button-color"},(0,n.toDisplayString)(a.okText),1)])])])}var Xe=V(Ze,[["render",ze],["styles",[We]]]),qe=B("animation"),L=class{constructor(e,i){this.options=e,this.animation=uni.createAnimation(M({},e)),this.currentStepAnimates={},this.next=0,this.$=i}_nvuePushAnimates(e,i){let o=this.currentStepAnimates[this.next],s={};if(o?s=o:s={styles:{},config:{}},ue.includes(e)){s.styles.transform||(s.styles.transform="");let a="";e==="rotate"&&(a="deg"),s.styles.transform+=`${e}(${i+a}) `}else s.styles[e]=`${i}`;this.currentStepAnimates[this.next]=s}_animateRun(e={},i={}){let o=this.$.$refs.ani.ref;if(o)return new Promise((s,a)=>{qe.transition(o,M({styles:e},i),l=>{s()})})}_nvueNextAnimate(e,i=0,o){let s=e[i];if(s){let{styles:a,config:l}=s;this._animateRun(a,l).then(()=>{i+=1,this._nvueNextAnimate(e,i,o)})}else this.currentStepAnimates={},typeof o=="function"&&o(),this.isEnd=!0}step(e={}){return this.currentStepAnimates[this.next].config=Object.assign({},this.options,e),this.currentStepAnimates[this.next].styles.transformOrigin=this.currentStepAnimates[this.next].config.transformOrigin,this.next++,this}run(e){this.isEnd=!1,this.$.$refs.ani&&this.$.$refs.ani.ref&&(this._nvueNextAnimate(this.currentStepAnimates,0,e),this.next=0)}},ue=["matrix","matrix3d","rotate","rotate3d","rotateX","rotateY","rotateZ","scale","scale3d","scaleX","scaleY","scaleZ","skew","skewX","skewY","translate","translate3d","translateX","translateY","translateZ"],$e=["opacity","backgroundColor"],et=["width","height","left","right","top","bottom"];ue.concat($e,et).forEach(t=>{L.prototype[t]=function(...e){return this._nvuePushAnimates(t,e),this}});function ce(t,e){if(e)return clearTimeout(e.timer),new L(t,e)}var tt={name:"uniTransition",emits:["click","change"],props:{show:{type:Boolean,default:!1},modeClass:{type:[Array,String],default(){return"fade"}},duration:{type:Number,default:300},styles:{type:Object,default(){return{}}},customClass:{type:String,default:""},onceRender:{type:Boolean,default:!1}},data(){return{isShow:!1,transform:"",opacity:1,animationData:{},durationTime:300,config:{}}},watch:{show:{handler(t){t?this.open():this.isShow&&this.close()},immediate:!0}},computed:{stylesObject(){let t=$(M({},this.styles),{"transition-duration":this.duration/1e3+"s"}),e="";for(let i in t){let o=this.toLine(i);e+=o+":"+t[i]+";"}return e},transformStyles(){return"transform:"+this.transform+";opacity:"+this.opacity+";"+this.stylesObject}},created(){this.config={duration:this.duration,timingFunction:"ease",transformOrigin:"50% 50%",delay:0},this.durationTime=this.duration},methods:{init(t={}){t.duration&&(this.durationTime=t.duration),this.animation=ce(Object.assign(this.config,t),this)},onClick(){this.$emit("click",{detail:this.isShow})},step(t,e={}){if(this.animation){for(let i in t)try{typeof t[i]=="object"?this.animation[i](...t[i]):this.animation[i](t[i])}catch(o){r("error","at uni_modules/uni-transition/components/uni-transition/uni-transition.vue:148",`\u65B9\u6CD5 ${i} \u4E0D\u5B58\u5728`)}return this.animation.step(e),this}},run(t){this.animation&&this.animation.run(t)},open(){clearTimeout(this.timer),this.transform="",this.isShow=!0;let{opacity:t,transform:e}=this.styleInit(!1);typeof t!="undefined"&&(this.opacity=t),this.transform=e,this.$nextTick(()=>{this.timer=setTimeout(()=>{this.animation=ce(this.config,this),this.tranfromInit(!1).step(),this.animation.run(),this.$emit("change",{detail:this.isShow})},20)})},close(t){this.animation&&this.tranfromInit(!0).step().run(()=>{this.isShow=!1,this.animationData=null,this.animation=null;let{opacity:e,transform:i}=this.styleInit(!1);this.opacity=e||1,this.transform=i,this.$emit("change",{detail:this.isShow})})},styleInit(t){let e={transform:""},i=(o,s)=>{s==="fade"?e.opacity=this.animationType(o)[s]:e.transform+=this.animationType(o)[s]+" "};return typeof this.modeClass=="string"?i(t,this.modeClass):this.modeClass.forEach(o=>{i(t,o)}),e},tranfromInit(t){let e=(i,o)=>{let s=null;o==="fade"?s=i?0:1:(s=i?"-100%":"0",o==="zoom-in"&&(s=i?.8:1),o==="zoom-out"&&(s=i?1.2:1),o==="slide-right"&&(s=i?"100%":"0"),o==="slide-bottom"&&(s=i?"100%":"0")),this.animation[this.animationMode()[o]](s)};return typeof this.modeClass=="string"?e(t,this.modeClass):this.modeClass.forEach(i=>{e(t,i)}),this.animation},animationType(t){return{fade:t?1:0,"slide-top":`translateY(${t?"0":"-100%"})`,"slide-right":`translateX(${t?"0":"100%"})`,"slide-bottom":`translateY(${t?"0":"100%"})`,"slide-left":`translateX(${t?"0":"-100%"})`,"zoom-in":`scaleX(${t?1:.8}) scaleY(${t?1:.8})`,"zoom-out":`scaleX(${t?1:1.2}) scaleY(${t?1:1.2})`}},animationMode(){return{fade:"opacity","slide-top":"translateY","slide-right":"translateX","slide-bottom":"translateY","slide-left":"translateX","zoom-in":"scale","zoom-out":"scale"}},toLine(t){return t.replace(/([A-Z])/g,"-$1").toLowerCase()}}};function it(t,e,i,o,s,a){return s.isShow?((0,n.openBlock)(),(0,n.createElementBlock)("view",{key:0,ref:"ani",animation:s.animationData,class:(0,n.normalizeClass)(i.customClass),style:(0,n.normalizeStyle)(a.transformStyles),onClick:e[0]||(e[0]=(...l)=>a.onClick&&a.onClick(...l)),renderWhole:!0},[(0,n.renderSlot)(t.$slots,"default")],14,["animation"])):(0,n.createCommentVNode)("",!0)}var nt=V(tt,[["render",it]]),st={"uni-popup":{"":{position:"fixed"},".top":{top:0},".left":{top:0},".right":{top:0}},"uni-popup__wrapper":{".uni-popup ":{position:"relative"},".uni-popup .left":{paddingTop:0,flex:1},".uni-popup .right":{paddingTop:0,flex:1}},"fixforpc-top":{"":{top:0}}},ot={name:"uniPopup",components:{},emits:["change","maskClick"],props:{animation:{type:Boolean,default:!0},type:{type:String,default:"center"},isMaskClick:{type:Boolean,default:null},maskClick:{type:Boolean,default:null},backgroundColor:{type:String,default:"none"},safeArea:{type:Boolean,default:!0},maskBackgroundColor:{type:String,default:"rgba(0, 0, 0, 0.4)"}},watch:{type:{handler:function(t){this.config[t]&&this[this.config[t]](!0)},immediate:!0},isDesktop:{handler:function(t){this.config[t]&&this[this.config[this.type]](!0)},immediate:!0},maskClick:{handler:function(t){this.mkclick=t},immediate:!0},isMaskClick:{handler:function(t){this.mkclick=t},immediate:!0},showPopup(t){}},data(){return{duration:300,ani:[],showPopup:!1,showTrans:!1,popupWidth:0,popupHeight:0,config:{top:"top",bottom:"bottom",center:"center",left:"left",right:"right",message:"top",dialog:"center",share:"bottom"},maskClass:{position:"fixed",bottom:0,top:0,left:0,right:0,backgroundColor:"rgba(0, 0, 0, 0.4)"},transClass:{position:"fixed",left:0,right:0},maskShow:!0,mkclick:!0,popupstyle:this.isDesktop?"fixforpc-top":"top"}},computed:{isDesktop(){return this.popupWidth>=500&&this.popupHeight>=500},bg(){return this.backgroundColor===""||this.backgroundColor==="none"?"transparent":this.backgroundColor}},mounted(){(()=>{let{windowWidth:e,windowHeight:i,windowTop:o,safeArea:s,screenHeight:a,safeAreaInsets:l}=uni.getSystemInfoSync();this.popupWidth=e,this.popupHeight=i+(o||0),s&&this.safeArea?this.safeAreaInsets=l.bottom:this.safeAreaInsets=0})()},unmounted(){this.setH5Visible()},created(){this.isMaskClick===null&&this.maskClick===null?this.mkclick=!0:this.mkclick=this.isMaskClick!==null?this.isMaskClick:this.maskClick,this.animation?this.duration=300:this.duration=0,this.messageChild=null,this.clearPropagation=!1,this.maskClass.backgroundColor=this.maskBackgroundColor},methods:{setH5Visible(){},closeMask(){this.maskShow=!1},disableMask(){this.mkclick=!1},clear(t){this.clearPropagation=!0},open(t){if(this.showPopup)return;if(t&&["top","center","bottom","left","right","message","dialog","share"].indexOf(t)!==-1||(t=this.type),!this.config[t]){r("error","at uni_modules/uni-popup/components/uni-popup/uni-popup.vue:279","\u7F3A\u5C11\u7C7B\u578B\uFF1A",t);return}this[this.config[t]](),this.$emit("change",{show:!0,type:t})},close(t){this.showTrans=!1,this.$emit("change",{show:!1,type:this.type}),clearTimeout(this.timer),this.timer=setTimeout(()=>{this.showPopup=!1},300)},touchstart(){this.clearPropagation=!1},onTap(){if(this.clearPropagation){this.clearPropagation=!1;return}this.$emit("maskClick"),this.mkclick&&this.close()},top(t){this.popupstyle=this.isDesktop?"fixforpc-top":"top",this.ani=["slide-top"],this.transClass={position:"fixed",left:0,right:0,backgroundColor:this.bg},!t&&(this.showPopup=!0,this.showTrans=!0,this.$nextTick(()=>{this.messageChild&&this.type==="message"&&this.messageChild.timerClose()}))},bottom(t){this.popupstyle="bottom",this.ani=["slide-bottom"],this.transClass={position:"fixed",left:0,right:0,bottom:0,paddingBottom:this.safeAreaInsets+"px",backgroundColor:this.bg},!t&&(this.showPopup=!0,this.showTrans=!0)},center(t){this.popupstyle="center",this.ani=["zoom-out","fade"],this.transClass={position:"fixed",bottom:0,left:0,right:0,top:0,justifyContent:"center",alignItems:"center"},!t&&(this.showPopup=!0,this.showTrans=!0)},left(t){this.popupstyle="left",this.ani=["slide-left"],this.transClass={position:"fixed",left:0,bottom:0,top:0,backgroundColor:this.bg},!t&&(this.showPopup=!0,this.showTrans=!0)},right(t){this.popupstyle="right",this.ani=["slide-right"],this.transClass={position:"fixed",bottom:0,right:0,top:0,backgroundColor:this.bg},!t&&(this.showPopup=!0,this.showTrans=!0)}}};function at(t,e,i,o,s,a){let l=O((0,n.resolveDynamicComponent)("uni-transition"),nt);return s.showPopup?((0,n.openBlock)(),(0,n.createElementBlock)("view",{key:0,class:(0,n.normalizeClass)(["uni-popup",[s.popupstyle,a.isDesktop?"fixforpc-z-index":""]]),renderWhole:!0},[(0,n.createElementVNode)("view",{onTouchstart:e[1]||(e[1]=(...d)=>a.touchstart&&a.touchstart(...d))},[s.maskShow?((0,n.openBlock)(),(0,n.createBlock)(l,{key:"1",name:"mask","mode-class":"fade",styles:s.maskClass,duration:s.duration,show:s.showTrans,onClick:a.onTap},null,8,["styles","duration","show","onClick"])):(0,n.createCommentVNode)("",!0),(0,n.createVNode)(l,{key:"2","mode-class":s.ani,name:"content",styles:s.transClass,duration:s.duration,show:s.showTrans,onClick:a.onTap},{default:(0,n.withCtx)(()=>[(0,n.createElementVNode)("view",{class:(0,n.normalizeClass)(["uni-popup__wrapper",[s.popupstyle]]),style:(0,n.normalizeStyle)({backgroundColor:a.bg}),onClick:e[0]||(e[0]=(...d)=>a.clear&&a.clear(...d))},[(0,n.renderSlot)(t.$slots,"default")],6)]),_:3},8,["mode-class","styles","duration","show","onClick"])],32)],2)):(0,n.createCommentVNode)("",!0)}var rt=V(ot,[["render",at],["styles",[st]]]),C=class{};v(C,"REASON_Unknown",0);v(C,"REASON_Busy",1);v(C,"REASON_SignalError",2);v(C,"REASON_Hangup",3);v(C,"REASON_MediaError",4);v(C,"REASON_RemoteHangup",5);v(C,"REASON_OpenCameraFailure",6);v(C,"REASON_Timeout",7);v(C,"REASON_AcceptByOtherClient",8);v(C,"REASON_AllLeft",9);v(C,"RemoteBusy",10);v(C,"RemoteTimeout",11);v(C,"RemoteNetworkError",12);v(C,"RoomDestroyed",13);v(C,"RoomNotExist",14);v(C,"RoomParticipantsFull",15);v(C,"Interrupted",16);v(C,"RemoteInterrupted",17);var lt={iconfont:{"":{fontFamily:"icomoon"}},"participant-video-item":{"":{display:"flex",position:"relative",width:200,height:200,flexDirection:"column",justifyContent:"center",alignItems:"center",borderWidth:1,borderStyle:"solid",borderColor:"#000000",backgroundColor:"#2d3033"},".highlight":{borderWidth:2,borderStyle:"solid",borderColor:"#1FCA6A"}},video:{".participant-video-item ":{width:200,height:200}},"video-stream-tip-container":{".participant-video-item ":{position:"absolute",top:0,left:0}},"avatar-container":{".participant-video-item ":{width:100,height:100,display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"center",backgroundColor:"#2d3033"}},avatar:{"":{width:80,height:80,borderRadius:50}},"info-container":{".participant-video-item ":{position:"absolute",left:0,bottom:0,display:"flex",flexDirection:"row",backgroundColor:"#808080",borderRadius:1,paddingTop:5,paddingRight:10,paddingBottom:5,paddingLeft:10,justifyContent:"center",alignItems:"center",textAlign:"center"}},name:{".info-container ":{height:20,lineHeight:20,textAlign:"center",fontSize:14,paddingLeft:5}}},ct={name:"ConferenceParticipantVideoView",props:{currentPageParticipants:{type:Object,required:!0},participant:{type:Object,required:!0},session:{type:Object,required:!0},selfUserInfo:{type:Object,required:!0}},data(){return{status:y.STATUS_CONNECTING,setupVideoViewTimer:0}},created(){r("log","at pages/voip/conference/ConferenceParticipantVideoView.nvue:59","videoView created",this.participant),this.selfUserId!==this.participant.uid&&(this.participant._isVideoMuted||m.setParticipantVideoType(this.session.callId,this.participant.uid,this.participant._isScreenSharing,k.BIG_STREAM))},unmounted(){r("log","at pages/voip/conference/ConferenceParticipantVideoView.nvue:67","videoView unmounted",this.participant.uid),this.selfUserInfo.uid!==this.participant.uid&&(this.participant._isVideoMuted||m.setParticipantVideoType(this.session.callId,this.participant.uid,this.participant._isScreenSharing,k.NONE))},mounted(){this.setupVideoView()},updated(){r("log","at pages/voip/conference/ConferenceParticipantVideoView.nvue:79","view updated, re-setupVideoView"),this.$nextTick(()=>{this.setupVideoView()})},methods:{setupVideoView(){if(this.participant._isAudience||this.participant._isVideoMuted)return;let t=!1;this.$refs.remoteVideoView&&(t=!0),t?(r("log","at pages/voip/conference/ConferenceParticipantVideoView.nvue:110","videoViews are ready, setup"),this.setupVideoViewTimer&&(clearTimeout(this.setupVideoViewTimer),this.setupVideoViewTimer=0),this.participant._isVideoMuted||(this.participant.uid===this.selfUserInfo.uid?m.setLocalVideoView(this.session.callId,this.$refs.remoteVideoView.ref):m.setRemoteVideoView(this.session.callId,this.participant.uid,this.$refs.remoteVideoView.ref,!1))):(r("log","at pages/voip/conference/ConferenceParticipantVideoView.nvue:123","videoViews are not ready, setTimeout"),clearTimeout(this.setupVideoViewTimer),this.setupVideoViewTimer=setTimeout(this.setupVideoView,100))},switchVideoType(t,e){if(!this.session)return;let i=k.NONE;i=k.SMALL_STREAM,m.setParticipantVideoType(this.session.callId,t,e,i)}},computed:{userName(){let t="",e=this.participant;return e.groupAlias?t=e.groupAlias:e.friendAlias?t=e.friendAlias:e.displayName?t=e.displayName:t=e.name,t},isVideoMuted(){return this.participant._isAudience||this.participant._isVideoMuted},computedParticipantSizeStyle(){let t,e;return this.currentPageParticipants.length===1?(t="750rpx",e="400rpx"):this.currentPageParticipants.length<=4?(t="375rpx",e="375rpx"):(t="250rpx",e="250rpx"),{width:t,height:e}}},watch:{isVideoMuted:{handler(t,e){r("log","at pages/voip/conference/ConferenceParticipantVideoView.nvue:188","watch participant",e,t),e&&!t&&this.$nextTick(()=>{r("log","at pages/voip/conference/ConferenceParticipantVideoView.nvue:191","watch participant setupVideoView",this.$refs.remoteVideoView.ref),this.setupVideoView()})}}}};function ut(t,e,i,o,s,a){return(0,n.resolveComponent)("p"),(0,n.openBlock)(),(0,n.createElementBlock)("div",{class:(0,n.normalizeClass)(["participant-video-item",{highlight:i.participant._volume>0}]),style:(0,n.normalizeStyle)(a.computedParticipantSizeStyle)},[!i.participant._isAudience&&!i.participant._isVideoMuted?((0,n.openBlock)(),(0,n.createElementBlock)("UIKit-Video-CallView",{key:0,onDblclick:e[0]||(e[0]=(...l)=>t.onDbClickVideo&&t.onDbClickVideo(...l)),ref:"remoteVideoView",style:(0,n.normalizeStyle)(a.computedParticipantSizeStyle),class:"video"},null,36)):((0,n.openBlock)(),(0,n.createElementBlock)("div",{key:1,class:"avatar-container"},[(0,n.createElementVNode)("u-image",{class:"avatar",src:i.participant.portrait,alt:i.participant},null,8,["src","alt"])])),(0,n.createCommentVNode)("",!0),(0,n.createElementVNode)("div",{class:"info-container"},[i.participant._isHost?((0,n.openBlock)(),(0,n.createElementBlock)("u-text",{key:0,class:"iconfont icon-ion-person"},"\uF213")):(0,n.createCommentVNode)("",!0),i.participant._isAudioMuted?((0,n.openBlock)(),(0,n.createElementBlock)("u-text",{key:1,class:"iconfont icon-ion-ios-mic-off",style:{color:"white"}},"\uF45F")):((0,n.openBlock)(),(0,n.createElementBlock)("u-text",{key:2,class:"iconfont icon-ion-ios-mic"},"\uF461")),(0,n.createElementVNode)("u-text",{class:"name"},(0,n.toDisplayString)(a.userName),1)])],6)}var dt=V(ct,[["render",ut],["styles",[lt]]]),ft="/static/image/av/av_conference_audio.png",pt="/static/image/av/av_conference_audio_mute.png",gt="/static/image/av/av_conference_handup.png",ht="/static/image/av/av_conference_handup_hover.png",At="/static/image/av/av_conference_members.png",mt="/static/image/av/av_conference_end_call.png",Ct={iconfont:{"":{fontFamily:"icomoon"}},"main-slider-container":{"":{width:"750rpx",flex:1,display:"flex",flexDirection:"column"}},"content-container":{"":{width:"750rpx",position:"relative",display:"flex",flex:1,flexDirection:"row",flexWrap:"wrap",justifyContent:"center",alignItems:"center",backgroundColor:"#2d3033",alignContent:"center"}},grid:{".main-slider-container ":{flexDirection:"row"}},focus:{".main-slider-container ":{"-ParticipantVideoItemWidth":"200px","-ParticipantVideoItemHeight":"100px",flexDirection:"column"}},"participant-audio-item":{"":{display:"flex",flexDirection:"column",width:"250rpx",height:"250rpx",justifyContent:"center",alignItems:"center"}},indicator:{".participant-audio-item ":{width:18,height:28,position:"absolute",left:50,bottom:0,color:"#FFFFFF",textAlign:"center",verticalAlign:"center",borderRadius:14,backgroundColor:"#d6d6d6",transform:"translateX(-50%) translateY(25%)"}},desc:{".participant-audio-item ":{paddingTop:8,fontSize:15},".duration-action-container ":{color:"#FFFFFF",paddingTop:0,paddingRight:5,paddingBottom:0,paddingLeft:0},"":{color:"#FFFFFF",fontSize:15,paddingTop:5,paddingRight:0,paddingBottom:5,paddingLeft:0}},"duration-action-container":{"":{width:"750rpx",height:140,backgroundColor:"#808080",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center"}},"action-container":{"":{width:"750rpx",display:"flex",flexDirection:"row",justifyContent:"center"}},action:{".action-container ":{flex:1,display:"flex",flexDirection:"column",alignItems:"center",fontSize:12,color:"#FFFFFF",paddingTop:0,paddingRight:25,paddingBottom:0,paddingLeft:25}},avatar:{"":{width:90,height:90,borderRadius:45},".highlight":{"//border":"2px solid #1FCA6A"}},"action-img":{"":{width:40,height:40}},"voip-title-container":{"":{position:"absolute",left:0,top:0,width:"750rpx",display:"flex",marginTop:"64rpx",paddingTop:0,paddingRight:"40rpx",paddingBottom:0,paddingLeft:"40rpx",flexDirection:"row",alignItems:"center",justifyContent:"space-between",height:"88rpx"}},title:{".voip-title-container ":{position:"absolute",left:0,top:0,width:"750rpx",height:"88rpx",display:"flex",justifyContent:"center",alignItems:"center"}}},vt={name:"ConferencePage",data(){return{wfc:getApp().wfc,conferenceId:null,password:"",conferenceManager:getApp().conferenceManager,session:{},conferenceInfo:null,audioOnly:!0,status:1,selfUserInfo:null,participantUserInfos:[],startTimestamp:0,currentTimestamp:0,showSlider:!1,showConversationView:!1,videoInputDeviceIndex:0,refreshUserInfoInternal:0,endReason:void 0,currentLayout:0,currentGridPageIndex:0,participantCountPerGridPage:9,speakingVideoParticipant:null,hideFocusLayoutParticipantListVideoView:!1,showConferenceSimpleInfoView:!1,showChooseLayoutView:!1,alertDialogOptions:{}}},components:{ConferenceParticipantVideoView:dt},onLoad(t){r("log","at pages/voip/conference/ConferencePage.nvue:184","voip/Conference onLoad",t),t.session?(this.session=JSON.parse(t.session),this.callState=Number(this.session.state),this.session.connectedTime&&(this.startTimestamp=this.session.connectedTime),this.conferenceInfo=this.conferenceManager.conferenceInfo):this.$nextTick(()=>{this.conferenceInfo=JSON.parse(t.conferenceInfo),r("log","at pages/voip/conference/ConferencePage.nvue:197","conferencePage parameters",this.conferenceInfo,t.muteAudio,t.muteVideo);let e=t.muteAudio,i=t.muteVideo;this.conferenceManager.setConferenceInfo(this.conferenceInfo),this.joinConference(this.conferenceInfo,e,i)})},created(){weex.requireModule("dom").addRule("fontFace",{fontFamily:"icomoon",src:"url('/static/iconfonts/icomoon/fonts/icomoon.ttf')"}),this.refreshUserInfoInternal=setInterval(()=>{},3*1e3),this.conferenceManager.setup(getApp(),this)},mounted(){this.callState===y.STATUS_CONNECTED&&(this.setupParticipants(),this.setupSessionCallback(),this.onVoipConnected())},methods:{profile2UserInfo(t){let e=this.wfc.getUserInfo(t.userId);return e._isAudience=t.audience,e._isHost=this.session.host===t.userId,e._isVideoMuted=t.videoMuted,e._isAudioMuted=t.audioMuted,e._volume=0,e._isScreenSharing=!!t.screenSharing,e},joinConference(t,e,i){let o=e&&i,s=!1,a="";r("log","at pages/voip/conference/ConferencePage.nvue:255","join conference",t.conferenceId,s,t.pin,t.owner,t.conferenceTitle,"",o,t.advance,e,i,a);let l=m.joinConference(t.conferenceId,s,t.pin,t.owner,t.conferenceTitle,"",o,t.advance,e,i,a);l?(this.session=l,this.setupSessionCallback()):(r("log","at pages/voip/conference/ConferencePage.nvue:262","joinConference failed, session is null"),uni.navigateBack({delta:1,fail:d=>{r("log","at pages/voip/conference/ConferencePage.nvue:266","nav back from ConferencePage err",d)}}))},setupSessionCallback(){let t=new R;t.didChangeState=e=>{r("log","at pages/voip/conference/ConferencePage.nvue:276","didChangeState",e),this.callState!==e&&(this.callState=e,e===y.STATUS_CONNECTED?this.onVoipConnected():e===y.STATUS_IDLE&&this.timer&&clearInterval(this.timer))},t.didCreateLocalVideoTrack=e=>{r("log","at pages/voip/conference/ConferencePage.nvue:292","didCreateLocalVideoTrack 00",e)},t.didRotateLocalVideoTrack=e=>{r("log","at pages/voip/conference/ConferencePage.nvue:298","didRotateLocalVideoTrack",e.getAudioTracks())},t.didScreenShareEnded=()=>{r("log","at pages/voip/conference/ConferencePage.nvue:302","didScreenShareEnded",this.session.videoMuted,this.session.audioMuted)},t.didCreateLocalVideoTrackError=()=>{},t.didReceiveRemoteVideoTrack=(e,i)=>{r("log","at pages/voip/conference/ConferencePage.nvue:314","didReceiveRemoteVideoTrack",e,i);for(let o=0;o<this.participantUserInfos.length;o++){let s=this.participantUserInfos[o];if(s.uid===e&&s._isScreenSharing===i){s._isVideoMuted=!1;break}}},t.didRemoveRemoteVideoTrack=e=>{r("log","at pages/voip/conference/ConferencePage.nvue:325","didRemoveRemoteVideoTrack",e)},t.didParticipantJoined=(e,i)=>{r("log","at pages/voip/conference/ConferencePage.nvue:329","didParticipantJoined",e,i);let o=m.getParticipantProfile(this.session.callId,e,i);r("log","at pages/voip/conference/ConferencePage.nvue:331","didParticipantJoined profile",o);let s=this.profile2UserInfo(o);this.participantUserInfos.push(s),r("log","at pages/voip/conference/ConferencePage.nvue:334","joined",s,o.audience,this.participantUserInfos.length)},t.didParticipantLeft=(e,i,o)=>{r("log","at pages/voip/conference/ConferencePage.nvue:338","didParticipantLeft",e,i,o,JSON.stringify(this.participantUserInfos),this.participantUserInfos.length),this.participantUserInfos=this.participantUserInfos.filter(s=>!(s.uid===e&&s._isScreenSharing===o)),r("log","at pages/voip/conference/ConferencePage.nvue:344","didParticipantLeft d",e,i,o,this.participantUserInfos.length)},t.didCallEndWithReason=e=>{r("log","at pages/voip/conference/ConferencePage.nvue:348","callEndWithReason",e),this.conferenceManager.addHistory(this.conferenceManager.conferenceInfo,new Date().getTime()-this.conferenceManager.conferenceInfo.startTime*1e3),this.endReason=e,e!==C.REASON_MediaError&&(e===C.RoomNotExist&&(r("log","at pages/voip/conference/ConferencePage.nvue:358","join conference failed",e,this.session),this.session),this.conferenceManager.destroy(),this.session=null)},t.onRequestChangeMode=e=>{},t.didChangeType=(e,i,o)=>{r("log","at pages/voip/conference/ConferencePage.nvue:388","didChangeType",e,i,o);let s=!1,a=m.getParticipantProfile(this.session.callId,e,o);this.selfUserInfo.uid===e&&(this.session.audience=i,this.session.videoMuted=a.videoMuted,this.session.audioMuted=a.audioMuted);for(let l of this.participantUserInfos)if(l.uid===e&&l._isScreenSharing===o){l._isAudience=i,l._isVideoMuted=a.videoMuted,l._isAudioMuted=a.audioMuted,this.speakingVideoParticipant&&this.speakingVideoParticipant.uid===l.uid&&(this.speakingVideoParticipant=null),s=!0;break}if(!s){let l=this.profile2UserInfo(a);this.participantUserInfos.push(l)}},t.didReportAudioVolume=(e,i)=>{if(i<100)return;r("log","at pages/voip/conference/ConferencePage.nvue:418","didReportAudioVolume",e,i);let o;if(e===this.selfUserInfo.uid?(this.selfUserInfo._volume=i,o=this.selfUserInfo):this.participantUserInfos.forEach(s=>{s.uid===e&&s._isScreenSharing===!1&&(s._volume=i,o=s)}),this.currentLayout!==0&&!o._isVideoMuted){if(this.conferenceFocusUser)return this.conferenceFocusUser;this.speakingVideoParticipant?o._volume>this.speakingVideoParticipant._volume&&(this.speakingVideoParticipant=o):this.speakingVideoParticipant=o}},t.didMuteStateChanged=e=>{r("log","at pages/voip/conference/ConferencePage.nvue:451","conference","didMuteStateChanged",e),e.forEach(i=>{let o=m.getParticipantProfile(this.session.callId,i,!1);o&&(r("log","at pages/voip/conference/ConferencePage.nvue:457","conference","didMuteStateChanged",i,o,o.videoMuted,o.audioMuted),this.participantUserInfos.forEach(s=>{s.uid===i&&!s._isScreenSharing&&(s._isVideoMuted=o.videoMuted,s._isAudioMuted=o.audioMuted,this.speakingVideoParticipant&&this.speakingVideoParticipant.uid===s.uid&&(this.speakingVideoParticipant=null)),o.userId===this.selfUserInfo.uid&&(this.session.videoMuted=o.videoMuted,this.session.audioMuted=o.audioMuted)}))})},t.didMediaLostPacket=(e,i,o)=>{r("log","at pages/voip/conference/ConferencePage.nvue:477","didMediaLostPacket",e,i,o),i>6&&r("log","at pages/voip/conference/ConferencePage.nvue:479","\u60A8\u7684\u7F51\u7EDC\u4E0D\u597D")},t.didUserMediaLostPacket=(e,i,o,s,a)=>{if(r("log","at pages/voip/conference/ConferencePage.nvue:484","didUserMediaLostPacket",e,i,o,s,a),o>10)if(s){let l=this.participantUserInfos.filter(d=>d.uid===e&&d._isScreenSharing===a);l&&l.length>0&&r("log","at pages/voip/conference/ConferencePage.nvue:491",l[0].displayName,"\u7F51\u7EDC\u4E0D\u597D")}else r("log","at pages/voip/conference/ConferencePage.nvue:494","\u60A8\u7684\u7F51\u7EDC\u4E0D\u597D")},getApp().avengineKit.sessionCallback=t},showAlertDialog(t){this.alertDialogOptions={cancelText:t.cancelText,confirmText:t.confirmText,title:t.title,content:t.content,onConfirm:()=>{t.onConfirm&&t.onConfirm()},onClose:()=>{t.onClose&&t.onClose(),this.alertDialogOptions={}}},this.$refs.alertDialog.open()},hangup(){m.leaveConference(this.session.callId,!1),this.session=null,this.conferenceManager.destroy(),uni.navigateBack({delta:1,fail:t=>{r("log","at pages/voip/conference/ConferencePage.nvue:527","nav back from ConferencePage err",t)}})},muteAudio(){let t=!!this.session.audioMuted;if(t&&this.session.audience&&!this.conferenceManager.isOwner()&&!this.conferenceManager.conferenceInfo.allowSwitchMode){this.requestUnmute(!0);return}this.enableAudio(t)},enableAudio(t){return _(this,null,function*(){m.muteAudio(this.session.callId,!t),r("log","at pages/voip/conference/ConferencePage.nvue:546","enableAudio",this.selfUserInfo._isAudioMuted,this.session.audience,t),t?this.session.audience&&m.switchAudience(this.session.callId,!1):this.session.videoMuted&&!this.session.audience&&m.switchAudience(this.session.callId,!0)})},muteVideo(){let t=!!this.session.videoMuted;if(r("log","at pages/voip/conference/ConferencePage.nvue:567","muteVideo",this.session,this.session.videoMuted,this.session.audience),t&&this.session.audience&&!this.conferenceManager.isOwner()&&!this.conferenceManager.conferenceInfo.allowSwitchMode){this.requestUnmute(!1);return}this.enableVideo(t)},enableVideo(t){return _(this,null,function*(){if(r("log","at pages/voip/conference/ConferencePage.nvue:576","enableVideo",!t),m.muteVideo(this.session.callId,!t),t){if(this.session.audience){let e=m.switchAudience(this.session.callId,!1);r("log","at pages/voip/conference/ConferencePage.nvue:582","switchAudience ",!1,e)}}else if(this.session.audioMuted&&!this.session.audience){let e=m.switchAudience(this.session.callId,!0);r("log","at pages/voip/conference/ConferencePage.nvue:591","switchAudience ",!0,e)}r("log","at pages/voip/conference/ConferencePage.nvue:598","_muteVideo",this.selfUserInfo,this.participantUserInfos)})},requestUnmute(t){this.alert({title:"\u63D0\u793A",content:"\u4E3B\u6301\u4EBA\u4E0D\u5141\u8BB8\u89E3\u9664\u9759\u97F3\uFF0C\u60A8\u53EF\u4EE5\u5411\u4E3B\u6301\u4EBA\u7533\u8BF7\u89E3\u9664\u9759\u97F3",confirmText:"\u7533\u8BF7",onClose:()=>{},onConfirm:()=>{this.conferenceManager.applyUnmute(t,!1)}})},members(){uni.navigateTo({url:"/pages/voip/conference/ConferenceManagePage",success:()=>{r("log","at pages/voip/conference/ConferencePage.nvue:623","nav to ConferenceManagePage success")},fail:t=>{r("log","at pages/voip/conference/ConferencePage.nvue:627","nav to ConferenceManagePage err",t)}})},chat(){let t=new U(S.ChatRoom,this.session.callId,0);getApp().store.setCurrentConversation(t);let e=new D;e.chatRoomId=this.session.callId,e.title=this.conferenceInfo.conferenceTitle,e._displayName=this.conferenceInfo.conferenceTitle,getApp().store.state.conversation.currentConversationInfo.conversation._target=e,uni.navigateTo({url:"/pages/conversation/ConversationPage",success:()=>{r("log","at pages/voip/conference/ConferencePage.nvue:645","nav to conversationPage success")},fail:i=>{r("log","at pages/voip/conference/ConferencePage.nvue:649","nav to conversationPage err",i)}})},toggleSliderView(){},screenShare(){return _(this,null,function*(){})},setAudioOutputDeviceId(t){let e=this.$el.getElementsByTagName("audio");for(let o of e)o.setSinkId(t);let i=this.$el.getElementsByTagName("video");for(let o of i)o.setSinkId(t)},handup(){this.conferenceManager.handUp(!this.conferenceManager.isHandUp)},userName(t){let e="";return t.groupAlias?e=t.groupAlias:t.friendAlias?e=t.friendAlias:t.displayName?e=t.displayName:e=t.name,e},setupParticipants(){this.participantUserInfos.length=0;let t=m.getMyProfile(this.session.callId),e=this.profile2UserInfo(t);r("log","at pages/voip/conference/ConferencePage.nvue:756","selfProfile",t),this.selfUserInfo=e,this.participantUserInfos.push(e);let i=m.getParticipantProfiles(this.session.callId);r("log","at pages/voip/conference/ConferencePage.nvue:761","participantProfiles",i);for(let o of i){let s=this.profile2UserInfo(o);this.participantUserInfos.push(s)}},onVoipConnected(){this.setupParticipants(),this.timer||(this.startTimestamp||(this.startTimestamp=new Date().getTime()),this.timer=setInterval(()=>{this.currentTimestamp=new Date().getTime()},1e3))},timestampFormat(t){t=~~(t/1e3);let e="",i=~~(t/3600);e=i>0?(i<10?"0":"")+i+":":"";let o=~~(t%3600/60);e+=(o<10?"0":"")+o+":";let s=~~(t%60);return e+=(s<10?"0":"")+s,e},minimize(){let t=getApp().avengineKit.checkOverlayPermission();r("log","at pages/voip/conference/ConferencePage.nvue:794","overlayPermission granted",t),t?(getApp().avengineKit.minimize(this.session.callId),uni.navigateBack({delta:1,fail:e=>{r("log","at pages/voip/conference/ConferencePage.nvue:800","nav back to err",e)}})):uni.showToast({title:"\u9700\u8981\u60AC\u6D6E\u7A97\u6743\u9650",icon:"none"})},prePage(){this.currentGridPageIndex--,this.currentGridPageIndex<0&&(this.currentGridPageIndex=Math.ceil(this.participantUserInfos.length/this.participantCountPerGridPage)-1)},nextPage(){this.participantUserInfos.length/this.participantCountPerGridPage>this.currentGridPageIndex+1?this.currentGridPageIndex++:this.currentGridPageIndex=0},updateCountPerPage(t){this.participantCountPerGridPage=t},setCurrentLayout(t){this.currentLayout!==t&&(t===1?this.participantUserInfos.forEach(e=>{e.uid!==this.selfUserInfo.uid&&!e._isAudience&&!e._isVideoMuted&&m.setParticipantVideoType(this.session.callId,e.uid,e._isScreenSharing,k.SMALL_STREAM)}):(this.currentGridPageIndex=0,this.conferenceManager.currentFocusUser=null),this.currentLayout=t,this.showChooseLayoutView=!1)},toggleParticipantListVideoView(){this.hideFocusLayoutParticipantListVideoView=!this.hideFocusLayoutParticipantListVideoView},hideConferenceSimpleInfoView(t){t.target.id!=="info-icon"&&(this.showConferenceSimpleInfoView=!1)},hideChooseLayoutView(t){t.target.id!=="grid-icon"&&(this.showChooseLayoutView=!1)},alert(t){r("log","at pages/voip/conference/ConferencePage.nvue:868","alert",t),this.showAlertDialog(t)}},computed:{duration(){if(this.currentTimestamp<=0)return"00:00";let t=this.currentTimestamp-this.startTimestamp;return this.timestampFormat(t)},speakingUserName(){if(!this.selfUserInfo)return"";let t=this.selfUserInfo._volume,e=this.selfUserInfo;return this.participantUserInfos.forEach(i=>{i._volume>t&&(e=i,t=i._volume)}),t?this.userName(e):""},currentPageParticipants(){if(this.currentLayout===1)return[];let t=this.conferenceFocusUser,e=[...this.participantUserInfos].sort((s,a)=>{if(t){if(s.uid===t.uid&&s._isScreenSharing===t._isScreenSharing)return-1;if(a.uid===t.uid&&a._isScreenSharing===t._isScreenSharing)return 1}return s._isAudience&&!a._isAudience?1:!s._isAudience&&a._isAudience?-1:s._isAudience&&a._isAudience?s.uid.localeCompare(a.uid):s._isScreenSharing&&!a._isScreenSharing?-1:!s._isScreenSharing&&a._isScreenSharing?1:!s._isVideoMuted&&a._isVideoMuted?-1:s._isVideoMuted&&!a._isVideoMuted?1:s.uid.localeCompare(a.uid)}),i=this.currentGridPageIndex*this.participantCountPerGridPage,o=i+this.participantCountPerGridPage>e.length?e.length:i+this.participantCountPerGridPage;return e.slice(i,o)},gridPageCount(){return Math.ceil(this.participantUserInfos.length/this.participantCountPerGridPage)},conferenceFocusUser(){let t=this.conferenceManager.conferenceInfo.focus;if(!t)return null;let e=this.participantUserInfos.find(i=>i.uid===t&&i._isScreenSharing===!0);return e||(e=this.participantUserInfos.find(i=>i.uid===t)),e},conferenceLocalFocusUser(){return this.conferenceManager.localFocusUser},computedCurrentLayout(){return this.currentLayout===-1&&this.conferenceFocusUser?0:this.currentLayout},computedFocusVideoParticipant(){if(this.currentLayout===0)return r("log","at pages/voip/conference/ConferencePage.nvue:993","computedSpeakingParticipant null"),null;let t;return this.conferenceFocusUser&&!this.conferenceFocusUser._isVideoMuted?t=this.conferenceFocusUser:this.conferenceLocalFocusUser&&!this.conferenceLocalFocusUser._isVideoMuted?t=this.conferenceLocalFocusUser:this.speakingVideoParticipant?t=this.speakingVideoParticipant:(t=this.participantUserInfos.find(e=>!e._isAudience&&!e._isVideoMuted&&e._isScreenSharing===!0),t||(t=this.participantUserInfos.find(e=>!e._isAudience&&!e._isVideoMuted))),r("log","at pages/voip/conference/ConferencePage.nvue:1010","computedFocusVideoParticipant",this.conferenceManager.currentFocusUser),this.conferenceManager.currentFocusUser&&m.setParticipantVideoType(this.session.callId,this.conferenceManager.currentFocusUser.uid,this.conferenceManager.currentFocusUser._isScreenSharing,k.SMALL_STREAM),t?(this.conferenceManager.currentFocusUser=t,m.setParticipantVideoType(this.session.callId,this.conferenceManager.currentFocusUser.uid,this.conferenceManager.currentFocusUser._isScreenSharing,k.BIG_STREAM)):this.session.screenSharing&&(t=this.selfUserInfo),r("log","at pages/voip/conference/ConferencePage.nvue:1022","computedSpeakingParticipant",t),t}},watch:{participantUserInfos:{deep:!0,handler(t){let e=!0;if(r("log","at pages/voip/conference/ConferencePage.nvue:1032","watchParticipantUserInfos"),this.session.screenSharing)e=!1;else for(let i=0;i<this.participantUserInfos.length;i++){let o=this.participantUserInfos[i];if(!o._isAudience&&!o._isVideoMuted){r("log","at pages/voip/conference/ConferencePage.nvue:1040","audioOnly false",o,o._isAudience,o._isVideoMuted),e=!1;break}}this.audioOnly=e,r("log","at pages/voip/conference/ConferencePage.nvue:1047","audioOly ",e),this.wfc.eventEmitter.emit("conferenceParticipantUpdated","")}}},directives:{},onBackPress(t){return r("log","at pages/voip/conference/ConferencePage.nvue:1057","conferencePage, onBackPress",t),t.from!=="navigateBack"},beforeUnmount(){r("log","at pages/voip/conference/ConferencePage.nvue:1067","conferencePage, beforeUnmount")},onShow(){r("log","at pages/voip/conference/ConferencePage.nvue:1071","conferencePage, onShow")},unmounted(){clearInterval(this.refreshUserInfoInternal)}};function wt(t,e,i,o,s,a){let l=(0,n.resolveComponent)("ConferenceParticipantVideoView"),d=(0,n.resolveComponent)("section"),f=(0,n.resolveComponent)("p"),w=O((0,n.resolveDynamicComponent)("uni-popup-dialog"),Xe),T=O((0,n.resolveDynamicComponent)("uni-popup"),rt);return(0,n.openBlock)(),(0,n.createElementBlock)("scroll-view",{scrollY:!0,showScrollbar:!0,enableBackToTop:!0,bubble:"true",style:{flexDirection:"column"}},[(0,n.createElementVNode)("div",{style:{flex:"1",display:"flex","flex-direction":"column"},ref:"rootContainer"},[s.session?((0,n.openBlock)(),(0,n.createElementBlock)("div",{key:0,class:"main-slider-container",style:{display:"flex",flex:"1"}},[s.audioOnly?((0,n.openBlock)(),(0,n.createElementBlock)("div",{key:1,style:{width:"750rpx",flex:"1"}},[(0,n.createVNode)(d,{class:"content-container audio"},{default:(0,n.withCtx)(()=>[((0,n.openBlock)(!0),(0,n.createElementBlock)(n.Fragment,null,(0,n.renderList)(s.participantUserInfos,u=>((0,n.openBlock)(),(0,n.createElementBlock)("div",{key:u.uid,style:{position:"relative"},class:"participant-audio-item"},[(0,n.createElementVNode)("div",{style:{position:"relative"}},[(0,n.createElementVNode)("u-image",{class:(0,n.normalizeClass)(["avatar",{highlight:u._volume>0}]),src:u.portrait,alt:u},null,10,["src","alt"]),u._isHost?((0,n.openBlock)(),(0,n.createElementBlock)("u-text",{key:0,class:"indicator iconfont icon-ion-person",style:{background:"#FD802E"}},"\uF213")):(0,n.createCommentVNode)("",!0),u._isAudience?((0,n.openBlock)(),(0,n.createElementBlock)("u-text",{key:1,class:"indicator iconfont icon-ion-ios-mic-off",style:{color:"red"}},"\uF45F")):(0,n.createCommentVNode)("",!0)]),(0,n.createElementVNode)("u-text",{class:"desc"},(0,n.toDisplayString)(a.userName(u)),1)]))),128))]),_:1}),(0,n.createElementVNode)("div",{style:{height:"50px",width:"750rpx",position:"absolute",left:"0",top:"70px",display:"flex","justify-content":"center","align-items":"center"}},[(0,n.createElementVNode)("div",{style:{background:"gray",width:"300px",height:"40px",padding:"0 5px","border-radius":"3px",display:"flex","flex-direction":"column","justify-content":"center"}},[(0,n.createElementVNode)("u-text",{class:"desc"},(0,n.toDisplayString)("\u6B63\u5728\u8BB2\u8BDD: "+a.speakingUserName),1)])])])):((0,n.openBlock)(),(0,n.createElementBlock)("div",{key:0,style:{width:"750rpx",flex:"1",position:"relative"}},[(0,n.createVNode)(d,{class:"content-container grid"},{default:(0,n.withCtx)(()=>[((0,n.openBlock)(!0),(0,n.createElementBlock)(n.Fragment,null,(0,n.renderList)(a.currentPageParticipants,u=>((0,n.openBlock)(),(0,n.createBlock)(l,{key:u.uid+"-"+u._isScreenSharing,participant:u,currentPageParticipants:a.currentPageParticipants,"self-user-info":s.selfUserInfo,session:s.session},null,8,["participant","currentPageParticipants","self-user-info","session"]))),128))]),_:1}),a.computedCurrentLayout===0&&s.currentGridPageIndex>0?((0,n.openBlock)(),(0,n.createElementBlock)("u-text",{key:0,style:{position:"absolute",top:"300px",left:"0",color:"#c8cacc","z-index":"1000","font-size":"40px",padding:"0 10px"},class:"iconfont icon-ion-arrow-left-c",onClick:e[0]||(e[0]=(...u)=>a.prePage&&a.prePage(...u))},"\uF108 ")):(0,n.createCommentVNode)("",!0),a.computedCurrentLayout===0&&s.currentGridPageIndex<a.gridPageCount-1?((0,n.openBlock)(),(0,n.createElementBlock)("u-text",{key:1,style:{position:"absolute",top:"300px",right:"0",color:"#c8cacc","z-index":"1000","font-size":"40px",padding:"0 10px"},class:"iconfont icon-ion-arrow-right-c",onClick:e[1]||(e[1]=(...u)=>a.nextPage&&a.nextPage(...u))},"\uF10B ")):(0,n.createCommentVNode)("",!0)])),(0,n.createElementVNode)("div",{class:"duration-action-container"},[(0,n.createCommentVNode)("",!0),(0,n.createElementVNode)("div",{class:"action-container"},[(0,n.createElementVNode)("div",{class:"action"},[!s.session.audience&&!s.session.audioMuted?((0,n.openBlock)(),(0,n.createElementBlock)("u-image",{key:0,onClick:e[2]||(e[2]=(...u)=>a.muteAudio&&a.muteAudio(...u)),class:"action-img",src:ft})):((0,n.openBlock)(),(0,n.createElementBlock)("u-image",{key:1,onClick:e[3]||(e[3]=(...u)=>a.muteAudio&&a.muteAudio(...u)),class:"action-img",src:pt})),(0,n.createVNode)(f,null,{default:(0,n.withCtx)(()=>[(0,n.createElementVNode)("u-text",null,"\u9759\u97F3")]),_:1})]),s.session.screenSharing?(0,n.createCommentVNode)("",!0):((0,n.openBlock)(),(0,n.createElementBlock)("div",{key:0,class:"action"},[!s.session.audience&&!s.session.videoMuted?((0,n.openBlock)(),(0,n.createElementBlock)("u-image",{key:0,onClick:e[4]||(e[4]=(...u)=>a.muteVideo&&a.muteVideo(...u)),class:"action-img",src:ne})):((0,n.openBlock)(),(0,n.createElementBlock)("u-image",{key:1,onClick:e[5]||(e[5]=(...u)=>a.muteVideo&&a.muteVideo(...u)),class:"action-img",src:se})),(0,n.createVNode)(f,null,{default:(0,n.withCtx)(()=>[(0,n.createElementVNode)("u-text",null,"\u89C6\u9891")]),_:1})])),(0,n.createCommentVNode)("",!0),(0,n.createElementVNode)("div",{class:"action",onClick:e[8]||(e[8]=(...u)=>a.chat&&a.chat(...u))},[(0,n.createElementVNode)("u-text",{class:"iconfont icon-ion-ios-chatboxes",style:(0,n.normalizeStyle)([{width:"40px",height:"40px","font-size":"40px",color:"black"},{color:s.showConversationView?"white":"black"}])},"\uF3FA ",4),(0,n.createVNode)(f,null,{default:(0,n.withCtx)(()=>[(0,n.createElementVNode)("u-text",null,"\u804A\u5929")]),_:1})]),s.selfUserInfo&&s.selfUserInfo.uid!==s.conferenceManager.conferenceInfo.owner?((0,n.openBlock)(),(0,n.createElementBlock)("div",{key:2,class:"action"},[s.conferenceManager.isHandUp?((0,n.openBlock)(),(0,n.createElementBlock)("u-image",{key:1,onClick:e[10]||(e[10]=(...u)=>a.handup&&a.handup(...u)),class:"action-img",src:ht})):((0,n.openBlock)(),(0,n.createElementBlock)("u-image",{key:0,onClick:e[9]||(e[9]=(...u)=>a.handup&&a.handup(...u)),class:"action-img",src:gt})),(0,n.createVNode)(f,{class:"single-line"},{default:(0,n.withCtx)(()=>[(0,n.createElementVNode)("u-text",null,"\u4E3E\u624B")]),_:1})])):(0,n.createCommentVNode)("",!0),(0,n.createElementVNode)("div",{class:"action"},[(0,n.createElementVNode)("u-image",{onClick:e[11]||(e[11]=(0,n.withModifiers)((...u)=>a.members&&a.members(...u),["stop"])),class:"action-img",src:At}),(0,n.createVNode)(f,null,{default:(0,n.withCtx)(()=>[(0,n.createElementVNode)("u-text",null,"\u7BA1\u7406")]),_:1})]),(0,n.createElementVNode)("div",{class:"action"},[(0,n.createElementVNode)("u-image",{onClick:e[12]||(e[12]=(...u)=>a.hangup&&a.hangup(...u)),class:"action-img",src:mt}),(0,n.createVNode)(f,null,{default:(0,n.withCtx)(()=>[(0,n.createElementVNode)("u-text",null,"\u7ED3\u675F")]),_:1})])])]),(0,n.createElementVNode)("div",{class:"voip-title-container"},[(0,n.createElementVNode)("div",{class:"title"},[((0,n.openBlock)(),(0,n.createElementBlock)("u-text",{key:0,class:"desc"},(0,n.toDisplayString)(a.duration),1))]),(0,n.createElementVNode)("u-image",{onClick:e[13]||(e[13]=(...u)=>a.minimize&&a.minimize(...u)),style:{width:"30px",height:"30px"},src:oe})])])):(0,n.createCommentVNode)("",!0),(0,n.createVNode)(T,{ref:"alertDialog",type:"dialog"},{default:(0,n.withCtx)(()=>[(0,n.createVNode)(w,{cancelText:s.alertDialogOptions.cancelText,confirmText:s.alertDialogOptions.confirmText,title:s.alertDialogOptions.title,onConfirm:s.alertDialogOptions.onConfirm,onClose:s.alertDialogOptions.onClose},null,8,["cancelText","confirmText","title","onConfirm","onClose"])]),_:1},512)],512)])}var F=V(vt,[["render",wt],["styles",[Ct]]]);var W=plus.webview.currentWebview();if(W){let t=parseInt(W.id),e="pages/voip/conference/ConferencePage",i={};try{i=JSON.parse(W.__query__)}catch(s){}F.mpType="page";let o=Vue.createPageApp(F,{$store:getApp({allowDefault:!0}).$store,__pageId:t,__pagePath:e,__pageQuery:i});o.provide("__globalStyles",Vue.useCssStyles([...__uniConfig.styles,...F.styles||[]])),o.mount("#root")}})();
