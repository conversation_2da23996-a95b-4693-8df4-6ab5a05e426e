"use weex:vue";

if (typeof Promise !== 'undefined' && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor
    return this.then(
      value => promise.resolve(callback()).then(() => value),
      reason => promise.resolve(callback()).then(() => {
        throw reason
      })
    )
  }
};

if (typeof uni !== 'undefined' && uni && uni.requireGlobal) {
  const global = uni.requireGlobal()
  ArrayBuffer = global.ArrayBuffer
  Int8Array = global.Int8Array
  Uint8Array = global.Uint8Array
  Uint8ClampedArray = global.Uint8ClampedArray
  Int16Array = global.Int16Array
  Uint16Array = global.Uint16Array
  Int32Array = global.Int32Array
  Uint32Array = global.Uint32Array
  Float32Array = global.Float32Array
  Float64Array = global.Float64Array
  BigInt64Array = global.BigInt64Array
  BigUint64Array = global.BigUint64Array
};


(()=>{var ii=Object.create;var ys=Object.defineProperty;var ni=Object.getOwnPropertyDescriptor;var ri=Object.getOwnPropertyNames;var oi=Object.getPrototypeOf,ai=Object.prototype.hasOwnProperty;var li=(r,e)=>()=>(e||r((e={exports:{}}).exports,e),e.exports);var ci=(r,e,t,s)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of ri(e))!ai.call(r,i)&&i!==t&&ys(r,i,{get:()=>e[i],enumerable:!(s=ni(e,i))||s.enumerable});return r};var Ss=(r,e,t)=>(t=r!=null?ii(oi(r)):{},ci(e||!r||!r.__esModule?ys(t,"default",{value:r,enumerable:!0}):t,r));var v=(r,e,t)=>new Promise((s,i)=>{var n=d=>{try{c(t.next(d))}catch(p){i(p)}},a=d=>{try{c(t.throw(d))}catch(p){i(p)}},c=d=>d.done?s(d.value):Promise.resolve(d.value).then(n,a);c((t=t.apply(r,e)).next())});var Xe=li((Wi,bs)=>{bs.exports=Vue});var Zi=Ss(Xe());function Ne(r){return weex.requireModule(r)}function f(r,e,...t){uni.__log__?uni.__log__(r,e,...t):console[r].apply(console,[...t,e])}var Es=(r,e)=>{let t=r.__vccOpts||r;for(let[s,i]of e)t[s]=i;return t};var ui=Object.defineProperty,di=(r,e,t)=>e in r?ui(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,x=(r,e,t)=>(di(r,typeof e!="symbol"?e+"":e,t),t),R=class ae{static getWFCPlatform(){if(ae.platform>0)return ae.platform;let e=uni.getSystemInfoSync();return f("log","at config.js:85","systemInfo",e),e.osName==="ios"||e.platform==="ios"?ae.platform=e.deviceType!=="phone"?8:1:e.osName==="android"||e.platform==="android"?ae.platform=e.deviceType!=="phone"?9:2:ae.platform=0,ae.platform}static config(e){Object.keys(e).forEach(t=>{ae[t]=e[t]})}static urlRedirect(e){return e}static emojiBaseUrl(){return"https://static.wildfirechat.net/twemoji/assets/"}static stickerBaseUrl(){return"https://static.wildfirechat.net/sticker/"}};x(R,"ENABLE_AUTO_LOGIN",!0);x(R,"ENABLE_MULTI_VOIP_CALL",!0);x(R,"ENABLE_SINGLE_VOIP_CALL",!0);x(R,"DEFAULT_PORTRAIT_URL","data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAFfgAABX4BPgLDIQAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAWCSURBVHic7Z3/T9R1HMefn7vPHXfenXeAw0a4UHSVmptKzK0knVqr+MEfwpyuzdqarVqzWcu5WrOfWuZixZitn8TNctRqTdaKvqDpzAoGZKQQaAQUJhwHx32/+/SDc7NT2u58fz7vJ/J+/AHv1xsevL9+Xu8XmmEYBhQ02GR3QPFflBAylBAylBAylBAylBAylBAylBAylBAylBAylBAylBAylBAydNkdyJdo3MDgSAojY2mkUlcurHVdw23FdpSV6HAVaJJ7mB8zSsjEVAYtP0TQ2hZFd38SqfSNvxzodg3LKpxYt9qNTWvc8M2ZOROBNhO+h8STBo58MYmPvgojFs+tu64CDVsf9GL7wz44Hfyjhl7IuYsJvPFBEEOXUjfVzoL5Ol57ugh33uEQ1DNzoBZy7Psp1B0JIZkS00WHruHF7QE8ev8cIe2ZAa2QD78M4+DHIeHtahrwbK0fWzZ5hbctAsrVruVMBO9/Il4GABgG0NAUwjc/Rk1p/2ahE9I/lMTbh8dh5rg1DGB/YxAXhpPmBckTOiH1R0M576TyIRo3cMBk8flAJeR0Vwxtv8Uti/fL7wkcb+eauqiENDZPWh6zqSVsecz/g0bIxeEUuvsTlsc925fAnyM3d8YRCY2QU53ypo5THTFpsbOhEfJrn/Wj4ypnJcbOhkbIwN/ypo0//uLZ/tIIGQ2lJcbOSIudDY0QK84e0xGNKyHXkZF4QMvw+OARoriCEkKGEkKGEkKGEkKGEkKGEkKGEkIGjRC7xJ44dJ58LRohHonZhR63EnIdJYV2abHnF8mLnQ2NkEVl8jIKKxbwZDPSCFl1V4G02JV3u6TFzoZGyJrlLikLu0PXcO9SeX8M2dAIKZxrwwOr3ZbH3VDlxlwvza+BRwgAPLbBC83CDY/NBtRu5MrxpRKyrMKJ9ZXWjZJH7vNgMdGCDpAJAYBd2wIo9pu/DS0r0fH8437T4+QKnRC/14Y9OwKmLvAFDg17niyEm/AdIp0QAKha7sLepwphM6F3Dl3DvmeKcM9ip/jGBUD7YAcAunoTeLVhFKGwmCwEv9eGN18oxtKFnDIA0hFylRVLnKheJW6Rr17lppYBkAuZjSghZCghZCghZCghZCghZFALSaUN9A+Je7sxeClF9+o2G9qDYc9AEm8dCqJ3QOxjmrUrXdi1LYB5AZ7PttdCJ6RvMInG5kmcaIua9kTBXaChZq0HtRu9mF/MJYZGSO9AEoeOTeJkR9SyaUW3a1hf6cbWh7w01/BShSRTBk60x/BZaxhdvXIfXq5Y4sTm9V5Ur3RJzdOSIiQcyaDp6zA+/W5K2MWhKAI+Gzav82DLJi88buv3PJYKSaUNfH48gsbmCQQnuERkU+S3Y0eNDzXVHkuTLywTcrorhoamkNTnz/lQXqrjuVo/qpZbkypkupBwJIP9jeNobeMq8pIr6yrdePmJALwmp7yaKqR/KInXD47NuFExHeWlOvbtLEZ5qXnFXE0T0tETx976MUxFudeKXAn4bHhn9zwsut2cbbIpQs5fTGDXgcuIxCiOOMIJ+Gyo2z0PC02QInxCHAul8cq7o7esDAAYn8zgpTpx3/qvRbiQ946GEJy8taapG3F5PI36o+ILdQoV0nE+jm9/mtm7qVxoORMRfsMgVIiMEn0yMQzgsOCfWZiQkdE02s9ZV8CShZ+7Y/gnKK60lDAhJzutu6VlImMApzrFlQgUJqSrh6dMntV09oibGYQJYawSbRUXhsXdRAgTMkZUJs9qRJYnFCYkEpu9QiJRcYunMCHp2etj2n+9lA/Cri337SwS1dSshibJQXEF6kS52YgSQoYSQoYSQoYSQoYSQoYSQoYSQoYSQoYSQoYSQoYSQoYSQoYSQoYSQsa/0LPTp+EdzPEAAAAASUVORK5CYII=");x(R,"DEFAULT_ORGANIZATION_PORTRAIT_URL","data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAABQCAYAAACOEfKtAAAACXBIWXMAAAsTAAALEwEAmpwYAAABlUlEQVR4nO2aIW7DQBBFc409UJRD9AoFJr1DeUADSrogoDws8EslloKCLeUEOcDWEzWtii133d2xH/h0ZD/9Pzs72lVSMBRGM1gBL/zJQAAUAK1kinCgAGg4UH4nASIsABoOVPkoEmEtBGDcN5PodFgvA+Dm+WMSfUFcDMDm/WK79jObANgC0HAgETZ6oDhEjENEnMKMMWIONAbplkHauIlwlTPuwprhMuFhd74vFHJpccuEDeus6UHHfXMHXcJVVTswARCACQcGIpwqaDP0QAHQcKAYY4wxpkLFJQ3Sp8M6+1ONp5fX3ztublUHMP64xYuqBZgyfpiXmgAUAA0Higibh35FDxQADQc6jltkjAkATDjQb9wiEQ6+AY5dGqQZ1MwKcHu8DNLj23nwz9ZeMyvA7toP0vb4/W5lDjUBeAWg4UARYRf9KtIDewB2ONBv3CIR7gHY4UC/cYtEuAdghwP/Z00UndTMskwY+worzaBmFoAoADBlNgIOFACtZGvBgQKg4UD5PeGJsABoOFDlo0iEVQbgDWanvolAkB8PAAAAAElFTkSuQmCC");x(R,"DEFAULT_DEPARTMENT_PORTRAIT_URL","data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAABQCAYAAACOEfKtAAAAAXNSR0IArs4c6QAAAIRlWElmTU0AKgAAAAgABQESAAMAAAABAAEAAAEaAAUAAAABAAAASgEbAAUAAAABAAAAUgEoAAMAAAABAAIAAIdpAAQAAAABAAAAWgAAAAAAAABIAAAAAQAAAEgAAAABAAOgAQADAAAAAQABAACgAgAEAAAAAQAAAFCgAwAEAAAAAQAAAFAAAAAAwtohTAAAAAlwSFlzAAALEwAACxMBAJqcGAAAAVlpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IlhNUCBDb3JlIDYuMC4wIj4KICAgPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4KICAgICAgPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIKICAgICAgICAgICAgeG1sbnM6dGlmZj0iaHR0cDovL25zLmFkb2JlLmNvbS90aWZmLzEuMC8iPgogICAgICAgICA8dGlmZjpPcmllbnRhdGlvbj4xPC90aWZmOk9yaWVudGF0aW9uPgogICAgICA8L3JkZjpEZXNjcmlwdGlvbj4KICAgPC9yZGY6UkRGPgo8L3g6eG1wbWV0YT4KGV7hBwAAAjlJREFUeAHt28FOwkAQgOGt8UF4Cg0nOJjo1bfgEfRkPekj8BZeOGjigRuRcNeTJ30NsW0g2UzatTNTpE3+JiS7dGba/dgAW0oIbAgggAACCCCAAAIIIIAAAgMTyI59vlcPq2lxDuVDtT3fjnNVwoGCTw9UV1N2us3CnSZhF5sbcjpP6QNgNajri7NWg/v4/A7vxaMv20lfTmSo5wGg85UDEECngDOdGQigU8CZzgwE0CngTGcGAugUcKYzAwF0CjjTmYEAOgWc6b25nPX0unYO5TjpfQBcZtvawY9+QhgV7zHL2r08mRYoLvXnl4+retp06r/u5UPEyQ0ggE4BZzozEECngDOdGQigU8CZ3smtHdbbM1LnXnyJnmZZmBRfsu9TcZZ9Xd4W0tVKxHp7RuP496+s8baPxrq7HflfAW33dwVYHe/lZrwfd9vjN8aVK5ES7xA1Gw9q2MGHiAEtTgEw1jC0ATSgxSkAxhqGNoAGtDgFwFjD0AbQgBanABhrGNoAGtDiFNXKIbHmTf4AlFp7DqVmjBa3tUu5xjVv+UoUvwBN4uJRO4/asjmUmvK8q74WsEqaz85ri8knF5uvsNi0+0vCUGrKMfIeKEWUfQCVYDIcQCmi7AOoBJPhAEoRZR9AJZgMB1CKKPsAKsFkOIBSRNkHUAkmwwGUIso+gEowGQ6gFFH2AVSCyXDT5azZ/E3WcfeHUlMOVAvY9JcEWVfTH0pNzZiIRQABBBBAAAEEEEAAAQQQ6K/ALz1vZTdxNVa5AAAAAElFTkSuQmCC");x(R,"DEFAULT_VIDEO_POSTER","data:image/png;base64,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");x(R,"APP_SERVER","https://app.wildfirechat.net");x(R,"ORGANIZATION_SERVER","https://org.wildfirechat.cn");x(R,"WORKSPACE_URL","https://open.wildfirechat.cn/work.html");x(R,"IM_SERVER_HOST","wildfirechat.net");x(R,"QR_CODE_PREFIX_PC_SESSION","wildfirechat://pcsession/");x(R,"ICE_SERVERS",[{uri:"turn:turn.wildfirechat.net:3478",userName:"wfchat",password:"wfchat123"}]);x(R,"LANGUAGE","zh_CN");x(R,"ENABLE_MULTI_CALL_AUTO_JOIN",!0);x(R,"ENABLE_PTT",!1);x(R,"ENABLE_VOIP",!0);x(R,"SDK_PLATFORM_WINDOWS",3);x(R,"SDK_PLATFORM_OSX",4);x(R,"SDK_PLATFORM_WEB",5);x(R,"SDK_PLATFORM_WX",6);x(R,"AMR_TO_MP3_SERVER_ADDRESS",R.APP_SERVER+"/amr2mp3?path=");x(R,"FILE_HELPER_ID","wfc_file_transfer");x(R,"RECALL_REEDIT_TIME_LIMIT",60);x(R,"platform",-1);var D=R;var gi=Object.defineProperty,hi=(r,e,t)=>e in r?gi(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,S=(r,e,t)=>(hi(r,typeof e!="symbol"?e+"":e,t),t),H=class{};S(H,"STATUS_IDLE",0);S(H,"STATUS_OUTGOING",1);S(H,"STATUS_INCOMING",2);S(H,"STATUS_CONNECTING",3);S(H,"STATUS_CONNECTED",4);var le=class{};S(le,"NONE",0);S(le,"BIG_STREAM",1);S(le,"SMALL_STREAM",2);var Ie=class{didCallEndWithReason(e){f("log","at wfc/av/engine/callSessionCallback.js:12","didCallEndWithReason",e)}didChangeState(e){f("log","at wfc/av/engine/callSessionCallback.js:24","didChangeState",e)}didParticipantJoined(e,t=!1){f("log","at wfc/av/engine/callSessionCallback.js:33","didParticipantJoined",e,t)}didParticipantConnected(e,t=!1){f("log","at wfc/av/engine/callSessionCallback.js:43","didParticipantConnected",e,t)}didParticipantLeft(e,t,s=!1){f("log","at wfc/av/engine/callSessionCallback.js:53","didParticipantLeft",e,t,s)}didChangeMode(e){f("log","at wfc/av/engine/callSessionCallback.js:61","didChangeMode",e)}didChangeInitiator(e){f("log","at wfc/av/engine/callSessionCallback.js:69","didChangeInitiator",e)}didCreateLocalVideoTrack(e){f("log","at wfc/av/engine/callSessionCallback.js:77","didCreateLocalVideoTrack",e)}didReceiveRemoteVideoTrack(e,t){f("log","at wfc/av/engine/callSessionCallback.js:86","didReceiveRemoteVideoTrack",e,t)}didRemoveRemoteVideoTrack(e){f("log","at wfc/av/engine/callSessionCallback.js:94","didRemoveRemoteVideoTrack",e)}didAudioDeviceChanged(e){f("log","at wfc/av/engine/callSessionCallback.js:99","didAudioDeviceChanged",e)}didError(e){f("log","at wfc/av/engine/callSessionCallback.js:103","didError",e)}didGetStats(e){}didVideoMuted(e,t){f("log","at wfc/av/engine/callSessionCallback.js:116","didVideoMuted",e,t)}didMuteStateChanged(e){f("log","at wfc/av/engine/callSessionCallback.js:125","didMuteStateChanged",e)}didMediaLostPacket(e,t,s=!1){f("log","at wfc/av/engine/callSessionCallback.js:135","didMediaLostPacket",e,t,s)}didUserMediaLostPacket(e,t,s,i,n=!1){f("log","at wfc/av/engine/callSessionCallback.js:147","didUserMediaLostPacket",e,t,s,i,n)}didChangeType(e,t,s=!1){f("log","at wfc/av/engine/callSessionCallback.js:158","didChangeType",e,t,s)}didReportAudioVolume(e,t){}onRequestChangeMode(e){f("log","at wfc/av/engine/callSessionCallback.js:176","onRequestChangeMode",e)}},k=class{};S(k,"Single",0);S(k,"Group",1);S(k,"ChatRoom",2);S(k,"Channel",3);S(k,"SecretChat",5);var T=class{constructor(){S(this,"callId"),S(this,"title"),S(this,"desc"),S(this,"initiator"),S(this,"inviter"),S(this,"state"),S(this,"startTime"),S(this,"connectedTime"),S(this,"endTime"),S(this,"conversation"),S(this,"audioOnly"),S(this,"endReason"),S(this,"conference"),S(this,"audience"),S(this,"advanced"),S(this,"multiCall"),S(this,"videoMuted"),S(this,"audioMuted")}};S(T,"kWFAVEngineStateIdle",0);S(T,"kWFAVEngineStateOutgoing",1);S(T,"kWFAVEngineStateIncomming",2);S(T,"kWFAVEngineStateConnecting",3);S(T,"kWFAVEngineStateConnected",4);S(T,"kWFAVCallEndReasonUnknown",0);S(T,"kWFAVCallEndReasonBusy",1);S(T,"kWFAVCallEndReasonSignalError",2);S(T,"kWFAVCallEndReasonHangup",3);S(T,"kWFAVCallEndReasonMediaError",4);S(T,"kWFAVCallEndReasonRemoteHangup",5);S(T,"kWFAVCallEndReasonOpenCameraFailure",6);S(T,"kWFAVCallEndReasonTimeout",7);S(T,"kWFAVCallEndReasonAcceptByOtherClient",8);S(T,"kWFAVCallEndReasonAllLeft",9);S(T,"kWFAVCallEndReasonRemoteBusy",10);S(T,"kWFAVCallEndReasonRemoteTimeout",11);S(T,"kWFAVCallEndReasonRemoteNetworkError",12);S(T,"kWFAVCallEndReasonRoomDestroyed",13);S(T,"kWFAVCallEndReasonRoomNotExist",14);S(T,"kWFAVCallEndReasonRoomParticipantsFull",15);var $e=class{constructor(){S(this,"innerAudioContext")}onReceiveCall(e){f("log","at wfc/av/engine/avengineCallback.js:8","onReceiveCall",e),typeof e=="string"&&(e=Object.assign(new T,JSON.parse(e)));let t;e.conversation.type===k.Single?t="/pages/voip/Single":e.conversation.type===k.Group&&(t="/pages/voip/Multi"),t+=`?session=${JSON.stringify(e)}`,t&&uni.navigateTo({url:t,success:s=>{f("log","at wfc/av/engine/avengineCallback.js:24",`navigate to ${t} success`),s.eventChannel.emit("options",{callSession:e})},fail:s=>{f("log","at wfc/av/engine/avengineCallback.js:30",`navigate to ${t} error`,s)}})}shouldStartRing(e){X.innerAudioContext=uni.createInnerAudioContext(),X.innerAudioContext.src=e?"/static/audios/incoming_call_ring.mp3":"/static/audios/outgoing_call_ring.mp3",X.innerAudioContext.autoplay=!0,X.innerAudioContext.loop=!0,X.innerAudioContext.play(),X.innerAudioContext.onPlay(()=>{f("log","at wfc/av/engine/avengineCallback.js:43","\u5F00\u59CB\u64AD\u653E")}),X.innerAudioContext.onError(t=>{f("error","at wfc/av/engine/avengineCallback.js:46","\u64AD\u653E\u54CD\u94C3\u5931\u8D25",t)})}shouldStopRing(){f("log","at wfc/av/engine/avengineCallback.js:51","shouldStopRing"),X.innerAudioContext.stop(),X.innerAudioContext.destroy(),X.innerAudioContext=null}didCallEnded(e,t){f("log","at wfc/av/engine/avengineCallback.js:59","didCallEnded",e,t);let s=getCurrentPages(),i="pages/voip/Single",n="pages/voip/Multi",a="pages/voip/conference/ConferencePage",c=s[s.length-1].route;(c===i||c===n||c===a)&&uni.navigateBack({delta:1,fail:d=>{f("log","at wfc/av/engine/avengineCallback.js:70","nav back to conversationView err",d)}})}},X=new $e,Se=class{constructor(){S(this,"userId"),S(this,"callExtra"),S(this,"state"),S(this,"joinTime",0),S(this,"acceptTime",0),S(this,"audioMuted",!1),S(this,"videoMuted",!1),S(this,"audience",!1),S(this,"screenSharing",!1)}},w=D.ENABLE_VOIP?Ne("wf-uni-wfc-avclient"):null,et=class{constructor(){S(this,"avengineCallback",X),S(this,"sessionCallback")}isAVEngineKitEnable(){return!!w}init(){w.initAVEngineKit(),plus.globalEvent.addEventListener("wfc-av-event",e=>{F._handleNativeAVEngineEvent(e)}),plus.globalEvent.addEventListener("wfc-av-session-event",e=>{F._handleNativeCallSessionEvent(e)})}_handleNativeAVEngineEvent(e){let t=e.args;if(this.avengineCallback){let s=this.avengineCallback[t[0]];s&&s(...t.slice(1))}else f("warn","at wfc/av/engine/avengineKit.js:47","_handleNativeAVEngineEvent avengineCallback is null",t)}_handleNativeCallSessionEvent(e){let t=e.args;if(t[0]!=="didReportAudioVolume"&&f("log","at wfc/av/engine/avengineKit.js:54","_handleNativeCallSessionEvent",t),t[0]==="resumeVoipPage"){let s=this.currentCallSession();s&&this._resumeVoipPage(s);return}if(this.sessionCallback){let s=this.sessionCallback[t[0]];s&&(t[0]==="didChangeState"||t[0]==="didCallEndWithReason"?s(Number(t[1])):s(...t.slice(1)))}else f("warn","at wfc/av/engine/avengineKit.js:76","_handleNativeCallSessionEvent sessionCallback is null",t)}_resumeVoipPage(e){f("log","at wfc/av/engine/avengineKit.js:81","_resumeVoipPage",e);let t;e.conference?t="/pages/voip/conference/ConferencePage":e.conversation.type===k.Single?t="/pages/voip/Single":e.conversation.type===k.Group&&(t="/pages/voip/Multi"),t+=`?session=${JSON.stringify(e)}`,t&&uni.navigateTo({url:t,success:s=>{f("log","at wfc/av/engine/avengineKit.js:95",`navigate to ${t} success`)},fail:s=>{f("log","at wfc/av/engine/avengineKit.js:98",`navigate to ${t} error`,s)}})}setSessionCallback(e){this.sessionCallback=e}startSingleCall(e,t){let s=w.startSingleCall(e,t);return s?Object.assign(new T,JSON.parse(s)):null}startMultiCall(e,t,s){let i=w.startMultiCall(e,t,s);return i?Object.assign(new T,JSON.parse(i)):null}startConference(e,t,s,i,n,a,c,d,p=!1,A=""){let b=w.startConference(e,t,s,i,n,a,c,d,p,A);return b?Object.assign(new T,JSON.parse(b)):null}joinConference(e,t,s,i,n,a,c,d,p,A,b=""){let N=w.joinConference(e,t,s,i,n,a,c,d,p,A,b);return N?Object.assign(new T,JSON.parse(N)):null}leaveConference(e,t=!1){w.leaveConference(e,t)}kickoffParticipant(e,t,s,i){w.kickoffParticipant(e,t,()=>{s&&s()},n=>{i&&i(n)})}setParticipantVideoType(e,t,s,i){f("log","at wfc/av/engine/avengineKit.js:207","setParticipantVideoType",t,s,i),w.setParticipantVideoType(e,t,s,i)}isSupportMultiCall(){return w.isSupportMultiCall()}isSupportConference(){return w.isSupportConference()}setVideoProfile(e,t=!1){w.setVideoProfile(e,t)}addICEServer(e,t,s){w.addICEServer(e,t,s)}currentCallSession(){let e=w.currentCallSession();return e===""?null:Object.assign(new T,JSON.parse(e))}answerCall(e,t){w.answerCall(e,t)}endCall(e){w.endCall(e)}muteVideo(e,t){w.muteVideo(e,t)}muteAudio(e,t){w.muteAudio(e,t)}switchAudience(e,t){return w.switchAudience(e,t)}downgrade2Voice(e){w.downgrade2Voice(e)}inviteNewParticipant(e,t){w.inviteNewParticipant(e,t)}setLocalVideoView(e,t){w.setLocalVideoView(e,t)}setRemoteVideoView(e,t,s,i=!1){w.setRemoteVideoView(e,t,i,s)}getParticipantProfiles(e){let t=w.getParticipantProfiles(e);if(!t)return[];let s=[];return JSON.parse(t).map(n=>{s.push(Object.assign(new Se,n))}),s}getParticipantProfile(e,t,s){let i=w.getParticipantProfile(e,t,s);return i?Object.assign(new Se,JSON.parse(i)):null}getMyProfile(e){let t=w.getMyProfile(e);return t?Object.assign(new Se,JSON.parse(t)):null}checkOverlayPermission(){return w.checkOverlayPermission()}minimize(e,t=""){w.minimize(e,t),this.sessionCallback=null}setSpeakerOn(e,t){w.setSpeakerOn(e,t)}switchCamera(e){w.switchCamera(e)}},F=new et,Ms="/static/image/av/av_conference_video.png",Ns="/static/image/av/av_conference_video_mute.png",Is="/static/image/av/av_minimize.png";var tt;function pi(){var r=0,e=plus.ios.import("PHPhotoLibrary"),t=e.authorizationStatus();return t===0?r=null:t==3?r=1:r=0,plus.ios.deleteObject(e),r}function fi(){var r=0,e=plus.ios.import("AVCaptureDevice"),t=e.authorizationStatusForMediaType("vide");return t===0?r=null:t==3?r=1:r=0,plus.ios.deleteObject(e),r}function mi(){var r=0,e=plus.ios.import("CLLocationManager"),t=e.locationServicesEnabled(),s=e.authorizationStatus();return t?s===0?r=null:s===3||s===4?r=1:r=0:r=2,plus.ios.deleteObject(e),r}function Ci(){var r=0,e=plus.ios.import("UIApplication"),t=e.sharedApplication(),s=0;if(t.currentUserNotificationSettings){var i=t.currentUserNotificationSettings();s=i.plusGetAttribute("types"),s==0?(r=0,f("log","at common/permission.js:63","\u63A8\u9001\u6743\u9650\u6CA1\u6709\u5F00\u542F")):(r=1,f("log","at common/permission.js:66","\u5DF2\u7ECF\u5F00\u542F\u63A8\u9001\u529F\u80FD!")),plus.ios.deleteObject(i)}else s=t.enabledRemoteNotificationTypes(),s==0?(r=3,f("log","at common/permission.js:73","\u63A8\u9001\u6743\u9650\u6CA1\u6709\u5F00\u542F!")):(r=4,f("log","at common/permission.js:76","\u5DF2\u7ECF\u5F00\u542F\u63A8\u9001\u529F\u80FD!"));return plus.ios.deleteObject(t),plus.ios.deleteObject(e),r}function Ai(){var r=0,e=plus.ios.import("CNContactStore"),t=e.authorizationStatusForEntityType(0);return t===0?r=null:t==3?r=1:r=0,plus.ios.deleteObject(e),r}function vi(){var r=null,e=plus.ios.import("AVAudioSession"),t=e.sharedInstance(),s=t.recordPermission();return f("log","at common/permission.js:104","permissionStatus:"+s),s===1970168948?r=null:s===1735552628?r=1:r=0,plus.ios.deleteObject(e),r}function yi(){var r=null,e=plus.ios.import("EKEventStore"),t=e.authorizationStatusForEntityType(0);return t==3?(r=1,f("log","at common/permission.js:122","\u65E5\u5386\u6743\u9650\u5DF2\u7ECF\u5F00\u542F")):f("log","at common/permission.js:124","\u65E5\u5386\u6743\u9650\u6CA1\u6709\u5F00\u542F"),plus.ios.deleteObject(e),r}function Si(){var r=null,e=plus.ios.import("EKEventStore"),t=e.authorizationStatusForEntityType(1);return t==3?(r=1,f("log","at common/permission.js:136","\u5907\u5FD8\u5F55\u6743\u9650\u5DF2\u7ECF\u5F00\u542F")):f("log","at common/permission.js:138","\u5907\u5FD8\u5F55\u6743\u9650\u6CA1\u6709\u5F00\u542F"),plus.ios.deleteObject(e),r}function bi(r){let e=[];for(let t of r){let s=new Promise((i,n)=>{switch(t){case"push":i(Ci());break;case"location":i(mi());break;case"record":i(vi());break;case"camera":i(fi());break;case"album":i(pi());break;case"contact":i(Ai());break;case"calendar":i(yi());break;case"memo":i(Si());break;default:i(0);break}});e.push(s)}return new Promise((t,s)=>{Promise.all(e).then(i=>{let n=1;for(let a of i)if(a!==1&&a!=null){n=a;break}t(n)},i=>{s(i)})})}function Ei(r){return new Promise((e,t)=>{plus.android.requestPermissions(r,function(s){for(var i=0,n=0;n<s.granted.length;n++){var a=s.granted[n];f("log","at common/permission.js:206","\u5DF2\u83B7\u53D6\u7684\u6743\u9650\uFF1A"+a),i=1}for(var n=0;n<s.deniedPresent.length;n++){var c=s.deniedPresent[n];f("log","at common/permission.js:211","\u62D2\u7EDD\u672C\u6B21\u7533\u8BF7\u7684\u6743\u9650\uFF1A"+c),i=0}for(var n=0;n<s.deniedAlways.length;n++){var d=s.deniedAlways[n];f("log","at common/permission.js:216","\u6C38\u4E45\u62D2\u7EDD\u7533\u8BF7\u7684\u6743\u9650\uFF1A"+d),i=-1}e(i)},function(s){f("log","at common/permission.js:222","result error: "+s.message),e({code:s.code,message:s.message})})})}function Mi(){if(be.isIOS){var r=plus.ios.import("UIApplication"),e=r.sharedApplication(),t=plus.ios.import("NSURL"),s=t.URLWithString("app-settings:");e.openURL(s),plus.ios.deleteObject(s),plus.ios.deleteObject(t),plus.ios.deleteObject(e)}else{var i=plus.android.importClass("android.content.Intent"),n=plus.android.importClass("android.provider.Settings"),a=plus.android.importClass("android.net.Uri"),c=plus.android.runtimeMainActivity(),d=new i;d.setAction(n.ACTION_APPLICATION_DETAILS_SETTINGS);var p=a.fromParts("package",c.getPackageName(),null);d.setData(p),c.startActivity(d)}}var be={get isIOS(){return typeof tt=="boolean"?tt:tt=uni.getSystemInfoSync().platform==="ios"},requestIOS:bi,requestAndroid:Ei,gotoAppSetting:Mi};function _s(r){return v(this,null,function*(){let e=["record"],t=["android.permission.RECORD_AUDIO"];return r||(e.push("camera"),t.push("android.permission.CAMERA")),(be.isIOS?yield be.requestIOS(e):yield be.requestAndroid(t))!==1?(uni.showModal({content:"\u9700\u8981\u76F8\u5173\u6743\u9650",confirmText:"\u8BBE\u7F6E",success:i=>{i.confirm&&be.gotoAppSetting()}}),!1):!0})}var _e="/static/image/av/av_hang_up.png",Os="/static/image/av/av_video_answer.png",ws="/static/image/av/av_mute.png",Ts="/static/image/av/av_mute_hover.png";var Us="/static/image/av/av_float_audio.png";var Ni=Object.defineProperty,Ii=(r,e,t)=>e in r?Ni(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,Y=(r,e,t)=>(Ii(r,typeof e!="symbol"?e+"":e,t),t),z=class{constructor(e,t,s=0){Y(this,"type",k.Single),Y(this,"conversationType",this.type),Y(this,"target",""),Y(this,"line",0),this.type=e,this.conversationType=e,this.target=t,this.line=s}equal(e){return e?this.type===e.type&&this.target===e.target&&this.line===e.line:!1}},Oe=class{constructor(){Y(this,"chatRoomId"),Y(this,"title"),Y(this,"desc"),Y(this,"portrait"),Y(this,"extra"),Y(this,"state"),Y(this,"memberCount"),Y(this,"createDt"),Y(this,"updateDt")}};var m=Ss(Xe());var _i=Object.defineProperty,Oi=(r,e,t)=>e in r?_i(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,o=(r,e,t)=>(Oi(r,typeof e!="symbol"?e+"":e,t),t),Te=class{constructor(){o(this,"type"),o(this,"searchableContent"),o(this,"pushContent"),o(this,"pushData"),o(this,"content"),o(this,"binaryContent"),o(this,"localContent"),o(this,"mediaType"),o(this,"remoteMediaUrl"),o(this,"localMediaPath"),o(this,"mentionedType",0),o(this,"mentionedTargets",[]),o(this,"extra")}},O=class{constructor(e,t=0,s=[]){o(this,"type"),o(this,"mentionedType",0),o(this,"mentionedTargets",[]),o(this,"extra"),o(this,"pushContent"),this.type=e,this.mentionedType=t,this.mentionedTargets=s}digest(e){return"...digest..."}encode(){let e=new Te;return e.type=this.type,e.mentionedType=this.mentionedType,e.mentionedTargets=this.mentionedTargets,e.extra=this.extra,e}decode(e){this.type=e.type,this.mentionedType=e.mentionedType,e.hasOwnProperty("mentionedTarget")?this.mentionedTargets=e.mentionedTarget:this.mentionedTargets=e.mentionedTargets,this.extra=e.extra,this.pushContent=e.pushContent}},ps={exports:{}},Ae=typeof Reflect=="object"?Reflect:null,Ps=Ae&&typeof Ae.apply=="function"?Ae.apply:function(e,t,s){return Function.prototype.apply.call(e,t,s)},we;Ae&&typeof Ae.ownKeys=="function"?we=Ae.ownKeys:Object.getOwnPropertySymbols?we=function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:we=function(e){return Object.getOwnPropertyNames(e)};function wi(r){console&&console.warn&&f("warn","at node_modules/events/events.js:46",r)}var Ls=Number.isNaN||function(e){return e!==e};function P(){P.init.call(this)}ps.exports=P;ps.exports.once=Ri;P.EventEmitter=P;P.prototype._events=void 0;P.prototype._eventsCount=0;P.prototype._maxListeners=void 0;var Rs=10;function Ke(r){if(typeof r!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof r)}Object.defineProperty(P,"defaultMaxListeners",{enumerable:!0,get:function(){return Rs},set:function(r){if(typeof r!="number"||r<0||Ls(r))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+r+".");Rs=r}});P.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0};P.prototype.setMaxListeners=function(e){if(typeof e!="number"||e<0||Ls(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this};function Fs(r){return r._maxListeners===void 0?P.defaultMaxListeners:r._maxListeners}P.prototype.getMaxListeners=function(){return Fs(this)};P.prototype.emit=function(e){for(var t=[],s=1;s<arguments.length;s++)t.push(arguments[s]);var i=e==="error",n=this._events;if(n!==void 0)i=i&&n.error===void 0;else if(!i)return!1;if(i){var a;if(t.length>0&&(a=t[0]),a instanceof Error)throw a;var c=new Error("Unhandled error."+(a?" ("+a.message+")":""));throw c.context=a,c}var d=n[e];if(d===void 0)return!1;if(typeof d=="function")Ps(d,this,t);else for(var p=d.length,A=qs(d,p),s=0;s<p;++s)Ps(A[s],this,t);return!0};function js(r,e,t,s){var i,n,a;if(Ke(t),n=r._events,n===void 0?(n=r._events=Object.create(null),r._eventsCount=0):(n.newListener!==void 0&&(r.emit("newListener",e,t.listener?t.listener:t),n=r._events),a=n[e]),a===void 0)a=n[e]=t,++r._eventsCount;else if(typeof a=="function"?a=n[e]=s?[t,a]:[a,t]:s?a.unshift(t):a.push(t),i=Fs(r),i>0&&a.length>i&&!a.warned){a.warned=!0;var c=new Error("Possible EventEmitter memory leak detected. "+a.length+" "+String(e)+" listeners added. Use emitter.setMaxListeners() to increase limit");c.name="MaxListenersExceededWarning",c.emitter=r,c.type=e,c.count=a.length,wi(c)}return r}P.prototype.addListener=function(e,t){return js(this,e,t,!1)};P.prototype.on=P.prototype.addListener;P.prototype.prependListener=function(e,t){return js(this,e,t,!0)};function Ti(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function zs(r,e,t){var s={fired:!1,wrapFn:void 0,target:r,type:e,listener:t},i=Ti.bind(s);return i.listener=t,s.wrapFn=i,i}P.prototype.once=function(e,t){return Ke(t),this.on(e,zs(this,e,t)),this};P.prototype.prependOnceListener=function(e,t){return Ke(t),this.prependListener(e,zs(this,e,t)),this};P.prototype.removeListener=function(e,t){var s,i,n,a,c;if(Ke(t),i=this._events,i===void 0)return this;if(s=i[e],s===void 0)return this;if(s===t||s.listener===t)--this._eventsCount===0?this._events=Object.create(null):(delete i[e],i.removeListener&&this.emit("removeListener",e,s.listener||t));else if(typeof s!="function"){for(n=-1,a=s.length-1;a>=0;a--)if(s[a]===t||s[a].listener===t){c=s[a].listener,n=a;break}if(n<0)return this;n===0?s.shift():Ui(s,n),s.length===1&&(i[e]=s[0]),i.removeListener!==void 0&&this.emit("removeListener",e,c||t)}return this};P.prototype.off=P.prototype.removeListener;P.prototype.removeAllListeners=function(e){var t,s,i;if(s=this._events,s===void 0)return this;if(s.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):s[e]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete s[e]),this;if(arguments.length===0){var n=Object.keys(s),a;for(i=0;i<n.length;++i)a=n[i],a!=="removeListener"&&this.removeAllListeners(a);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(t=s[e],typeof t=="function")this.removeListener(e,t);else if(t!==void 0)for(i=t.length-1;i>=0;i--)this.removeListener(e,t[i]);return this};function Bs(r,e,t){var s=r._events;if(s===void 0)return[];var i=s[e];return i===void 0?[]:typeof i=="function"?t?[i.listener||i]:[i]:t?Pi(i):qs(i,i.length)}P.prototype.listeners=function(e){return Bs(this,e,!0)};P.prototype.rawListeners=function(e){return Bs(this,e,!1)};P.listenerCount=function(r,e){return typeof r.listenerCount=="function"?r.listenerCount(e):Hs.call(r,e)};P.prototype.listenerCount=Hs;function Hs(r){var e=this._events;if(e!==void 0){var t=e[r];if(typeof t=="function")return 1;if(t!==void 0)return t.length}return 0}P.prototype.eventNames=function(){return this._eventsCount>0?we(this._events):[]};function qs(r,e){for(var t=new Array(e),s=0;s<e;++s)t[s]=r[s];return t}function Ui(r,e){for(;e+1<r.length;e++)r[e]=r[e+1];r.pop()}function Pi(r){for(var e=new Array(r.length),t=0;t<e.length;++t)e[t]=r[t].listener||r[t];return e}function Ri(r,e){return new Promise(function(t,s){function i(a){r.removeListener(e,n),s(a)}function n(){typeof r.removeListener=="function"&&r.removeListener("error",i),t([].slice.call(arguments))}Qs(r,e,n,{once:!0}),e!=="error"&&xi(r,i,{once:!0})})}function xi(r,e,t){typeof r.on=="function"&&Qs(r,"error",e,t)}function Qs(r,e,t,s){if(typeof r.on=="function")s.once?r.once(e,t):r.on(e,t);else if(typeof r.addEventListener=="function")r.addEventListener(e,function i(n){s.once&&r.removeEventListener(e,i),t(n)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof r)}var Di=ps.exports,Ys="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function ki(r){for(var e,t,s=String(r),i=0,n=Ys,a="";s.charAt(0|i)||(n="=",i%1);a+=n.charAt(63&e>>8-i%1*8)){if(255<(t=s.charCodeAt(i+=.75)))throw new Error("'btoa' failed: The string to be encoded contains characters outside of the Latin1 range.");e=e<<8|t}return a}function Gi(r){var e=String(r).replace(/[=]+$/,"");if(e.length%4==1)throw new Error("'atob' failed: The string to be decoded is not correctly encoded.");for(var t,s,i=0,n=0,a="";s=e.charAt(n++);~s&&(t=i%4?64*t+s:s,i++%4)?a+=String.fromCharCode(255&t>>(-2*i&6)):0)s=Ys.indexOf(s);return a}var W=null;try{W=new WebAssembly.Instance(new WebAssembly.Module(new Uint8Array([0,97,115,109,1,0,0,0,1,13,2,96,0,1,127,96,4,127,127,127,127,1,127,3,7,6,0,1,1,1,1,1,6,6,1,127,1,65,0,11,7,50,6,3,109,117,108,0,1,5,100,105,118,95,115,0,2,5,100,105,118,95,117,0,3,5,114,101,109,95,115,0,4,5,114,101,109,95,117,0,5,8,103,101,116,95,104,105,103,104,0,0,10,191,1,6,4,0,35,0,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,126,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,127,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,128,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,129,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,130,34,4,66,32,135,167,36,0,32,4,167,11])),{}).exports}catch(r){}function E(r,e,t){this.low=r|0,this.high=e|0,this.unsigned=!!t}E.prototype.__isLong__;Object.defineProperty(E.prototype,"__isLong__",{value:!0});function B(r){return(r&&r.__isLong__)===!0}function xs(r){var e=Math.clz32(r&-r);return r?31-e:e}E.isLong=B;var Ds={},ks={};function pe(r,e){var t,s,i;return e?(r>>>=0,(i=0<=r&&r<256)&&(s=ks[r],s)?s:(t=_(r,0,!0),i&&(ks[r]=t),t)):(r|=0,(i=-128<=r&&r<128)&&(s=Ds[r],s)?s:(t=_(r,r<0?-1:0,!1),i&&(Ds[r]=t),t))}E.fromInt=pe;function Z(r,e){if(isNaN(r))return e?re:$;if(e){if(r<0)return re;if(r>=Ks)return Xs}else{if(r<=-Vs)return q;if(r+1>=Vs)return Zs}return r<0?Z(-r,e).neg():_(r%ve|0,r/ve|0,e)}E.fromNumber=Z;function _(r,e,t){return new E(r,e,t)}E.fromBits=_;var Ue=Math.pow;function fs(r,e,t){if(r.length===0)throw Error("empty string");if(typeof e=="number"?(t=e,e=!1):e=!!e,r==="NaN"||r==="Infinity"||r==="+Infinity"||r==="-Infinity")return e?re:$;if(t=t||10,t<2||36<t)throw RangeError("radix");var s;if((s=r.indexOf("-"))>0)throw Error("interior hyphen");if(s===0)return fs(r.substring(1),e,t).neg();for(var i=Z(Ue(t,8)),n=$,a=0;a<r.length;a+=8){var c=Math.min(8,r.length-a),d=parseInt(r.substring(a,a+c),t);if(c<8){var p=Z(Ue(t,c));n=n.mul(p).add(Z(d))}else n=n.mul(i),n=n.add(Z(d))}return n.unsigned=e,n}E.fromString=fs;function ee(r,e){return typeof r=="number"?Z(r,e):typeof r=="string"?fs(r,e):_(r.low,r.high,typeof e=="boolean"?e:r.unsigned)}E.fromValue=ee;var Gs=65536,Vi=1<<24,ve=Gs*Gs,Ks=ve*ve,Vs=Ks/2,Js=pe(Vi),$=pe(0);E.ZERO=$;var re=pe(0,!0);E.UZERO=re;var me=pe(1);E.ONE=me;var Ws=pe(1,!0);E.UONE=Ws;var st=pe(-1);E.NEG_ONE=st;var Zs=_(-1,2147483647,!1);E.MAX_VALUE=Zs;var Xs=_(-1,-1,!0);E.MAX_UNSIGNED_VALUE=Xs;var q=_(0,-2147483648,!1);E.MIN_VALUE=q;var C=E.prototype;C.toInt=function(){return this.unsigned?this.low>>>0:this.low};C.toNumber=function(){return this.unsigned?(this.high>>>0)*ve+(this.low>>>0):this.high*ve+(this.low>>>0)};C.toString=function(e){if(e=e||10,e<2||36<e)throw RangeError("radix");if(this.isZero())return"0";if(this.isNegative())if(this.eq(q)){var t=Z(e),s=this.div(t),i=s.mul(t).sub(this);return s.toString(e)+i.toInt().toString(e)}else return"-"+this.neg().toString(e);for(var n=Z(Ue(e,6),this.unsigned),a=this,c="";;){var d=a.div(n),p=a.sub(d.mul(n)).toInt()>>>0,A=p.toString(e);if(a=d,a.isZero())return A+c;for(;A.length<6;)A="0"+A;c=""+A+c}};C.getHighBits=function(){return this.high};C.getHighBitsUnsigned=function(){return this.high>>>0};C.getLowBits=function(){return this.low};C.getLowBitsUnsigned=function(){return this.low>>>0};C.getNumBitsAbs=function(){if(this.isNegative())return this.eq(q)?64:this.neg().getNumBitsAbs();for(var e=this.high!=0?this.high:this.low,t=31;t>0&&!(e&1<<t);t--);return this.high!=0?t+33:t+1};C.isZero=function(){return this.high===0&&this.low===0};C.eqz=C.isZero;C.isNegative=function(){return!this.unsigned&&this.high<0};C.isPositive=function(){return this.unsigned||this.high>=0};C.isOdd=function(){return(this.low&1)===1};C.isEven=function(){return(this.low&1)===0};C.equals=function(e){return B(e)||(e=ee(e)),this.unsigned!==e.unsigned&&this.high>>>31===1&&e.high>>>31===1?!1:this.high===e.high&&this.low===e.low};C.eq=C.equals;C.notEquals=function(e){return!this.eq(e)};C.neq=C.notEquals;C.ne=C.notEquals;C.lessThan=function(e){return this.comp(e)<0};C.lt=C.lessThan;C.lessThanOrEqual=function(e){return this.comp(e)<=0};C.lte=C.lessThanOrEqual;C.le=C.lessThanOrEqual;C.greaterThan=function(e){return this.comp(e)>0};C.gt=C.greaterThan;C.greaterThanOrEqual=function(e){return this.comp(e)>=0};C.gte=C.greaterThanOrEqual;C.ge=C.greaterThanOrEqual;C.compare=function(e){if(B(e)||(e=ee(e)),this.eq(e))return 0;var t=this.isNegative(),s=e.isNegative();return t&&!s?-1:!t&&s?1:this.unsigned?e.high>>>0>this.high>>>0||e.high===this.high&&e.low>>>0>this.low>>>0?-1:1:this.sub(e).isNegative()?-1:1};C.comp=C.compare;C.negate=function(){return!this.unsigned&&this.eq(q)?q:this.not().add(me)};C.neg=C.negate;C.add=function(e){B(e)||(e=ee(e));var t=this.high>>>16,s=this.high&65535,i=this.low>>>16,n=this.low&65535,a=e.high>>>16,c=e.high&65535,d=e.low>>>16,p=e.low&65535,A=0,b=0,N=0,V=0;return V+=n+p,N+=V>>>16,V&=65535,N+=i+d,b+=N>>>16,N&=65535,b+=s+c,A+=b>>>16,b&=65535,A+=t+a,A&=65535,_(N<<16|V,A<<16|b,this.unsigned)};C.subtract=function(e){return B(e)||(e=ee(e)),this.add(e.neg())};C.sub=C.subtract;C.multiply=function(e){if(this.isZero())return this;if(B(e)||(e=ee(e)),W){var t=W.mul(this.low,this.high,e.low,e.high);return _(t,W.get_high(),this.unsigned)}if(e.isZero())return this.unsigned?re:$;if(this.eq(q))return e.isOdd()?q:$;if(e.eq(q))return this.isOdd()?q:$;if(this.isNegative())return e.isNegative()?this.neg().mul(e.neg()):this.neg().mul(e).neg();if(e.isNegative())return this.mul(e.neg()).neg();if(this.lt(Js)&&e.lt(Js))return Z(this.toNumber()*e.toNumber(),this.unsigned);var s=this.high>>>16,i=this.high&65535,n=this.low>>>16,a=this.low&65535,c=e.high>>>16,d=e.high&65535,p=e.low>>>16,A=e.low&65535,b=0,N=0,V=0,oe=0;return oe+=a*A,V+=oe>>>16,oe&=65535,V+=n*A,N+=V>>>16,V&=65535,V+=a*p,N+=V>>>16,V&=65535,N+=i*A,b+=N>>>16,N&=65535,N+=n*p,b+=N>>>16,N&=65535,N+=a*d,b+=N>>>16,N&=65535,b+=s*A+i*p+n*d+a*c,b&=65535,_(V<<16|oe,b<<16|N,this.unsigned)};C.mul=C.multiply;C.divide=function(e){if(B(e)||(e=ee(e)),e.isZero())throw Error("division by zero");if(W){if(!this.unsigned&&this.high===-2147483648&&e.low===-1&&e.high===-1)return this;var t=(this.unsigned?W.div_u:W.div_s)(this.low,this.high,e.low,e.high);return _(t,W.get_high(),this.unsigned)}if(this.isZero())return this.unsigned?re:$;var s,i,n;if(this.unsigned){if(e.unsigned||(e=e.toUnsigned()),e.gt(this))return re;if(e.gt(this.shru(1)))return Ws;n=re}else{if(this.eq(q)){if(e.eq(me)||e.eq(st))return q;if(e.eq(q))return me;var a=this.shr(1);return s=a.div(e).shl(1),s.eq($)?e.isNegative()?me:st:(i=this.sub(e.mul(s)),n=s.add(i.div(e)),n)}else if(e.eq(q))return this.unsigned?re:$;if(this.isNegative())return e.isNegative()?this.neg().div(e.neg()):this.neg().div(e).neg();if(e.isNegative())return this.div(e.neg()).neg();n=$}for(i=this;i.gte(e);){s=Math.max(1,Math.floor(i.toNumber()/e.toNumber()));for(var c=Math.ceil(Math.log(s)/Math.LN2),d=c<=48?1:Ue(2,c-48),p=Z(s),A=p.mul(e);A.isNegative()||A.gt(i);)s-=d,p=Z(s,this.unsigned),A=p.mul(e);p.isZero()&&(p=me),n=n.add(p),i=i.sub(A)}return n};C.div=C.divide;C.modulo=function(e){if(B(e)||(e=ee(e)),W){var t=(this.unsigned?W.rem_u:W.rem_s)(this.low,this.high,e.low,e.high);return _(t,W.get_high(),this.unsigned)}return this.sub(this.div(e).mul(e))};C.mod=C.modulo;C.rem=C.modulo;C.not=function(){return _(~this.low,~this.high,this.unsigned)};C.countLeadingZeros=function(){return this.high?Math.clz32(this.high):Math.clz32(this.low)+32};C.clz=C.countLeadingZeros;C.countTrailingZeros=function(){return this.low?xs(this.low):xs(this.high)+32};C.ctz=C.countTrailingZeros;C.and=function(e){return B(e)||(e=ee(e)),_(this.low&e.low,this.high&e.high,this.unsigned)};C.or=function(e){return B(e)||(e=ee(e)),_(this.low|e.low,this.high|e.high,this.unsigned)};C.xor=function(e){return B(e)||(e=ee(e)),_(this.low^e.low,this.high^e.high,this.unsigned)};C.shiftLeft=function(e){return B(e)&&(e=e.toInt()),(e&=63)===0?this:e<32?_(this.low<<e,this.high<<e|this.low>>>32-e,this.unsigned):_(0,this.low<<e-32,this.unsigned)};C.shl=C.shiftLeft;C.shiftRight=function(e){return B(e)&&(e=e.toInt()),(e&=63)===0?this:e<32?_(this.low>>>e|this.high<<32-e,this.high>>e,this.unsigned):_(this.high>>e-32,this.high>=0?0:-1,this.unsigned)};C.shr=C.shiftRight;C.shiftRightUnsigned=function(e){return B(e)&&(e=e.toInt()),(e&=63)===0?this:e<32?_(this.low>>>e|this.high<<32-e,this.high>>>e,this.unsigned):e===32?_(this.high,0,this.unsigned):_(this.high>>>e-32,0,this.unsigned)};C.shru=C.shiftRightUnsigned;C.shr_u=C.shiftRightUnsigned;C.rotateLeft=function(e){var t;return B(e)&&(e=e.toInt()),(e&=63)===0?this:e===32?_(this.high,this.low,this.unsigned):e<32?(t=32-e,_(this.low<<e|this.high>>>t,this.high<<e|this.low>>>t,this.unsigned)):(e-=32,t=32-e,_(this.high<<e|this.low>>>t,this.low<<e|this.high>>>t,this.unsigned))};C.rotl=C.rotateLeft;C.rotateRight=function(e){var t;return B(e)&&(e=e.toInt()),(e&=63)===0?this:e===32?_(this.high,this.low,this.unsigned):e<32?(t=32-e,_(this.high<<t|this.low>>>e,this.low<<t|this.high>>>e,this.unsigned)):(e-=32,t=32-e,_(this.low<<t|this.high>>>e,this.high<<t|this.low>>>e,this.unsigned))};C.rotr=C.rotateRight;C.toSigned=function(){return this.unsigned?_(this.low,this.high,!1):this};C.toUnsigned=function(){return this.unsigned?this:_(this.low,this.high,!0)};C.toBytes=function(e){return e?this.toBytesLE():this.toBytesBE()};C.toBytesLE=function(){var e=this.high,t=this.low;return[t&255,t>>>8&255,t>>>16&255,t>>>24,e&255,e>>>8&255,e>>>16&255,e>>>24]};C.toBytesBE=function(){var e=this.high,t=this.low;return[e>>>24,e>>>16&255,e>>>8&255,e&255,t>>>24,t>>>16&255,t>>>8&255,t&255]};E.fromBytes=function(e,t,s){return s?E.fromBytesLE(e,t):E.fromBytesBE(e,t)};E.fromBytesLE=function(e,t){return new E(e[0]|e[1]<<8|e[2]<<16|e[3]<<24,e[4]|e[5]<<8|e[6]<<16|e[7]<<24,t)};E.fromBytesBE=function(e,t){return new E(e[4]<<24|e[5]<<16|e[6]<<8|e[7],e[0]<<24|e[1]<<16|e[2]<<8|e[3],t)};var Q=class extends O{constructor(e){super(e),o(this,"fromSelf",!1)}digest(e){var t="";try{t=this.formatNotification(e)}catch(s){f("log","at wfc/messages/notification/notificationMessageContent.js:20","disgest",s)}return t}formatNotification(e){return"..nofication.."}},u=class{};o(u,"Unknown",0);o(u,"Text",1);o(u,"Voice",2);o(u,"Image",3);o(u,"Location",4);o(u,"File",5);o(u,"Video",6);o(u,"Sticker",7);o(u,"Link",8);o(u,"P_Text",9);o(u,"UserCard",10);o(u,"Composite_Message",11);o(u,"Rich_Notification",12);o(u,"Articles",13);o(u,"Streaming_Text_Generating",14);o(u,"Streaming_Text_Generated",15);o(u,"Mark_Unread_Sync",31);o(u,"StartSecretChat_Notification",40);o(u,"Enter_Channel_Chat",71);o(u,"Leave_Channel_Chat",72);o(u,"Channel_Menu_Event",73);o(u,"RecallMessage_Notification",80);o(u,"DeleteMessage_Notification",81);o(u,"Tip_Notification",90);o(u,"Typing",91);o(u,"Friend_Greeting",92);o(u,"Friend_Added",93);o(u,"PC_Login_Request",94);o(u,"CreateGroup_Notification",104);o(u,"AddGroupMember_Notification",105);o(u,"KickOffGroupMember_Notification",106);o(u,"QuitGroup_Notification",107);o(u,"DismissGroup_Notification",108);o(u,"TransferGroupOwner_Notification",109);o(u,"ChangeGroupName_Notification",110);o(u,"ModifyGroupAlias_Notification",111);o(u,"ChangeGroupPortrait_Notification",112);o(u,"MuteGroup_Notification",113);o(u,"ChangeJoinType_Notification",114);o(u,"ChangePrivateChat_Notification",115);o(u,"ChangeSearchable_Notification",116);o(u,"SetGroupManager_Notification",117);o(u,"MuteGroupMember_Notification",118);o(u,"AllowGroupMember_Notification",119);o(u,"KickOffGroupMember_Visible_Notification",120);o(u,"QuitGroup_Visible_Notification",121);o(u,"ModifyGroupExtra_Notification",122);o(u,"ModifyGroupMemberExtra_Notification",123);o(u,"VOIP_CONTENT_TYPE_START",400);o(u,"VOIP_CONTENT_TYPE_END",402);o(u,"VOIP_CONTENT_TYPE_ACCEPT",401);o(u,"VOIP_CONTENT_TYPE_SIGNAL",403);o(u,"VOIP_CONTENT_TYPE_MODIFY",404);o(u,"VOIP_CONTENT_TYPE_ACCEPT_T",405);o(u,"VOIP_CONTENT_TYPE_ADD_PARTICIPANT",406);o(u,"VOIP_CONTENT_TYPE_MUTE_VIDEO",407);o(u,"CONFERENCE_CONTENT_TYPE_INVITE",408);o(u,"CONFERENCE_CONTENT_TYPE_CHANGE_MODE",410);o(u,"CONFERENCE_CONTENT_TYPE_KICKOFF_MEMBER",411);o(u,"CONFERENCE_CONTENT_TYPE_COMMAND",412);o(u,"VOIP_Multi_Call_Ongoing",416);o(u,"VOIP_Join_Call_Request",417);o(u,"MESSAGE_CONTENT_TYPE_FEED",501);o(u,"MESSAGE_CONTENT_TYPE_COMMENT",502);function ms(r,e){let t=E.fromValue(r),s=E.fromValue(e);return t.compare(s)}function Ji(r,e){return!De(r)||!De(e)?!1:ms(r,e)===0}function Li(r,e){return!De(r)||!De(e)?!1:ms(r,e)<=0}function se(r){return E.fromValue(r).toString()}function Pe(r){return E.fromValue(r)}function Fi(r){return Li(r,Number.MAX_SAFE_INTEGER)?E.fromValue(r).toNumber():(f("log","at wfc/util/longUtil.js:118",r,"is large than Number.MAX_SAFE_INTEGER, do nothing"),r)}function Re(r,e){if(!r)return r;let t=new RegExp(`"${e}":"([0-9]+)"`,"g");return r.replace(t,`"${e}":$1`)}function xe(r,e){if(!r)return r;let t=new RegExp(`"${e}":([0-9]+)`,"g");return r.replace(t,`"${e}":"$1"`)}function De(r){try{return E.fromValue(r),!0}catch(e){return!1}}var it=class r{constructor(){o(this,"messageUid"),o(this,"userId"),o(this,"userDisplayName"),o(this,"messageDigest")}static initWithMessage(e){let t=new r;if(e){t.messageUid=e.messageUid,t.userId=e.from;let s=h.getUserInfo(e.from,!1);t.userDisplayName=s.displayName,t.messageDigest=e.messageContent.digest(),t.messageDigest.length>48&&(t.messageDigest=t.messageDigest.substr(0,48))}return t}encode(){return{u:se(this.messageUid),i:this.userId,n:this.userDisplayName,d:this.messageDigest}}decode(e){e.messageUid?(this.messageUid=E.fromValue(e.messageUid),this.userId=e.userId,this.userDisplayName=e.userDisplayName,this.messageDigest=e.messageDigest):(this.messageUid=E.fromValue(e.u||e.messageUid),this.userId=e.i,this.userDisplayName=e.n,this.messageDigest=e.d)}},ke=class extends O{constructor(e,t=0,s=[]){super(u.Text,t,s),o(this,"content"),o(this,"quoteInfo"),this.content=e}digest(){return this.content}encode(){let e=super.encode();if(e.searchableContent=this.content,this.quoteInfo){let t={quote:this.quoteInfo.encode()},i=JSON.stringify(t).replace(/"u":"([0-9]+)"/,'"u":$1');e.binaryContent=h.utf8_to_b64(i)}return e}decode(e){if(super.decode(e),this.content=e.searchableContent,e.binaryContent&&e.binaryContent.length>0){let t=h.b64_to_utf8(e.binaryContent);t=t.substring(0,t.lastIndexOf("}")+1),t=t.replace(/"u":([0-9]+),/,'"u":"$1",');let s=JSON.parse(t).quote;this.quoteInfo=new it,this.quoteInfo.decode(s)}}setQuoteInfo(e){this.quoteInfo=e}},nt=class extends ke{constructor(e,t=0,s=[]){super(e,t,s),this.type=u.P_Text}},ie=class extends O{constructor(e,t=0,s,i){super(e),o(this,"file"),o(this,"remotePath",""),o(this,"localPath",""),o(this,"mediaType",0),this.mediaType=t,s?typeof s=="string"&&!s.startsWith("http")?(this.localPath=s,this.remotePath=i):(this.file=s,s&&s.path&&(this.localPath=s.path,this.localPath.indexOf(s.name)<0&&(this.localPath+=s.name))):(this.localPath="",this.remotePath=i)}encode(){let e=super.encode();return e.localMediaPath=this.localPath,e.remoteMediaUrl=this.remotePath?this.remotePath:"",e.mediaType=this.mediaType,e}decode(e){super.decode(e),this.localPath=e.localMediaPath,this.remotePath=e.remoteMediaUrl,D.urlRedirect&&(this.remotePath=D.urlRedirect(e.remoteMediaUrl)),this.mediaType=e.mediaType}},G=class{};o(G,"General",0);o(G,"Image",1);o(G,"Voice",2);o(G,"Video",3);o(G,"File",4);o(G,"Portrait",5);o(G,"Favorite",6);o(G,"Sticker",7);o(G,"Moments",8);var rt=class extends ie{constructor(e,t,s){super(u.Image,G.Image,e,t),o(this,"thumbnail"),o(this,"imageWidth"),o(this,"imageHeight"),this.thumbnail=s}digest(){return"[\u56FE\u7247]"}encode(){let e=super.encode();if(e.mediaType=G.Image,e.binaryContent=this.thumbnail,this.imageWidth){let t={w:this.imageWidth,h:this.imageHeight};e.content=JSON.stringify(t)}return e}decode(e){if(super.decode(e),this.thumbnail=e.binaryContent,e.content){let t=JSON.parse(e.content);this.imageWidth=t.w,this.imageHeight=t.h}}},y=class{};o(y,"No_Persist",0);o(y,"Persist",1);o(y,"Persist_And_Count",3);o(y,"Transparent",4);var ot=class extends Q{constructor(e){super(u.Tip_Notification),o(this,"tip",""),this.tip=e}formatNotification(){return this.tip}digest(){return this.tip}encode(){let e=super.encode();return e.content=this.tip,e}decode(e){super.decode(e),this.tip=e.content}},ce=class extends O{constructor(e){super(u.Unknown),o(this,"originalPayload"),this.originalPayload=e}encode(){return this.originalPayload}decode(e){this.originalPayload=e}digest(){return"\u672A\u77E5\u7C7B\u578B\u6D88\u606F"}},Ge=class extends O{digest(){return"\u5C1A\u4E0D\u652F\u6301\u8BE5\u7C7B\u578B\u6D88\u606F, \u8BF7\u624B\u673A\u67E5\u770B : "+this.type}},J=class extends Q{constructor(e){super(e),o(this,"groupId","")}},at=class extends J{constructor(e,t){super(u.ChangeGroupName_Notification),o(this,"operator",""),o(this,"name",""),this.operator=e,this.name=t}formatNotification(){return this.fromSelf?"\u60A8\u4FEE\u6539\u7FA4\u540D\u79F0\u4E3A\uFF1A"+this.name:h.getGroupMemberDisplayName(this.groupId,this.operator)+"\u4FEE\u6539\u7FA4\u540D\u79F0\u4E3A\uFF1A"+this.name}encode(){let e=super.encode(),t={g:this.groupId,n:this.name,o:this.operator};return e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){super.decode(e);let t=h.b64_to_utf8(e.binaryContent),s=JSON.parse(t);this.groupId=s.g,this.operator=s.o,this.name=s.n}},lt=class extends J{constructor(e,t){super(u.KickOffGroupMember_Notification),o(this,"operator",""),o(this,"kickedMembers",[]),this.operator=e,this.kickedMembers=t}formatNotification(){let e;this.fromSelf?e="\u60A8\u628A ":e=h.getGroupMemberDisplayName(this.groupId,this.operator)+"\u628A ";let t="";return h.getUserInfos(this.kickedMembers,this.groupId).forEach(i=>{i.uid===h.getUserId()?t+=" \u60A8":t+=" "+i.displayName}),e+t+" \u79FB\u9664\u4E86\u7FA4\u7EC4"}encode(){let e=super.encode(),t={g:this.groupId,ms:this.kickedMembers,o:this.operateUser};return e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){super.decode(e);let t=h.b64_to_utf8(e.binaryContent),s=JSON.parse(t);this.groupId=s.g,this.operator=s.o,this.kickedMembers=s.ms}},ct=class extends J{constructor(e,t){super(u.KickOffGroupMember_Visible_Notification),o(this,"operator",""),o(this,"kickedMembers",[]),this.operator=e,this.kickedMembers=t}formatNotification(){let e;this.fromSelf?e="\u60A8\u628A ":e=h.getGroupMemberDisplayName(this.groupId,this.operator)+"\u628A ";let t="";return h.getUserInfos(this.kickedMembers,this.groupId).forEach(i=>{i.uid===h.getUserId()?t+=" \u60A8":t+=" "+i.displayName}),e+t+" \u79FB\u9664\u4E86\u7FA4\u7EC4"}encode(){let e=super.encode(),t={g:this.groupId,ms:this.kickedMembers,o:this.operateUser};return e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){super.decode(e);let t=h.b64_to_utf8(e.binaryContent),s=JSON.parse(t);this.groupId=s.g,this.operator=s.o,this.kickedMembers=s.ms}},Ve=class extends J{constructor(e,t){super(u.AddGroupMember_Notification),o(this,"invitor",""),o(this,"invitees",[]),this.invitor=e,this.invitees=t}formatNotification(){let e;if(this.invitees.length===1&&this.invitees[0]===this.invitor)return this.fromSelf?"\u60A8\u52A0\u5165\u4E86\u7FA4\u7EC4":h.getGroupMemberDisplayName(this.groupId,this.invitor)+" \u52A0\u5165\u4E86\u7FA4\u7EC4";this.fromSelf?e="\u60A8\u9080\u8BF7:":e=h.getGroupMemberDisplayName(this.groupId,this.invitor)+"\u9080\u8BF7:";let t="";return h.getUserInfos(this.invitees,this.groupId).forEach(i=>{t+=" "+i.displayName}),e+t+"\u52A0\u5165\u4E86\u7FA4\u7EC4"}encode(){let e=super.encode(),t={g:this.groupId,o:this.invitor,ms:this.invitees};return e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){super.decode(e);let t=h.b64_to_utf8(e.binaryContent),s=JSON.parse(t);this.groupId=s.g,this.invitor=s.o,this.invitees=s.ms}},ut=class extends J{constructor(e){super(u.ChangeGroupPortrait_Notification),o(this,"operator",""),this.operator=e}formatNotification(){return this.fromSelf?"\u60A8\u4FEE\u6539\u7FA4\u5934\u50CF":h.getGroupMemberDisplayName(this.groupId,this.operator)+"\u4FEE\u6539\u4E86\u7FA4\u5934\u50CF"}encode(){let e=super.encode(),t={g:this.groupId,n:this.name};return e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){super.decode(e);let t=h.b64_to_utf8(e.binaryContent),s=JSON.parse(t);this.groupId=s.g,this.operator=s.o}},dt=class extends J{constructor(e,t){super(u.CreateGroup_Notification),o(this,"creator",""),o(this,"groupName",""),this.creator=e,this.groupName=t}formatNotification(){return this.fromSelf?"\u60A8\u521B\u5EFA\u4E86\u7FA4\u7EC4 "+this.groupName:h.getUserDisplayName(this.creator)+"\u521B\u5EFA\u4E86\u7FA4\u7EC4 "+this.groupName}encode(){let e=super.encode(),t={g:this.groupId,n:this.groupName,o:this.creator};return e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){super.decode(e);let t=h.b64_to_utf8(e.binaryContent),s=JSON.parse(t);this.groupId=s.g,this.creator=s.o,this.groupName=s.n}},gt=class extends J{constructor(e){super(u.DismissGroup_Notification),o(this,"operator",""),this.operator=e}formatNotification(){return this.fromSelf?"\u60A8\u89E3\u6563\u4E86\u7FA4\u7EC4":h.getGroupMemberDisplayName(this.groupId,this.operator)+"\u89E3\u6563\u4E86\u7FA4\u7EC4"}encode(){let e=super.encode(),t={g:this.groupId,o:this.operator};return e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){super.decode(e);let t=h.b64_to_utf8(e.binaryContent),s=JSON.parse(t);this.groupId=s.g,this.operator=s.o}},ht=class extends J{constructor(e,t){super(u.ModifyGroupAlias_Notification),o(this,"operator",""),o(this,"alias",""),o(this,"memberId",""),this.operator=e,this.alias=t}formatNotification(){let e="";if(this.fromSelf)e+="\u4F60";else{let t=h.getUserInfo(this.operator,!1,this.groupId);t.friendAlias?e+=t.friendAlias:t.displayName?e+=t.displayName:e+=this.operator}if(e+="\u4FEE\u6539",this.memberId){let t=h.getUserInfo(this.memberId,!1);t.friendAlias?e+=t.friendAlias:t.displayName?e+=t.displayName:e+=this.memberId,e+="\u7684"}return e+="\u7FA4\u6635\u79F0\u4E3A",e+=this.alias,e}encode(){let e=super.encode(),t={g:this.groupId,n:this.alias,o:this.operator,m:this.memberId};return e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){super.decode(e);let t=h.b64_to_utf8(e.binaryContent),s=JSON.parse(t);this.groupId=s.g,this.operator=s.o,this.alias=s.n,this.memberId=s.m}},pt=class extends J{constructor(e,t){super(u.ModifyGroupExtra_Notification),o(this,"operator",""),o(this,"groupExtra",""),this.operator=e,this.groupExtra=t}formatNotification(){let e="";if(this.fromSelf)e+="\u4F60";else{let t=h.getUserInfo(this.operator,!1,this.groupId);t.friendAlias?e+=t.friendAlias:t.displayName?e+=t.displayName:e+=this.operator}return e+="\u4FEE\u6539",e+="\u7FA4\u9644\u52A0\u4FE1\u606F\u4E3A",e+=this.groupExtra,e}encode(){let e=super.encode(),t={g:this.groupId,n:this.groupExtra,o:this.operator};return e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){super.decode(e);let t=h.b64_to_utf8(e.binaryContent),s=JSON.parse(t);this.groupId=s.g,this.operator=s.o,this.groupExtra=s.n}},ft=class extends J{constructor(e,t){super(u.ModifyGroupMemberExtra_Notification),o(this,"operator",""),o(this,"groupMemberExtra",""),o(this,"memberId",""),this.operator=e,this.groupMemberExtra=t}formatNotification(){let e="";if(this.fromSelf)e+="\u4F60";else{let t=h.getUserInfo(this.operator,!1,this.groupId);t.friendAlias?e+=t.friendAlias:t.displayName?e+=t.displayName:e+=this.operator}if(e+="\u4FEE\u6539",this.memberId){let t=h.getUserInfo(this.memberId,!1);t.friendAlias?e+=t.friendAlias:t.displayName?e+=t.displayName:e+=this.memberId,e+="\u7684"}return e+="\u7FA4\u6210\u5458\u4FE1\u606F\u4E3A",e+=this.groupMemberExtra,e}encode(){let e=super.encode(),t={g:this.groupId,n:this.groupMemberExtra,o:this.operator,m:this.memberId};return e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){super.decode(e);let t=h.b64_to_utf8(e.binaryContent),s=JSON.parse(t);this.groupId=s.g,this.operator=s.o,this.groupMemberExtra=s.n,this.memberId=s.m}},mt=class extends J{constructor(e){super(u.QuitGroup_Notification),o(this,"operator",""),this.operator=e}formatNotification(){return this.fromSelf?"\u60A8\u9000\u51FA\u4E86\u7FA4\u7EC4":h.getGroupMemberDisplayName(this.groupId,this.operator)+"\u9000\u51FA\u4E86\u7FA4\u7EC4"}encode(){let e=super.encode(),t={g:this.groupId,o:this.operator};return e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){super.decode(e);let t=h.b64_to_utf8(e.binaryContent),s=JSON.parse(t);this.groupId=s.g,this.operator=s.o}},Ct=class extends J{constructor(e){super(u.QuitGroup_Visible_Notification),o(this,"operator",""),this.operator=e}formatNotification(){return this.fromSelf?"\u60A8\u9000\u51FA\u4E86\u7FA4\u7EC4":h.getGroupMemberDisplayName(this.groupId,this.operator)+"\u9000\u51FA\u4E86\u7FA4\u7EC4"}encode(){let e=super.encode(),t={g:this.groupId,o:this.operator};return e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){super.decode(e);let t=h.b64_to_utf8(e.binaryContent),s=JSON.parse(t);this.groupId=s.g,this.operator=s.o}},At=class extends J{constructor(e,t){super(u.TransferGroupOwner_Notification),o(this,"operator",""),o(this,"newOwner",""),this.operator=e,this.newOwner=t}formatNotification(){return this.fromSelf?"\u60A8\u628A\u7FA4\u8F6C\u8BA9\u7ED9\u4E86 "+h.getGroupMemberDisplayName(this.groupId,this.newOwner):h.getGroupMemberDisplayName(this.groupId,this.operator)+"\u628A\u7FA4\u8F6C\u8BA9\u7ED9\u4E86 "+h.getGroupMemberDisplayName(this.groupId,this.newOwner)}encode(){let e=super.encode(),t={g:this.groupId,o:this.operator,m:this.newOwner};return e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){super.decode(e);let t=h.b64_to_utf8(e.binaryContent),s=JSON.parse(t);this.groupId=s.g,this.operator=s.o,this.newOwner=s.m}},vt=class extends ie{constructor(e,t,s,i){super(u.File,G.File,e,t),o(this,"name",""),o(this,"size",0),e&&e.name?(this.name=e.name,this.size=e.size):t&&(this.name=s||t.substring(t.lastIndexOf("/")+1),this.size=i||0)}digest(){return"[\u6587\u4EF6]"+this.name}encode(){let e=super.encode();return e.searchableContent=this.name,e.content=this.size+"",e}decode(e){super.decode(e),e.searchableContent&&(this.name=e.searchableContent,this.size=Number(e.content))}},yt=class extends ie{constructor(e,t,s,i=0){super(u.Video,G.Video,e,t),o(this,"thumbnail"),o(this,"duration"),this.thumbnail=s,this.duration=i}digest(){return"[\u89C6\u9891]"}encode(){let e=super.encode();e.binaryContent=this.thumbnail;let t={d:this.duration,duration:this.duration};return e.content=JSON.stringify(t),e.mediaType=G.Video,e}decode(e){if(super.decode(e),this.thumbnail=e.binaryContent,e.content){let t=JSON.parse(e.content);this.duration=t.d,this.duration===void 0&&(this.duration=t.duration)}}},St=class extends ie{constructor(e,t,s,i){super(u.Sticker,G.Sticker,e,t),o(this,"width",200),o(this,"height",200),this.width=s,this.height=i}digest(){return"[\u8868\u60C5]"}encode(){let e=super.encode();e.mediaType=G.File;let t={x:this.width,y:this.height};return e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){super.decode(e);let t=JSON.parse(h.b64_to_utf8(e.binaryContent));this.width=t.x,this.height=t.y}},bt=class extends ie{constructor(e,t,s){super(u.Voice,G.Voice,e,t),o(this,"duration"),this.duration=s}digest(){return"[\u8BED\u97F3]"}encode(){let e=super.encode();e.mediaType=G.Voice;let t={duration:this.duration};return e.content=JSON.stringify(t),e}decode(e){super.decode(e);let t=JSON.parse(e.content);this.duration=t.duration}},ye=class $s extends O{constructor(e){super(u.Typing),o(this,"typingType",$s.TYPING_TEXT),this.typingType=e}digest(){return this.content}encode(){let e=super.encode();return e.content=this.typingType+"",e}decode(e){super.decode(e),this.typingType=parseInt(e.content)}};o(ye,"TYPING_TEXT",0);o(ye,"TYPING_VOICE",1);o(ye,"TYPING_CAMERA",2);o(ye,"TYPING_LOCATION",3);o(ye,"TYPING_FILE",4);var ji=ye,Je=class extends Q{constructor(e,t){super(u.RecallMessage_Notification),o(this,"operatorId",""),o(this,"messageUid",new E(0)),o(this,"originalSender"),o(this,"originalContentType"),o(this,"originalSearchableContent"),o(this,"originalContent"),o(this,"originalExtra"),o(this,"originalMessageTimestamp"),this.operatorId=e,this.messageUid=t}formatNotification(e){return this.operatorId===h.getUserId()?"\u4F60\u64A4\u56DE\u4E86\u4E00\u6761\u6D88\u606F":e.conversation.type===k.Group?h.getGroupMemberDisplayName(e.conversation.target,this.operatorId)+"\u64A4\u56DE\u4E86\u4E00\u6761\u6D88\u606F":h.getUserDisplayName(this.operatorId)+"\u64A4\u56DE\u4E86\u4E00\u6761\u6D88\u606F"}encode(){let e=super.encode();return e.content=this.operatorId,e.binaryContent=h.utf8_to_b64(this.messageUid.toString()),e}decode(e){super.decode(e),this.operatorId=e.content,this.messageUid=E.fromString(h.b64_to_utf8(e.binaryContent));try{this.setExtra(e.extra)}catch(t){f("error","at wfc/messages/notification/recallMessageNotification.js:54","decode recallMessage extra error",t)}}setExtra(e){if(e){e=e.replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\t/g,"\\t");let t=JSON.parse(e);this.originalSender=t.s,this.originalContentType=t.t,this.originalSearchableContent=t.sc,this.originalContent=t.c,this.originalExtra=t.e,this.originalMessageTimestamp=E.fromValue(t.ts)}}},Et=class extends O{constructor(e,t){super(u.DeleteMessage_Notification),o(this,"operatorId",""),o(this,"messageUid",new E(0)),this.operatorId=e,this.messageUid=t}formatNotification(e){return"\u6D88\u606F\u5DF2\u5220\u9664"}encode(){let e=super.encode();return e.content=this.operatorId,e.binaryContent=h.utf8_to_b64(this.messageUid.toString()),e}decode(e){super.decode(e),this.operatorId=e.content,this.messageUid=E.fromString(h.b64_to_utf8(e.binaryContent))}},Mt=class extends O{constructor(e=0,t=[]){super(u.VOIP_CONTENT_TYPE_START,e,t),o(this,"callId"),o(this,"targetIds",[]),o(this,"connectTime"),o(this,"endTime"),o(this,"status",0),o(this,"audioOnly"),o(this,"pin"),o(this,"sdkType",0)}digest(){return this.audioOnly?"[\u8BED\u97F3\u901A\u8BDD]":"[\u89C6\u9891\u901A\u8BDD]"}encode(){let e=super.encode();e.content=this.callId,e.pushContent="\u97F3\u89C6\u9891\u901A\u8BDD\u9080\u8BF7";let t={c:this.connectTime,e:this.endTime,s:this.status,a:this.audioOnly?1:0,ts:this.targetIds,t:this.targetIds[0],p:this.pin,ty:this.sdkType};e.binaryContent=h.utf8_to_b64(JSON.stringify(t));let s={callId:this.callId,audioOnly:this.audioOnly,participants:this.targetIds};return e.pushData=JSON.stringify(s),e}decode(e){super.decode(e),this.callId=e.content;let t=h.b64_to_utf8(e.binaryContent),s=JSON.parse(t);this.connectTime=s.c,this.endTime=s.e,this.status=s.s,this.audioOnly=s.a===1,this.targetIds=s.ts,this.targetIds||(this.targetIds=[s.t]),this.pin=s.p,this.sdkType=s.ty}},Nt=class extends O{constructor(e=0,t=[]){super(u.VOIP_CONTENT_TYPE_ACCEPT,e,t),o(this,"callId"),o(this,"audioOnly"),o(this,"inviteMessageUid")}digest(){return""}encode(){let e=super.encode();e.content=this.callId;var t;this.audioOnly?t="1":t="0",e.binaryContent=h.utf8_to_b64(t);let s={u:se(this.inviteMessageUid)},i=JSON.stringify(s);return i=Re(i,"u"),e.extra=i,e}decode(e){super.decode(e),this.callId=e.content;let t=h.b64_to_utf8(e.binaryContent);this.audioOnly=t==="1",t=e.extra,t&&(t=xe(t,"u"),this.inviteMessageUid=Pe(JSON.parse(t).u))}},It=class extends O{constructor(e=0,t=[]){super(u.VOIP_CONTENT_TYPE_ACCEPT_T,e,t),o(this,"callId"),o(this,"audioOnly")}digest(){return""}encode(){let e=super.encode();e.content=this.callId;var t;return this.audioOnly?t="1":t="0",e.binaryContent=h.utf8_to_b64(t),e}decode(e){super.decode(e),this.callId=e.content;let t=h.b64_to_utf8(e.binaryContent);this.audioOnly=t==="1"}},_t=class extends O{constructor(e=0,t=[]){super(u.VOIP_CONTENT_TYPE_END,e,t),o(this,"callId"),o(this,"reason"),o(this,"inviteMsgUid")}digest(){return""}encode(){let e=super.encode();e.content=this.callId;let t={r:this.reason,u:this.inviteMsgUid?se(this.inviteMsgUid):void 0},s=JSON.stringify(t);return s=Re(s,"u"),e.binaryContent=h.utf8_to_b64(s),e.pushData=s,e}decode(e){super.decode(e),this.callId=e.content;let t=h.b64_to_utf8(e.binaryContent);t=xe(t,"u");let s=JSON.parse(t);this.reason=s.r,this.inviteMsgUid=s.u?Pe(s.u):void 0}},Ot=class extends O{constructor(e=0,t=[]){super(u.VOIP_CONTENT_TYPE_SIGNAL,e,t),o(this,"callId"),o(this,"payload")}digest(){return""}encode(){let e=super.encode();return e.content=this.callId,e.binaryContent=h.utf8_to_b64(this.payload),e}decode(e){super.decode(e),this.callId=e.content,this.payload=h.b64_to_utf8(e.binaryContent)}},wt=class extends O{constructor(e=0,t=[]){super(u.VOIP_CONTENT_TYPE_MODIFY,e,t),o(this,"callId"),o(this,"audioOnly")}digest(){return""}encode(){let e=super.encode();e.content=this.callId;var t;return this.audioOnly?t="1":t="0",e.binaryContent=h.utf8_to_b64(t),e}decode(e){super.decode(e),this.callId=e.content;let t=h.b64_to_utf8(e.binaryContent);this.audioOnly=t==="1"}},Tt=class extends Q{constructor(){super(u.VOIP_CONTENT_TYPE_ADD_PARTICIPANT),o(this,"callId"),o(this,"initiator"),o(this,"pin"),o(this,"participants"),o(this,"existParticipants"),o(this,"audioOnly"),o(this,"autoAnswer"),o(this,"clientId")}formatNotification(e){let t="";return this.fromSelf?t="\u60A8\u9080\u8BF7":(t=h.getGroupMemberDisplayName(e.conversation.target,this.initiator),t+="\u9080\u8BF7"),this.participants&&this.participants.forEach(s=>{t+=" ",s===h.getUserId()?t+="\u60A8":t+=h.getGroupMemberDisplayName(e.conversation.target,s)}),t+=" \u52A0\u5165\u4E86\u901A\u8BDD",t}encode(){let e=super.encode();e.content=this.callId;let t={initiator:this.initiator,audioOnly:this.audioOnly?1:0,pin:this.pin,participants:this.participants,existParticipants:this.existParticipants,autoAnswer:this.autoAnswer,clientId:this.clientId};e.binaryContent=h.utf8_to_b64(JSON.stringify(t));let s=this.existParticipants.map(n=>n.userId),i={callId:this.callId,audioOnly:this.audioOnly,participants:this.participants,existParticipants:s};return e.pushData=JSON.stringify(i),e}decode(e){super.decode(e),this.callId=e.content;let t=h.b64_to_utf8(e.binaryContent),s=JSON.parse(t);this.initiator=s.initiator,this.audioOnly=s.audioOnly===1,this.pin=s.pin,this.participants=s.participants,this.existParticipants=s.existParticipants,this.autoAnswer=s.autoAnswer,this.clientId=s.clientId}},Ut=class extends Q{constructor(e=0,t=[]){super(u.VOIP_CONTENT_TYPE_MUTE_VIDEO,e,t),o(this,"callId"),o(this,"videoMuted"),o(this,"existParticipants")}formatNotification(e){return"mute video"}encode(){let e=super.encode();e.content=this.callId;let t={existParticipants:this.existParticipants,videoMuted:this.videoMuted};return e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){super.decode(e),this.callId=e.content;let t=h.b64_to_utf8(e.binaryContent),s=JSON.parse(t);this.existParticipants=s.existParticipants,this.videoMuted=s.videoMuted}},Pt=class extends J{constructor(e,t){super(u.ChangeJoinType_Notification),o(this,"operator"),o(this,"joinType"),this.operator=e,this.type=t}formatNotification(e){let t;switch(this.fromSelf?t="\u60A8":t=h.getGroupMemberDisplayName(this.groupId,this.operator),this.joinType){case 0:t+=" \u5F00\u653E\u4E86\u52A0\u5165\u7FA4\u7EC4\u529F\u80FD";break;case 1:t+=" \u4EC5\u5141\u8BB8\u7FA4\u6210\u5458\u9080\u8BF7\u52A0\u5165\u7FA4\u7EC4";break;case 2:t+=" \u5173\u95ED\u4E86\u52A0\u5165\u7FA4\u7EC4\u529F\u80FD";break}return t}encode(){let e=super.encode(),t={g:this.groupId,o:this.operator,n:this.joinType+""};return e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),super.encode()}decode(e){super.decode(e);let t=JSON.parse(h.b64_to_utf8(e.binaryContent));this.groupId=t.g,this.operator=t.o,this.joinType=parseInt(t.n)}},Rt=class extends J{constructor(e,t){super(u.MuteGroup_Notification),o(this,"operator"),o(this,"muteType"),this.operator=e,this.muteType=t}formatNotification(e){let t=this.fromSelf?"\u60A8":h.getGroupMemberDisplayName(this.groupId,this.operator);return t+=this.muteType===0?" \u5173\u95ED\u4E86\u5168\u5458\u7981\u8A00":" \u5F00\u542F\u4E86\u5168\u5458\u7981\u8A00",t}encode(){let e=super.encode(),t={g:this.groupId,o:this.operator,n:this.muteType+""};return e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){super.decode(e);let t=JSON.parse(h.b64_to_utf8(e.binaryContent));this.groupId=t.g,this.operator=t.o,this.muteType=parseInt(t.n)}},xt=class extends J{constructor(e,t,s){super(u.SetGroupManager_Notification),o(this,"operator"),o(this,"setManagerType"),o(this,"memberIds",[]),this.operator=e,this.setManagerType=t,this.memberIds=s}formatNotification(e){let t=this.fromSelf?"\u60A8":h.getGroupMemberDisplayName(this.groupId,this.operator);return t+="\u628A ",this.memberIds.forEach(s=>{t+=" ",t+=h.getGroupMemberDisplayName(this.groupId,s)}),t+=" ",t+=this.setManagerType===0?"\u53D6\u6D88\u4E86\u7BA1\u7406\u5458":"\u8BBE\u7F6E\u4E3A\u4E86\u7BA1\u7406\u5458",t}encode(){let e=super.encode(),t={g:this.groupId,o:this.operator,n:this.setManagerType+"",ms:this.memberIds};return e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){super.decode(e);let t=JSON.parse(h.b64_to_utf8(e.binaryContent));this.groupId=t.g,this.operator=t.o,this.setManagerType=parseInt(t.n),this.memberIds=t.ms}},Dt=class extends J{constructor(e,t){super(u.ChangePrivateChat_Notification),o(this,"operator"),o(this,"privateChatType"),this.operator=e,this.privateChatType=t}formatNotification(e){let t=this.fromSelf?"\u60A8":h.getGroupMemberDisplayName(this.groupId,this.operator);return t+=this.privateChatType===0?" \u5F00\u542F\u4E86\u6210\u5458\u79C1\u804A":" \u5173\u95ED\u4E86\u6210\u5458\u79C1\u804A",t}encode(){let e=super.encode(),t={g:this.groupId,o:this.operator,n:this.privateChatType+""};return e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){super.decode(e);let t=JSON.parse(h.b64_to_utf8(e.binaryContent));this.groupId=t.g,this.operator=t.o,this.privateChatType=parseInt(t.n)}},kt=class extends O{constructor(){super(...arguments),o(this,"title"),o(this,"thumbnail"),o(this,"lat"),o(this,"long")}digest(){return"\u4F4D\u7F6E"}encode(){let e=super.encode();e.searchableContent=this.title,e.binaryContent=this.thumbnail;let t={lat:this.lat,long:this.long};return e.content=JSON.stringify(t),e}decode(e){super.decode(e),this.title=e.searchableContent,this.thumbnail=e.binaryContent;let t=JSON.parse(e.content);this.lat=t.lat,this.long=t.long}},Gt=class extends J{constructor(e,t,s){super(u.MuteGroupMember_Notification),o(this,"groupId"),o(this,"operator"),o(this,"muteType"),o(this,"memberIds"),this.operator=e,this.muteType=t,this.memberIds=s}formatNotification(e){let t="";return this.fromSelf?t+="\u60A8":t+=h.getGroupMemberDisplayName(this.groupId,this.operator),t+="\u628A",this.memberIds&&this.memberIds.forEach(s=>{t+=" ",t+=h.getGroupMemberDisplayName(this.groupId,s)}),this.muteType===1?t+="\u8BBE\u7F6E\u4E86\u7981\u8A00":t+="\u53D6\u6D88\u4E86\u7981\u8A00",t}encode(){let e=super.encode(),t={g:this.groupId,o:this.operator,n:this.muteType+"",ms:this.memberIds};return e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){super.decode(e);let t=h.b64_to_utf8(e.binaryContent),s=JSON.parse(t);this.groupId=s.g,this.operator=s.o,this.muteType=parseInt(s.n),this.memberIds=s.ms}},Vt=class extends J{constructor(e,t,s){super(u.MuteGroupMember_Notification),o(this,"groupId"),o(this,"operator"),o(this,"type"),o(this,"memberIds"),this.operator=e,this.type=t,this.memberIds=s}formatNotification(e){let t="";return this.fromSelf?t+="\u60A8":t+=h.getGroupMemberDisplayName(this.groupId,this.operator),t+="\u628A",this.memberIds&&this.memberIds.forEach(s=>{t+=" ",t+=h.getGroupMemberDisplayName(this.groupId,s)}),this.type===0?t+="\u53D6\u6D88\u7FA4\u7981\u8A00\u65F6\u53D1\u8A00\u6743\u9650":t+="\u5141\u8BB8\u7FA4\u7981\u8A00\u65F6\u53D1\u8A00",t}encode(){let e=super.encode(),t={g:this.groupId,o:this.operator,n:this.type+"",ms:this.memberIds};return e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){super.decode(e);let t=h.b64_to_utf8(e.binaryContent),s=JSON.parse(t);this.groupId=s.g,this.operator=s.o,this.type=parseInt(s.n),this.memberIds=s.ms}},Jt=class extends O{constructor(e,t,s,i,n){super(u.UserCard),o(this,"cardType"),o(this,"target"),o(this,"name"),o(this,"displayName"),o(this,"portrait"),o(this,"from"),this.cardType=e,this.target=t,this.displayName=s,this.portrait=i,this.from=n}encode(){let e=super.encode();e.content=this.target;let t={t:this.cardType,n:this.name,d:this.displayName,p:this.portrait,f:this.from};return e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){super.decode(e),this.target=e.content;let t=JSON.parse(h.b64_to_utf8(e.binaryContent));this.cardType=t.t,this.name=t.n,this.displayName=t.d,this.portrait=t.p,D.urlRedirect&&(this.portrait=D.urlRedirect(t.p)),this.from=t.f}digest(){let e="[\u540D\u7247]";switch(this.cardType){case 0:e="[\u4E2A\u4EBA\u540D\u7247]";break;case 1:e="[\u7FA4\u7EC4\u540D\u7247]";break;case 2:e="[\u804A\u5929\u5BA4\u540D]";break;case 3:e="[\u9891\u9053\u540D\u7247]";break}return e+this.displayName}},Le=class extends O{constructor(){super(u.Link),o(this,"title"),o(this,"contentDigest"),o(this,"url"),o(this,"thumbnail")}encode(){let e=super.encode(),t={d:this.contentDigest,u:this.url,t:this.thumbnail};return e.searchableContent=this.title,e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){super.decode(e),this.title=e.searchableContent;let t=JSON.parse(h.b64_to_utf8(e.binaryContent));this.contentDigest=t.d,this.url=t.u,this.thumbnail=t.t}digest(e){let t=this.title?this.title.trim():this.contentDigest?this.contentDigest.trim():null;return t||this.url}},Fe=class extends O{constructor(){super(u.Articles),o(this,"topArticle"),o(this,"subArticles")}digest(e){return this.topArticle.title}encode(){let e=super.encode(),t={top:this.topArticle.toJson()};return this.subArticles&&(t.subArticles=this.subArticles.map(s=>s.toJson())),e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){super.decode(e);let t=JSON.parse(h.b64_to_utf8(e.binaryContent));this.topArticle=new je,this.topArticle.fromJson(t.top),t.subArticles&&(this.subArticles=[],t.subArticles.forEach(s=>{let i=new je;i.fromJson(s),this.subArticles.push(i)}))}toLinkMessageContent(){let e=[];return e.push(this.topArticle.toLinkMessageContent()),this.subArticles&&this.subArticles.forEach(t=>{e.push(t.toLinkMessageContent())}),e}},je=class{constructor(){o(this,"articleId"),o(this,"cover"),o(this,"title"),o(this,"digest"),o(this,"url"),o(this,"readReport")}toJson(){return{id:this.articleId,cover:this.cover,title:this.title,url:this.url,digest:this.digest,rr:this.readReport}}fromJson(e){this.articleId=e.id,this.cover=e.cover,this.title=e.title,this.digest=e.digest,this.url=e.url,this.readReport=e.rr}toLinkMessageContent(){let e=new Le;return e.url=this.url,e.title=this.title,e.contentDigest=this.digest,e.thumbnail=this.cover,e}},Lt=class extends ie{constructor(){super(u.Composite_Message,G.General,""),o(this,"title",""),o(this,"messages",[]),o(this,"loaded",!1)}setMessages(e){this.messages=[],e.forEach(t=>{t.messageContent instanceof Fe?t.messageContent.toLinkMessageContent().forEach(i=>{let n=Object.assign(new U,t);n.messageContent=i,this.messages.push(n)}):this.messages.push(t)}),this.messages=this.messages.sort((t,s)=>ms(t.messageUid,s.messageUid))}digest(e){return"[\u804A\u5929\u8BB0\u5F55]"+this.title}encode(){let e=super.encode();e.content=this.title;let t=[],s,i=0;this.messages.forEach(c=>{let d=c.messageContent.encode(),p={uid:se(c.messageUid),type:c.conversation.type,target:c.conversation.target,line:c.conversation.line,from:c.from,tos:c.to,direction:c.direction,status:c.status,serverTime:se(c.timestamp),ctype:d.type,csc:d.searchableContent,cpc:d.pushContent,cpd:d.pushData,cc:d.content,cmt:d.mentionedType,cmts:d.mentionedTargets,ce:d.extra};d.searchableContent&&(e.searchableContent=e.searchableContent+d.searchableContent+" "),d.binaryContent&&(p.cbc=d.binaryContent),c.messageContent instanceof ie&&(p.mt=c.messageContent.mediaType,p.mru=c.messageContent.remotePath),s||(i+=JSON.stringify(p).length,i>20480&&t.length>0&&(s=t.map(A=>A))),t.push(p)});let n;if(s&&!this.file){n={ms:t};let c=JSON.stringify(n);c=c.replace(/"uid":"([0-9]+)"/,'"uid":$1'),c=c.replace(/"serverTime":"([0-9]+)"/,'"serverTime":$1');let d=new Blob([c]),p="wcf-"+new Date().getTime()+".data";this.file=new File([d],p),n={ms:s}}else s?n={ms:s}:n={ms:t};let a=JSON.stringify(n);return a=a.replace(/"uid":"([0-9]+)"/,'"uid":$1'),a=a.replace(/"serverTime":"([0-9]+)"/,'"serverTime":$1'),e.binaryContent=h.utf8_to_b64(a),e}decode(e){super.decode(e),this.title=e.content;let t;if(this.file){let s=new FileReader;s.onload(i=>{this._decodeMessages(i.target.result)}),s.readAsBinaryString(this.file)}else this.localPath;t||(t=h.b64_to_utf8(e.binaryContent),this._decodeMessages(t))}_decodeMessages(e){if(this.loaded)return;this.messages=[],e=e.substring(0,e.lastIndexOf("}")+1),e=e.replace(/"uid":([0-9]+)/g,'"uid":"$1"'),e=e.replace(/"serverTime":([0-9]+)/g,'"serverTime":"$1"'),JSON.parse(e).ms.forEach(s=>{let i=new z(s.type,s.target,s.line),n=new U;n.messageUid=E.fromValue(s.uid),n.conversation=i,n.from=s.from,n.to=s.tos,n.direction=s.direction,n.status=s.status,n.timestamp=E.fromValue(s.serverTime);let a=new Te;a.type=s.ctype,a.searchableContent=s.csc,a.pushContent=s.cpc,a.pushData=s.cpd,a.content=s.cc,a.mentionedType=s.cmt,a.mentionedTargets=s.cmts,a.extra=s.ce,a.binaryContent=s.cbc,a.mediaType=s.mt,a.remoteMediaUrl=s.mru,n.messageContent=U.messageContentFromMessagePayload(a,n.from),this.messages.push(n)}),f("log","at wfc/messages/compositeMessageContent.js:191","cp ms",this.messages)}},Ft=class extends O{constructor(e,t,s,i,n,a,c,d,p){super(u.CONFERENCE_CONTENT_TYPE_INVITE),o(this,"callId"),o(this,"host"),o(this,"title"),o(this,"desc"),o(this,"startTime"),o(this,"audioOnly"),o(this,"audience"),o(this,"pin"),o(this,"advanced"),this.callId=e,this.host=t,this.title=s,this.desc=i,this.startTime=n,this.audioOnly=a,this.audience=c,this.advanced=d,this.pin=p}digest(e){return"[\u4F1A\u8BAE\u9080\u8BF7]"}encode(){let e=super.encode();e.content=this.callId,e.pushContent="\u4F1A\u8BAE\u9080\u8BF7";let t={h:this.host,s:this.startTime,t:this.title,d:this.desc,audience:this.audience?1:0,advanced:this.advanced?1:0,a:this.audioOnly?1:0,p:this.pin};return e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){if(super.decode(e),e.binaryContent){let t=h.b64_to_utf8(e.binaryContent),s=JSON.parse(t);this.host=s.h,this.startTime=s.s,this.title=s.t,this.desc=s.d,this.audience=s.audience>0,this.advanced=s.advanced>0,this.audioOnly=s.a>0,this.pin=s.p}this.callId=e.content}},jt=class extends O{constructor(e,t){super(u.CONFERENCE_CONTENT_TYPE_CHANGE_MODE),o(this,"callId"),o(this,"audience"),this.callId=e,this.audience=t}encode(){let e=super.encode();e.content=this.callId;let t={a:this.audience};return e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){if(super.decode(e),this.callId=e.content,e.binaryContent){let t=h.b64_to_utf8(e.binaryContent),s=JSON.parse(t);this.audience=!!s.a}}},zt=class extends O{constructor(e){super(u.CONFERENCE_CONTENT_TYPE_KICKOFF_MEMBER),o(this,"callId"),this.callId=e}encode(){let e=super.encode();return e.content=this.callId,e}decode(e){super.decode(e),this.callId=e.content}},ze=class extends O{constructor(e){super(u.Mark_Unread_Sync),o(this,"messageUid"),o(this,"timestamp"),this.messageUid=e}encode(){let e=super.encode(),t={u:se(this.messageUid),t:se(this.timestamp)},s=JSON.stringify(t);return s=Re(s,"u"),s=Re(s,"t"),e.binaryContent=h.utf8_to_b64(s),e}decode(e){super.decode(e);let t=h.b64_to_utf8(e.binaryContent);t=xe(t,"u"),t=xe(t,"t");let s=JSON.parse(t);this.messageUid=s.u?Pe(s.u):void 0,this.timestamp=s.t?Pe(s.t):void 0}},Bt=class extends Q{constructor(){super(u.Friend_Added)}formatNotification(){return"\u4F60\u4EEC\u5DF2\u7ECF\u662F\u597D\u53CB\u4E86\uFF0C\u53EF\u4EE5\u5F00\u59CB\u804A\u5929\u4E86\u3002"}},Ht=class extends Q{constructor(){super(u.Friend_Greeting)}formatNotification(e){return"\u4EE5\u4E0A\u662F\u6253\u62DB\u547C\u7684\u5185\u5BB9"}},qt=class extends O{constructor(){super(u.PC_Login_Request),o(this,"platform"),o(this,"sessionId")}digest(){return"[PC\u8BF7\u6C42\u767B\u5F55]"}encode(){let e=super.encode();e.mediaType=G.File;let t={p:this.platform,t:this.sessionId};return e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){super.decode(e);let t=JSON.parse(h.b64_to_utf8(e.binaryContent));this.platform=t.p,this.sessionId=t.t}},te=class{};o(te,"Starting",0);o(te,"Accepting",1);o(te,"Established",2);o(te,"Canceled",3);var Qt=class extends Q{constructor(){super(u.StartSecretChat_Notification)}formatNotification(e){let t=h.getSecretChatInfo(e.conversation.target).state;return t===te.Starting?"\u7B49\u5F85\u5BF9\u65B9\u54CD\u5E94":t===te.Accepting?"\u5BC6\u804A\u4F1A\u8BDD\u5EFA\u7ACB\u4E2D":t===te.Established?"\u5BC6\u804A\u4F1A\u8BDD\u5DF2\u5EFA\u7ACB":t===te.Canceled?"\u5BC6\u804A\u4F1A\u8BDD\u5DF2\u53D6\u6D88":"\u5BC6\u804A\u4F1A\u8BDD\u4E0D\u53EF\u7528"}encode(){return super.encode()}decode(e){super.decode(e)}},Yt=class extends O{constructor(e,t){super(u.VOIP_Join_Call_Request),o(this,"callId"),o(this,"clientId"),this.callId=e,this.clientId=t}digest(){return""}encode(){let e=super.encode();e.content=this.callId;let t={clientId:this.clientId};return e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){super.decode(e),this.callId=e.content;let t=h.b64_to_utf8(e.binaryContent),s=JSON.parse(t);this.clientId=s.clientId}},Kt=class extends Q{constructor(){super(u.Rich_Notification),o(this,"title"),o(this,"desc"),o(this,"remark"),o(this,"datas"),o(this,"exName"),o(this,"exPortrait"),o(this,"exUrl"),o(this,"appId")}formatNotification(){return this.title}encode(){let e=super.encode();e.pushContent=this.title,e.content=this.desc;let t={remark:this.remark,exName:this.exName,exPortrait:this.exPortrait,exUrl:this.exUrl,appId:this.appId,datas:this.datas};return e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){super.decode(e),this.title=e.pushContent,this.desc=e.content;let t=h.b64_to_utf8(e.binaryContent),s=JSON.parse(t);this.remark=s.remark,this.exName=s.exName,this.exPortrait=s.exPortrait,this.exUrl=s.exUrl,this.appId=s.appId,this.datas=s.datas}},Wt=class extends O{constructor(e){super(u.Channel_Menu_Event),o(this,"menu"),this.menu=e}encode(){let e=super.encode();return e.content=JSON.stringify(this.menu),e}},Zt=class extends O{constructor(){super(u.Enter_Channel_Chat)}},Xt=class extends O{constructor(){super(u.Leave_Channel_Chat)}},Be=class extends O{constructor(e,t,s,i){super(u.CONFERENCE_CONTENT_TYPE_COMMAND),o(this,"conferenceId"),o(this,"commandType"),o(this,"targetUserId"),o(this,"boolValue"),this.conferenceId=e,this.commandType=t,this.targetUserId=s,this.boolValue=i}encode(){let e=super.encode();e.content=this.conferenceId;let t={t:this.commandType,u:this.targetUserId};return this.boolValue&&(t.b=this.boolValue),e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){if(super.decode(e),e.binaryContent){let t=h.b64_to_utf8(e.binaryContent),s=JSON.parse(t);this.commandType=s.t,this.boolValue=!!s.b,this.targetUserId=s.u}this.conferenceId=e.content}};o(Be,"ConferenceCommandType",Object.freeze({MUTE_ALL_AUDIO:0,CANCEL_MUTE_ALL_AUDIO:1,REQUEST_MUTE_AUDIO:2,REJECT_UNMUTE_AUDIO_REQUEST:3,APPLY_UNMUTE_AUDIO:4,APPROVE_UNMUTE_AUDIO:5,APPROVE_ALL_UNMUTE_AUDIO:6,HANDUP:7,PUT_HAND_DOWN:8,PUT_ALL_HAND_DOWN:9,RECORDING:10,FOCUS:11,CANCEL_FOCUS:12,MUTE_ALL_VIDEO:13,CANCEL_MUTE_ALL_VIDEO:14,REQUEST_MUTE_VIDEO:15,REJECT_UNMUTE_VIDEO_REQUEST:16,APPLY_UNMUTE_VIDEO:17,APPROVE_UNMUTE_VIDEO:18,APPROVE_ALL_UNMUTE_VIDEO:19}));var $t=class extends O{constructor(){super(u.Streaming_Text_Generating),o(this,"text",""),o(this,"streamId","")}digest(e){return this.text}encode(){let e=super.encode();return e.searchableContent=this.text,e.content=this.streamId,e}decode(e){super.decode(e),this.text=e.searchableContent,this.streamId=e.content}},es=class extends O{constructor(){super(u.Streaming_Text_Generated),o(this,"text",""),o(this,"streamId","")}digest(e){return this.text}encode(){let e=super.encode();return e.searchableContent=this.text,e.content=this.streamId,e}decode(e){super.decode(e),this.text=e.searchableContent,this.streamId=e.content}},ei=class fe{static getMessageContentClazz(e){for(let t of fe.MessageContents)if(t.type===e)return t.contentClazz?t.contentClazz:Ge;return f("error","at wfc/client/messageConfig.js:85",`message type ${e} is unknown`),ce}static getMessageContentFlag(e){let t=y.No_Persist;for(let s of fe.MessageContents)s.type===e&&(t=s.flag);return t}static getMessageContentPersitFlag(e){for(let t of fe.MessageContents)if(t.type===e)return t.flag;return-1}static getMessageContentType(e){for(let t of fe.MessageContents)if(t.contentClazz&&e instanceof t.contentClazz)return t.type;return u.Unknown}static registerMessageContent(e,t,s,i){fe.MessageContents.push({name:e,flag:t,type:s,contentClazz:i})}};o(ei,"MessageContents",[{name:"unknown",flag:y.Persist,type:u.Unknown,contentClazz:ce},{name:"text",flag:y.Persist_And_Count,type:u.Text,contentClazz:ke},{name:"ptext",flag:y.Persist,type:u.P_Text,contentClazz:nt},{name:"voice",flag:y.Persist_And_Count,type:u.Voice,contentClazz:bt},{name:"image",flag:y.Persist_And_Count,type:u.Image,contentClazz:rt},{name:"location",flag:y.Persist_And_Count,type:u.Location,contentClazz:kt},{name:"file",flag:y.Persist_And_Count,type:u.File,contentClazz:vt},{name:"video",flag:y.Persist_And_Count,type:u.Video,contentClazz:yt},{name:"sticker",flag:y.Persist_And_Count,type:u.Sticker,contentClazz:St},{name:"link",flag:y.Persist_And_Count,type:u.Link,contentClazz:Le},{name:"userCard",flag:y.Persist_And_Count,type:u.UserCard,contentClazz:Jt},{name:"compositeMessage",flag:y.Persist_And_Count,type:u.Composite_Message,contentClazz:Lt},{name:"tip",flag:y.Persist,type:u.Tip_Notification,contentClazz:ot},{name:"typing",flag:y.Transparent,type:u.Typing,contentClazz:ji},{name:"friendGreeting",flag:y.Persist,type:u.Friend_Added,contentClazz:Ht},{name:"friendAdded",flag:y.Persist,type:u.Friend_Greeting,contentClazz:Bt},{name:"addGroupMemberNotification",flag:y.Persist,type:u.AddGroupMember_Notification,contentClazz:Ve},{name:"changeGroupNameNotification",flag:y.Persist,type:u.ChangeGroupName_Notification,contentClazz:at},{name:"changeGroupPortraitNotification",flag:y.Persist,type:u.ChangeGroupPortrait_Notification,contentClazz:ut},{name:"createGroupNotification",flag:y.Persist,type:u.CreateGroup_Notification,contentClazz:dt},{name:"dismissGroupNotification",flag:y.Persist,type:u.DismissGroup_Notification,contentClazz:gt},{name:"kickoffGroupMemberNotification",flag:y.Persist,type:u.KickOffGroupMember_Notification,contentClazz:lt},{name:"kickoffGroupMemberVisiableNotification",flag:y.Persist,type:u.KickOffGroupMember_Visible_Notification,contentClazz:ct},{name:"modifyGroupAliasNotification",flag:y.Persist,type:u.ModifyGroupAlias_Notification,contentClazz:ht},{name:"modifyGroupExtraNotification",flag:y.Persist,type:u.ModifyGroupExtra_Notification,contentClazz:pt},{name:"modifyGroupMemberExtraNotification",flag:y.Persist,type:u.ModifyGroupMemberExtra_Notification,contentClazz:ft},{name:"quitGroupNotification",flag:y.Persist,type:u.QuitGroup_Notification,contentClazz:mt},{name:"quitGroupVisiableNotification",flag:y.Persist,type:u.QuitGroup_Visible_Notification,contentClazz:Ct},{name:"transferGroupOwnerNotification",flag:y.Persist,type:u.TransferGroupOwner_Notification,contentClazz:At},{name:"groupJoinTypeNotificationContent",flag:y.Persist,type:u.ChangeJoinType_Notification,contentClazz:Pt},{name:"groupMuteNotificationContent",flag:y.Persist,type:u.MuteGroup_Notification,contentClazz:Rt},{name:"groupPrivateChatNotificationContent",flag:y.Persist,type:u.ChangePrivateChat_Notification,contentClazz:Dt},{name:"groupSetManagerNotificationContent",flag:y.Persist,type:u.SetGroupManager_Notification,contentClazz:xt},{name:"muteGroupMemberNotificationContent",flag:y.Persist,type:u.MuteGroupMember_Notification,contentClazz:Gt},{name:"allowGroupMemberNotificationContent",flag:y.Persist,type:u.AllowGroupMember_Notification,contentClazz:Vt},{name:"recall",flag:y.Persist,type:u.RecallMessage_Notification,contentClazz:Je},{name:"delete",flag:y.No_Persist,type:u.DeleteMessage_Notification,contentClazz:Et},{name:"streamingTextGenerating",flag:y.Transparent,type:u.Streaming_Text_Generating,contentClazz:$t},{name:"streamingTextGenerated",flag:y.Persist_And_Count,type:u.Streaming_Text_Generated,contentClazz:es},{name:"callStartMessageContent",flag:y.Persist,type:u.VOIP_CONTENT_TYPE_START,contentClazz:Mt},{name:"callAnswerMessageContent",flag:y.No_Persist,type:u.VOIP_CONTENT_TYPE_ACCEPT,contentClazz:Nt},{name:"callAnswerTMessageContent",flag:y.Transparent,type:u.VOIP_CONTENT_TYPE_ACCEPT_T,contentClazz:It},{name:"callByeMessageContent",flag:y.No_Persist,type:u.VOIP_CONTENT_TYPE_END,contentClazz:_t},{name:"callSignalMessageContent",flag:y.Transparent,type:u.VOIP_CONTENT_TYPE_SIGNAL,contentClazz:Ot},{name:"callModifyMessageContent",flag:y.No_Persist,type:u.VOIP_CONTENT_TYPE_MODIFY,contentClazz:wt},{name:"callAddParticipant",flag:y.Persist,type:u.VOIP_CONTENT_TYPE_ADD_PARTICIPANT,contentClazz:Tt},{name:"callMuteVideo",flag:y.No_Persist,type:u.VOIP_CONTENT_TYPE_MUTE_VIDEO,contentClazz:Ut},{name:"conferenceInvite",flag:y.Persist_And_Count,type:u.CONFERENCE_CONTENT_TYPE_INVITE,contentClazz:Ft},{name:"conferenceChangeMode",flag:y.Transparent,type:u.CONFERENCE_CONTENT_TYPE_CHANGE_MODE,contentClazz:jt},{name:"conferenceKickoffMember",flag:y.Transparent,type:u.CONFERENCE_CONTENT_TYPE_KICKOFF_MEMBER,contentClazz:zt},{name:"multiCallOngoing",flag:y.Transparent,type:u.VOIP_Multi_Call_Ongoing,contentClazz:Ye},{name:"joinCallRequest",flag:y.Transparent,type:u.VOIP_Join_Call_Request,contentClazz:Yt},{name:"markUnreadMessage",flag:y.No_Persist,type:u.Mark_Unread_Sync,contentClazz:ze},{name:"pcLoginRequest",flag:y.No_Persist,type:u.PC_Login_Request,contentClazz:qt},{name:"startSecretChat",flag:y.Persist_And_Count,type:u.StartSecretChat_Notification,contentClazz:Qt},{name:"richNotification",flag:y.Persist_And_Count,type:u.Rich_Notification,contentClazz:Kt},{name:"articlesMessageContent",flag:y.Persist_And_Count,type:u.Articles,contentClazz:Fe},{name:"channelMenuEventMessageContent",flag:y.Transparent,type:u.Channel_Menu_Event,contentClazz:Wt},{name:"enterChannelChatMessageContent",flag:y.Transparent,type:u.Enter_Channel_Chat,contentClazz:Zt},{name:"leaveChannelChatMessageContent",flag:y.Transparent,type:u.Leave_Channel_Chat,contentClazz:Xt},{name:"conferenceCommandMessageContent",flag:y.Transparent,type:u.CONFERENCE_CONTENT_TYPE_COMMAND,contentClazz:Be}]);var de=ei,j=class{};o(j,"All",-1);o(j,"Sending",0);o(j,"Sent",1);o(j,"SendFailure",2);o(j,"Mentioned",3);o(j,"AllMentioned",4);o(j,"Unread",5);o(j,"Readed",6);o(j,"Played",7);var Ce="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",zi=typeof Uint8Array=="undefined"?[]:new Uint8Array(256);for(Ee=0;Ee<Ce.length;Ee++)zi[Ce.charCodeAt(Ee)]=Ee;var Ee,Bi=function(r){var e=new Uint8Array(r),t,s=e.length,i="";for(t=0;t<s;t+=3)i+=Ce[e[t]>>2],i+=Ce[(e[t]&3)<<4|e[t+1]>>4],i+=Ce[(e[t+1]&15)<<2|e[t+2]>>6],i+=Ce[e[t+2]&63];return s%3===2?i=i.substring(0,i.length-1)+"=":s%3===1&&(i=i.substring(0,i.length-2)+"=="),i},U=class r{constructor(e,t){o(this,"conversation",{}),o(this,"from",""),o(this,"content",{}),o(this,"messageContent",{}),o(this,"messageId",0),o(this,"direction",0),o(this,"status",0),o(this,"messageUid",-1),o(this,"timestamp",0),o(this,"to",""),o(this,"localExtra",""),this.conversation=e,this.messageContent=t}static fromProtoMessage(e){if(!e||!e.conversation.target)return null;if([1,2,3,4,7,8,9].indexOf(D.getWFCPlatform())>=0){let t=Object.assign(new r,e);if(t.messageId=Number(t.messageId),t.messageId===-1)return null;t.messageUid=E.fromValue(t.messageUid),t.timestamp?t.timestamp=E.fromValue(t.timestamp).toNumber():t.timestamp=E.fromValue(t.serverTime).toNumber(),t.localExtra=e.localExtra,t.from||(t.from=e.sender),t.conversation=new z(e.conversation.conversationType!==void 0?e.conversation.conversationType:e.conversation.type,e.conversation.target,e.conversation.line);let s=de.getMessageContentClazz(t.content.type!==void 0?t.content.type:t.content.messageContentType);if(s){let i=new s;try{i.decode(t.content),i instanceof Q&&(i.fromSelf=t.from===h.getUserId())}catch(n){f("error","at wfc/messages/message.js:107","decode message payload failed, fallback to unkownMessage",t.content,n);let a=de.getMessageContentPersitFlag(t.content.type);if(y.Persist===a||y.Persist_And_Count===a)i=new ce(t.content);else return null}t.messageContent=i,i instanceof ce&&f("log","at wfc/messages/message.js:117","unknownMessage Content",e)}else f("error","at wfc/messages/message.js:121","message content not register",e);return t}else{let t=new r;t.from=e.fromUser,t.content=e.content,t.messageUid=e.messageId,t.localExtra=e.localExtra,t.timestamp=e.serverTimestamp;let s=de.getMessageContentClazz(e.content.type);if(s){let i=new s;try{e.content.data&&e.content.data.length>0&&(e.content.binaryContent=Bi(e.content.data)),i.decode(e.content),i.extra=e.content.extra,i instanceof Q&&(i.fromSelf=t.from===h.getUserId())}catch(n){f("error","at wfc/messages/message.js:146","decode message payload failed, fallback to unkownMessage",e.content,n);let a=de.getMessageContentPersitFlag(e.content.type);if(y.Persist===a||y.Persist_And_Count===a)i=new ce(e.content);else return null}t.messageContent=i,i instanceof ce&&f("log","at wfc/messages/message.js:156","unknownMessage Content",e)}else f("error","at wfc/messages/message.js:159","message content not register",e);if(t.from===h.getUserId())t.conversation=new z(e.conversation.type,e.conversation.target,e.conversation.line),t.direction=0,t.status=j.Sent;else if(e.conversation.type===k.Single?t.conversation=new z(e.conversation.type,e.fromUser,e.conversation.line):t.conversation=new z(e.conversation.type,e.conversation.target,e.conversation.line),t.direction=1,t.status=j.Unread,t.content.mentionedType===2)t.status=j.AllMentioned;else if(t.content.mentionedType===1){for(let i of t.content.mentionedTarget)if(i===h.getUserId()){t.status=j.Mentioned;break}}return t}}static messageContentFromMessagePayload(e,t){let s=de.getMessageContentClazz(e.type);s=s||Ge;let i=new s;i.decode(e);let n=h.getUserId();return i instanceof Q&&(i instanceof Je?i.operatorId===n&&(i.fromSelf=!0):t===n&&(i.fromSelf=!0)),i}static toMessagePayload(e){return e.messageContent.encode()}},He=class r{constructor(){o(this,"conversation",{}),o(this,"lastMessage",{}),o(this,"timestamp",0),o(this,"draft",""),o(this,"unreadCount",{}),o(this,"top",0),o(this,"isSilent",!1),o(this,"target")}static protoConversationToConversationInfo(e){let t=Object.assign(new r,e);return t.top=e.isTop,delete t.isTop,e.conversation?t.conversation=new z(e.conversation.type,e.conversation.target,e.conversation.line):t.conversation=new z(e.conversationType,e.target,e.line),t.lastMessage=U.fromProtoMessage(e.lastMessage),t.timestamp||(t.timestamp=0),t}portrait(){let e="";switch(this.conversation.type){case k.Single:e=h.getUserInfo(this.conversation.target,!1).portrait;break;case k.Group:e=h.getGroupInfo(this.conversation.target,!1).portrait;break}return e}title(){let e=this.conversation.target;switch(this.conversation.type){case k.Single:e=h.getUserDisplayName(this.conversation.target);break;case k.Group:e=h.getGroupInfo(this.conversation.target,!1).name;break}return e}static equals(e,t){if(!e||!t||!e.conversation.equal(t.conversation))return!1;let s=e.unreadCount,i=t.unreadCount;return s.unread!==i.unread||s.unreadMention!==i.unreadMention||s.unreadMentionAll!==i.unreadMentionAll||e.lastMessage&&!t.lastMessage||!e.lastMessage&&t.lastMessage||e.lastMessage&&t.lastMessage&&e.lastMessage.messageId!==t.lastMessage.messageId?!1:Ji(e.timestamp,t.timestamp)&&e.draft===t.draft}},M=class{};o(M,"SendMessage","sendMsg");o(M,"ReceiveMessage","receiveMsg");o(M,"MessageReceived","msgReceived");o(M,"MessageRead","msgRead");o(M,"RecallMessage","recallMsg");o(M,"MessageDeleted","msgDeleted");o(M,"DeleteMessage","deleteMsg");o(M,"MessageStatusUpdate","msgStatusUpdate");o(M,"ConnectionStatusChanged","connectionStatusChanged");o(M,"ConnectToServer","connectToServer");o(M,"UserInfosUpdate","userInfosUpdate");o(M,"ChannelInfosUpdate","channelInfosUpdate");o(M,"GroupInfosUpdate","groupInfosUpdate");o(M,"FriendListUpdate","friendListUpdate");o(M,"FriendRequestUpdate","friendRequestUpdate");o(M,"ConversationInfoUpdate","conversationInfoUpdate");o(M,"SettingUpdate","settingUpdate");o(M,"GroupMembersUpdate","groupMembersUpdate");o(M,"ConferenceEvent","conferenceEvent");o(M,"UserOnlineEvent","onlineEvent");o(M,"SecretChatStartBurn","secretChatStartBurn");o(M,"SecretChatMessageBurned","secretChatMessageBurned");o(M,"SecretChatStateChange","secretChatStateChange");var ne=class{constructor(){o(this,"uid",""),o(this,"name",""),o(this,"displayName",""),o(this,"groupAlias",""),o(this,"friendAlias",""),o(this,"gender",0),o(this,"portrait",""),o(this,"mobile",""),o(this,"email",""),o(this,"address",""),o(this,"social",""),o(this,"extra",""),o(this,"type",0),o(this,"deleted",0),o(this,"updateDt",0)}},Me=class extends ne{constructor(e){super(),this.uid=e,this.name="\u7528\u6237",this.displayName=this.name,this.portrait=""}},he=class{};o(he,"Normal",0);o(he,"Free",1);o(he,"Restricted",2);o(he,"Organization",3);var ue=class{constructor(){o(this,"target",""),o(this,"name",""),o(this,"portrait",""),o(this,"owner",""),o(this,"type",he.Normal),o(this,"memberCount",0),o(this,"extra",""),o(this,"remark",""),o(this,"updateDt",0),o(this,"deleted",0),o(this,"memberDt",0),o(this,"mute",0),o(this,"joinType",0),o(this,"privateChat",0),o(this,"superGroup",0),o(this,"searchable"),o(this,"historyMessage"),o(this,"maxMemberCount")}},ts=class extends ue{constructor(e){super(),this.target=e,this.name="\u7FA4\u804A"}},ge=class{constructor(){o(this,"groupId",""),o(this,"memberId",""),o(this,"alias",""),o(this,"extra",""),o(this,"type",0),o(this,"updateDt",0),o(this,"createDt",0)}getName(){return h.getUserInfo(this.memberId).displayName}getPortrait(){return h.getUserInfo(this.memberId).portrait}},I=class{};o(I,"ConversationSilent",1);o(I,"GlobalSilent",2);o(I,"ConversationTop",3);o(I,"HiddenNotificationDetail",4);o(I,"GroupHideNickname",5);o(I,"FavoriteGroup",6);o(I,"Conversation_Sync",7);o(I,"My_Channel",8);o(I,"Listened_Channel",9);o(I,"UserSettingPCOnline",10);o(I,"UserSettingConversationReaded",11);o(I,"WebOnline",12);o(I,"DisableReceipt",13);o(I,"FavoriteUser",14);o(I,"MuteWhenPCOnline",15);o(I,"LinesReaded",16);o(I,"NoDisturbing",17);o(I,"ConversationClearMessage",18);o(I,"ConversationDraft",19);o(I,"DisableSyncDraft",20);o(I,"VoipSilent",21);o(I,"PttReserved",22);o(I,"CustomState",23);o(I,"DisableSecretChat",24);o(I,"PttSilent",25);o(I,"GroupRemark",26);o(I,"kUserSettingCustomBegin",1e3);var qe=class{constructor(){o(this,"unread",0),o(this,"unreadMention",0),o(this,"unreadMentionAll",0)}},ss=class r{constructor(){o(this,"conversation"),o(this,"matchMessage"),o(this,"timestamp"),o(this,"matchCount")}static fromProtoConversationSearchResult(e){let t=new r;return e.conversation?t.conversation=new z(e.conversation.conversationType!==void 0?e.conversation.conversationType:e.conversation.type,e.conversation.target,e.conversation.line):t.conversation=new z(e.conversationType,e.target,e.line),t.matchCount=e.marchedCount,e.marchedCount===1&&(t.matchMessage=U.fromProtoMessage(e.marchedMessage)),t.timestamp=Number(e.timestamp),t}},is=class r{constructor(){o(this,"groupInfo"),o(this,"matchType"),o(this,"matchMembers",[])}static fromProtoGroupSearchResult(e){let t=new r;return t.groupInfo=Object.assign(new ue,e.groupInfo),t.matchType=e.marchedType,t.matchMembers=e.marchedMemberNames,t}},Qe=class{constructor(){o(this,"direction"),o(this,"target"),o(this,"reason"),o(this,"extra"),o(this,"status"),o(this,"readStatus"),o(this,"timestamp")}},ns=class{constructor(){o(this,"memberCount"),o(this,"members")}},K=class{constructor(){o(this,"channelId"),o(this,"name"),o(this,"portrait"),o(this,"desc"),o(this,"owner"),o(this,"status"),o(this,"extra"),o(this,"updateDt"),o(this,"menus")}};o(K,"StatusMaskFullInfo",1);o(K,"StatusMaskUnsubscribedUserAccess",2);o(K,"StatusMaskActiveSubscribe",4);o(K,"StatusMaskMessageUnsubscribed",8);o(K,"StatusMaskPrivate",16);o(K,"StatusMaskDeleted",64);o(K,"StatusMaskGlobal",128);var L=class{static desc(e){return{"-9":"\u5BA2\u6237\u7AEF\u548CIM \u670D\u52A1\u7AEF\u65F6\u95F4\u4E0D\u540C\u6B65\uFF0C\u8BF7\u8FDB\u884C\u65F6\u949F\u540C\u6B65","-8":"IM \u670D\u52A1\u672A\u6388\u6743\u6216\u5DF2\u8FC7\u671F\uFF0C\u4E13\u4E1A\u7248IM-Server \u662F\u7ED1\u5B9A\u57DF\u540D\u6216\u8005 ip \u7684\uFF0C\u53EA\u80FD\u901A\u8FC7\u6240\u7ED1\u5B9A\u7684\u57DF\u540D\u53BB\u8FDE\u63A5","-7":"\u88AB\u8E22\u4E0B\u7EBF","-6":"\u4F1A\u8BDD\u5BC6\u94A5\u9519\u8BEF\uFF0C\u8BF7\u53C2\u8003 https://docs.wildfirechat.cn/faq/general.html \u7B2C12\u4E2A\u95EE\u9898\u6392\u67E5","-5":"token\u9519\u8BEF","-4":"IM Server\u670D\u52A1\u65E0\u6CD5\u8FDE\u901A\uFF0C\u8BF7\u68C0\u67E5\u670D\u52A1\u5668\u662F\u5426\u5B95\u673A\u6216\u8005\u7F51\u7EDC\u51FA\u73B0\u95EE\u9898","-3":"\u8FDE\u63A5\u88AB\u670D\u52A1\u5668\u62D2\u7EDD\uFF0C\u4E00\u822C\u662F\u7528\u6237\u88AB\u5C01\u7981","-2":"\u9000\u51FA\u767B\u5F55","-1":"\u8FDE\u63A5\u5931\u8D25",0:"\u8FDE\u63A5\u4E2D",1:"\u8FDE\u63A5\u6210\u529F\uFF0C\u6B63\u5E38\u72B6\u6001\uFF0C\u6240\u6709\u4E1A\u52A1\u53EF\u7528",2:"\u6B63\u5728\u540C\u6B65\u4FE1\u606F\uFF0C\u767B\u5F55\u4EE5\u540E\u8981\u5148\u540C\u6B65\u6D88\u606F\uFF0C\u53EF\u80FD\u540C\u6B65\u6570\u636E\u91CF\u6BD4\u8F83\u5927\uFF0C\u8FD9\u65F6\u53EF\u4EE5\u9009\u62E9\u7B49\u5F85\u8FDE\u63A5\u72B6\u6001\u53D8\u4E3A1\u65F6\u6765\u7EDF\u4E00\u66F4\u65B0UI"}[e]}};o(L,"ConnectionStatusTimeInconsistent",-9);o(L,"ConnectionStatusNotLicensed",-8);o(L,"ConnectionStatusKickedOff",-7);o(L,"ConnectionStatusSecretKeyMismatch",-6);o(L,"ConnectionStatusTokenIncorrect",-5);o(L,"ConnectionStatusServerDown",-4);o(L,"ConnectionStatusRejected",-3);o(L,"ConnectionStatusLogout",-2);o(L,"ConnectionStatusUnconnected",-1);o(L,"ConnectionStatusConnecting",0);o(L,"ConnectionStatusConnected",1);o(L,"ConnectionStatusReceiveing",2);var rs=class{constructor(){o(this,"userId"),o(this,"conversation"),o(this,"readTime")}},os=class{constructor(){o(this,"userId"),o(this,"conversation"),o(this,"messageUid"),o(this,"name"),o(this,"url"),o(this,"size"),o(this,"downloadCount"),o(this,"timestamp")}},as=class extends K{constructor(e){super(),this.channelId=e,this.name=`<${e}>`}},ls=class{constructor(){o(this,"userId"),o(this,"customState"),o(this,"clientStates")}desc(){if(this.customState.state>0){let s=["\u672A\u8BBE\u7F6E","\u5FD9\u788C","\u79BB\u5F00(\u4E3B\u52A8\u79BB\u5F00)","\u79BB\u5F00(\u957F\u65F6\u95F4\u672A\u64CD\u4F5C)","\u9690\u8EAB"];return this.customState.text+s[this.customState.state]}let e="",t="";return this.clientStates.forEach(s=>{let i=["","iOS","Android","Windows","mac","Web","\u5C0F\u7A0B\u5E8F","Linux","iPad","Android-Pad"];s.state===0?e+=i[s.platform]+" ":[1,2,8,9].indexOf(s.platform)>=0&&(t+=i[s.platform]+" ")}),e.trim()?e+"\u5728\u7EBF":t.trim()?t+"\u4E0D\u4E45\u524D\u5728\u7EBF":""}},cs=class{constructor(){o(this,"state"),o(this,"text")}},us=class{constructor(){o(this,"platform"),o(this,"state"),o(this,"lastSeen")}},g=Ne("wf-uni-wfc-client"),ds=class{constructor(){o(this,"connectionStatus",0),o(this,"userId",""),o(this,"token",""),o(this,"isLogined",!1),o(this,"firstSync",!0),o(this,"messageContentList",[]),o(this,"eventEmitter",null),o(this,"needPreloadDefaultData",!1),o(this,"_nativeEvent2WfcEvent",{onChannelInfoUpdate:this.onChannelInfoUpdate,onClearMessage:null,onConferenceEvent:this.onConferenceEvent,onConnectToServer:this.onConnectToServer,onConnectionStatusChange:this.onConnectionChanged,onConversationDraftUpdate:null,onDeleteMessage:this.onDeleteRemoteMessage,onFriendListUpdate:this.onFriendListUpdate,onFriendRequestUpdate:this.onFriendRequestUpdate,onGroupInfoUpdate:this.onGroupInfoUpdate,onGroupMembersUpdate:this.onGroupMemberUpdateListener,onServiceConnected:null,onMessageDelivered:this.onUserReceivedMessage,onMessageRead:this.onUserReadedMessage,onMessageUpdate:this.onMessageUpdate,onReceiveMessage:this.onReceiveMessage,onRecallMessage:this.onRecallMessage,onConversationRemove:null,onSettingUpdate:this.onSettingUpdate,onTrafficData:null,onUserInfoUpdate:this.onUserInfoUpdate,onUserOnlineEvent:this.onOnlineEvent,onSendSuccess:this.onSendSuccess,onSendFail:this.onSendFail,onSendPrepare:this.onSendPrepare,onProgress:this.onProgress,onMediaUpload:this.onMediaUpload})}onConnectionChanged(e){if(e=Number(e),!l.isLogined&&e==L.ConnectionStatusConnected&&(l.isLogined=!0),e===L.ConnectionStatusConnected)if(l.needPreloadDefaultData){let t=l._preloadDefaultData();f("log","at wfc/proto/proto.min.js:90","to load default data",t),setTimeout(()=>{l.connectionStatus=e,l.eventEmitter.emit(M.ConnectionStatusChanged,e)},t)}else l.connectionStatus=e,l.eventEmitter.emit(M.ConnectionStatusChanged,e);else l.connectionStatus=e,l.eventEmitter.emit(M.ConnectionStatusChanged,e);f("log","at wfc/proto/proto.min.js:103","connection status changed",e),l.isLogined&&(e==L.ConnectionStatusSecretKeyMismatch||e===L.ConnectionStatusRejected)&&l.disconnect()}onConnectToServer(e,t,s){f("log","at wfc/proto/proto.min.js:110","connect to server",e,t,s),l.eventEmitter.emit(M.ConnectToServer,e)}onReceiveMessage(e,t){if(l.isLogined&&!(l.connectionStatus===2&&l.firstSync)){l.connectionStatus===1&&(l.firstSync=!1);var s=JSON.parse(e);s.forEach(i=>{let n=U.fromProtoMessage(i);if(n.messageContent instanceof ze&&n.from===l.userId){let a=n.messageContent,c=n.conversation;g.setLastReceivedMessageUnRead(JSON.stringify(c),se(a.messageUid),se(a.timestamp))}n&&l.eventEmitter.emit(M.ReceiveMessage,n,t)})}}onConferenceEvent(e){l.eventEmitter.emit(M.ConferenceEvent,e)}onOnlineEvent(e){let t=l._parseUserOnlineState(e);l.eventEmitter.emit(M.UserOnlineEvent,t)}onSendSuccess(e){let t=U.fromProtoMessage(JSON.parse(e));l.eventEmitter.emit(M.MessageStatusUpdate,t)}onSendFail(e,t){let s=U.fromProtoMessage(JSON.parse(e));l.eventEmitter.emit(M.MessageStatusUpdate,s)}onSendPrepare(e,t){let s=U.fromProtoMessage(JSON.parse(e));l.eventEmitter.emit(M.SendMessage,s)}onProgress(e,t,s){}onMediaUpload(e,t){let s=U.fromProtoMessage(JSON.parse(e));l.eventEmitter.emit(M.MessageStatusUpdate,s)}onMessageUpdate(e){let t=U.fromProtoMessage(JSON.parse(e));l.eventEmitter.emit(M.MessageStatusUpdate,t)}_parseUserOnlineState(e){let t=[];return JSON.parse(e).forEach(i=>{let n=new ls;n.userId=i.userId,n.customState=new cs,n.customState.state=i.customState,n.customState.text=i.customText,n.clientStates=[];let a=i.states?i.states:i.clientStates;a&&a.forEach(c=>{let d=new us;d.state=c.state,d.platform=c.platform,d.lastSeen=E.fromValue(c.lastSeen),n.clientStates.push(d)}),t.push(n)}),t}onGroupInfoUpdate(e){if(!l.isLogined)return;let t=JSON.parse(e),s=[];t.forEach(i=>{s.push(Object.assign(new ue,i))}),l.eventEmitter.emit(M.GroupInfosUpdate,s)}onChannelInfoUpdate(e){if(!l.isLogined)return;let t=JSON.parse(e),s=[];t.forEach(i=>{s.push(l.getChannelInfo(i,!1))}),l.eventEmitter.emit(M.ChannelInfosUpdate,s)}onGroupMemberUpdateListener(e,t){if(!l.isLogined)return;let s=[],i=[];JSON.parse(t).forEach(a=>{s.push(Object.assign(new ge,a)),i.push(a.memberId)}),l._preloadGroupMemberUserInfos(i),l.eventEmitter.emit(M.GroupMembersUpdate,e,s)}onSettingUpdate(){l.isLogined&&l.eventEmitter.emit(M.SettingUpdate)}onRecallMessage(e,t){l.isLogined&&l.eventEmitter.emit(M.RecallMessage,e,E.fromValue(t))}onDeleteRemoteMessage(e){l.isLogined&&l.eventEmitter.emit(M.MessageDeleted,E.fromValue(e))}onUserReceivedMessage(e){if(!l.isLogined)return;let t=JSON.parse(e),s=new Map;t.forEach(i=>{s.set(i.key,i.value)}),l.eventEmitter.emit(M.MessageReceived,s),f("log","at wfc/proto/proto.min.js:306","onreceive",s)}onUserReadedMessage(e){if(!l.isLogined)return;let t=JSON.parse(e),s=[];t.forEach(i=>{let n=new rs;n.userId=i.userId,i.conversation?n.conversation=Object.assign(new z,i.conversation):n.conversation=new z(i.conversationType,i.target,i.line),n.readTime=i.readDt,s.push(n)}),l.eventEmitter.emit(M.MessageRead,s)}onMessageDeleted(e){l.isLogined&&l.eventEmitter.emit(M.DeleteMessage,e)}onUserInfoUpdate(e){if(!l.isLogined)return;let s=JSON.parse(e).map(i=>Object.assign(new ne,i));l.eventEmitter.emit(M.UserInfosUpdate,s)}onFriendListUpdate(e){if(!l.isLogined)return;f("log","at wfc/proto/proto.min.js:352","friendList update, ids",e);let t=JSON.parse(e);l.eventEmitter.emit(M.FriendListUpdate,t)}onFriendRequestUpdate(e="[]"){if(!l.isLogined)return;f("log","at wfc/proto/proto.min.js:361","friend request list update, new incomming requests",e);let t=JSON.parse(e);l.eventEmitter.emit(M.FriendRequestUpdate,t)}init(e=[]){f("log","at wfc/proto/proto.min.js:368","proto init"),g.initProto(),plus.globalEvent.addEventListener("wfc-event",t=>{l._handleNativeEvent(t)}),l.connectionStatus=l.getConnectionStatus(),l.isLogined=l.connectionStatus===L.ConnectionStatusConnected,f("log","at wfc/proto/proto.min.js:383","proto init end")}registerMessageContent(e,t,s,i){de.MessageContents.push({name:e,flag:t,type:s,contentClazz:i}),g.registerMessageFlag(s,t)}useSM4(){g.useSM4()}connect(e,t){e=e+"",l.userId=e;let s=g.connect(D.IM_SERVER_HOST,e,t);return f("log","at wfc/proto/proto.min.js:407","connect",e,D.IM_SERVER_HOST),new Date().getTime()/1e3-s>7*24*60*60&&(l.needPreloadDefaultData=!0),s}setProxyInfo(e,t,s,i,n){g.setProxyInfo(e,t,s,i,n)}setPackageName(e){}disconnect(e,t){l.userId="",g.disconnect(e,t);for(var s=new Date,i=s.getTime()+1e3;;)if(s=new Date,s.getTime()>i)return}registerDefaultMessageContents(){de.MessageContents.map(e=>{g.registerMessageFlag(e.type,e.flag)})}getClientId(){return g.getClientId()}getUserId(){try{return g.getUserId()}catch(e){return l.userId}}getServerDeltaTime(){return g.getServerDeltaTime()}screenShot(){}isLogin(){return l.isLogined}getConnectionStatus(){return g.getConnectionStatus()}setBackupAddressStrategy(e){g.setBackupAddressStrategy(e)}setBackupAddress(e,t){g.setBackupAddress(e,t)}setProtoUserAgent(e){if(typeof e!="string"){f("error","at wfc/proto/proto.min.js:487","setProtoUserAgent userAgent must be string");return}g.setUserAgent(e)}addHttpHeader(e,t){if(typeof e!="string"||typeof t!="string"){f("error","at wfc/proto/proto.min.js:495","addHttpHeader header, value must be string");return}g.addHttpHeader(e,t)}onAppResume(){}onAppSuspend(){}getMyGroupList(){return this.getUserSettings(I.FavoriteGroup).filter(s=>s.value==="1").map(s=>this.getGroupInfo(s.key,!1))}getUserInfo(e,t=!1,s=""){if(!e||e==="")return new Me("");let i,n=g.getUserInfo(e,t,s);return n===""?i=new Me(e):i=Object.assign(new ne,JSON.parse(n)),i}getUserInfos(e,t=""){if(!e||e.length===0)return[];let s=[],i=g.getUserInfos(e,t);return i&&i!==""&&JSON.parse(i).forEach(a=>{let c=Object.assign(new ne,a);s.push(c)}),s}getUserInfoEx(e,t,s,i){return v(this,null,function*(){g.getUserInfoEx(e,t,n=>{let a=Object.assign(new ne,JSON.parse(n));s&&s(a)},n=>{n&&i(n)})})}searchUser(e,t,s,i,n){return v(this,null,function*(){g.searchUser(e,t,s,a=>{let c=JSON.parse(a),d=[];c&&c.length>0&&c.forEach(p=>{d.push(Object.assign(new ne,p))}),i&&i(e,d)},a=>{a&&n(a)})})}searchFriends(e){let t=g.searchFriends(e),s=JSON.parse(t),i=[];return s&&s.length>0&&s.forEach(n=>{i.push(Object.assign(new ne,n))}),i}searchGroups(e){let t=g.searchGroups(e),s=JSON.parse(t),i=[];return s&&s.length>0&&s.forEach(n=>{i.push(is.fromProtoGroupSearchResult(n))}),i}getIncommingFriendRequest(){let e=g.getIncommingFriendRequest(),t=JSON.parse(e),s=[];return t&&t.length>0&&t.forEach(i=>{s.push(Object.assign(new Qe,i))}),s}getOutgoingFriendRequest(){let e=g.getOutgoingFriendRequest(),t=JSON.parse(e),s=[];return t&&t.length>0&&t.forEach(i=>{s.push(Object.assign(new Qe,i))}),s}getFriendRequest(e,t){let s=g.getFriendRequest(e,t);return JSON.parse(s)}loadFriendRequestFromRemote(){g.loadFriendRequestFromRemote()}getFavUsers(){let e=g.getFavUsers();return JSON.parse(e)}isFavUser(e){return g.isFavUser(e)}setFavUser(e,t,s,i){g.setFavUser(e,t,()=>{s&&s()},n=>{i&&i(n)})}loadRemoteMessages(e,t,s,i,n,a){t||(t=[]),g.getRemoteMessages(JSON.stringify(e),s+"",i,c=>{var d=JSON.parse(c);let p=[];d.map(A=>{let b=U.fromProtoMessage(A);b&&p.push(b)}),f("log","at wfc/proto/proto.min.js:680","loadRemoteMessages",p.length),n&&n(p)},c=>{f("log","at wfc/proto/proto.min.js:683","loadRemoteMessages failure:",c),a&&a(c)},t)}loadRemoteMessage(e,t,s){g.getRemoteMessage(se(e),i=>{var n=JSON.parse(i);let a=[];n.map(c=>{let d=U.fromProtoMessage(c);d&&a.push(d)}),f("log","at wfc/proto/proto.min.js:698","loadRemoteMessage",a.length),t&&t(a)},i=>{f("log","at wfc/proto/proto.min.js:701","loadRemoteMessage failure:",i),s&&s(i)})}getUnreadFriendRequestCount(){return g.getUnreadFriendRequestStatus()}clearUnreadFriendRequestStatus(){g.clearUnreadFriendRequestStatus()}deleteFriend(e,t,s){return v(this,null,function*(){g.deleteFriend(e,()=>{t&&t()},i=>{s(i)})})}handleFriendRequest(e,t,s,i,n){return v(this,null,function*(){g.handleFriendRequest(e,t,()=>{i&&i()},a=>{n&&n(a)},s)})}isBlackListed(e){return g.isBlackListed(e)}getBlackList(){let e=g.getBlackList();return JSON.parse(e)}setBlackList(e,t,s,i){g.setBlackList(e,t,()=>{s&&s()},n=>{i&&i(n)})}getMyFriendList(e=!1){let t=g.getMyFriendList(e);return t!==""?JSON.parse(t):[]}getFriendList(e=!1){let t=g.getFriendList(e);return JSON.parse(t)}getFriendAlias(e){return g.getFriendAlias(e)}setFriendAlias(e,t,s,i){return v(this,null,function*(){g.setFriendAlias(e,t,s,i)})}createGroup(N,V,oe,As){return v(this,arguments,function*(e,t,s,i,n="",a=[],c="",d=[0],p,A,b){e=e||"",n=n||"",c=c||"";let vs=l.getUserId();a.includes(vs)||a.push(vs);let ti=p?p.encode():"",si=JSON.stringify(ti);g.createGroup(e,t,s,i,n,a,c,d,si,Ze=>{A&&A(Ze)},Ze=>{b&&b(Ze)})})}setGroupManager(d,p){return v(this,arguments,function*(e,t,s=[],i=[0],n,a,c){let A=n?n.encode():"",b=JSON.stringify(A);g.setGroupManager(e,t,s,i,b,a,c)})}muteOrAllowGroupMembers(p,A,b,N){return v(this,arguments,function*(e,t,s,i,n=[0],a,c,d){if(s){let V=a?a.encode():"",oe=JSON.stringify(V);g.allowGroupMember(e,t,i,n,oe,()=>{c&&c()},As=>{d&&d()})}else{let V=a?a.encode():"",oe=JSON.stringify(V);g.muteGroupMember(e,t,i,n,oe,()=>{c&&c()},As=>{d&&d(As)})}})}getGroupInfo(e,t=!1){let s,i=g.getGroupInfo(e,t);return i===""?new ts(e):(s=Object.assign(new ue,JSON.parse(i)),s)}getGroupInfos(e,t=!1){f("log","at wfc/proto/proto.min.js:845","get groupInfos",e.length,t);let s=g.getGroupInfos(e,t);if(s==="")return[];{let i=JSON.parse(s),n=[];return i.forEach(a=>{n.push(Object.assign(new ue,a))}),n}}getGroupInfoEx(e,t,s,i){return v(this,null,function*(){g.getGroupInfoEx(e,t,n=>{let a=Object.assign(new ue,JSON.parse(n));s&&s(a)},n=>{n&&i(n)})})}addGroupMembers(e,t,s,i,n,a,c){n||(n=new Ve(l.getUserId(),t));let d=n.encode(),p=JSON.stringify(d);s=s||"",g.addMembers(e,t,s,i,p,()=>{a&&a()},A=>{c&&c(A)})}getGroupMemberIds(e,t=!1){let s=l.getGroupMembers(e,t);var i=[];return s.forEach(n=>{i.push(n.memberId)}),i}getGroupMembers(e,t=!1){let s=g.getGroupMembers(e,t);var i=[];return JSON.parse(s).forEach(a=>{i.push(Object.assign(new ge,a))}),i}getGroupMembersByType(e,t){let s=g.getGroupMembersByType(e,t);var i=[];return JSON.parse(s).forEach(a=>{i.push(Object.assign(new ge,a))}),i}getGroupMember(e,t){let s=g.getGroupMember(e,t);return Object.assign(new ge,JSON.parse(s))}getGroupMembersEx(e,t,s,i){return v(this,null,function*(){g.getGroupMembersEx(e,t,n=>{var a=[];JSON.parse(n).forEach(d=>{a.push(Object.assign(new ge,d))}),s&&s(a)},n=>{n&&i(n)})})}kickoffGroupMembers(e,t,s,i,n,a){let c=i?i.encode():"",d=JSON.stringify(c);g.kickoffMembers(e,t,s,d,()=>{n&&n()},p=>{a&&a(p)})}quitGroup(a){return v(this,arguments,function*(e,t=[0],s,i,n){let c=s?s.encode():"";g.quitGroup(e,t,JSON.stringify(c),()=>{i&&i()},d=>{n(d)})})}quitGroupEx(c,d){return v(this,arguments,function*(e,t,s=[0],i,n,a){let p=i?i.encode():"";g.quitGroupEx(e,t,s,JSON.stringify(p),()=>{n&&n()},A=>{a(A)})})}dismissGroup(a){return v(this,arguments,function*(e,t=[0],s,i,n){let c=s?s.encode():"";g.dismissGroup(e,t,JSON.stringify(c),()=>{i&&i()},d=>{n(d)})})}modifyGroupInfo(d,p,A){return v(this,arguments,function*(e,t,s,i=[0],n,a,c){let b=n?n.encode():"";g.modifyGroupInfo(e,t,s,i,JSON.stringify(b),()=>{a&&a()},N=>{c&&c(N)})})}modifyGroupAlias(c,d){return v(this,arguments,function*(e,t,s=[0],i,n,a){let p=i?i.encode():"";g.modifyGroupAlias(e,t,s,JSON.stringify(p),()=>{n()},A=>{a(A)})})}modifyGroupMemberAlias(d,p,A){return v(this,arguments,function*(e,t,s,i=[0],n,a,c){let b=n?n.encode():"";g.modifyGroupMemberAlias(e,t,s,i,JSON.stringify(b),()=>{a()},N=>{c(N)})})}modifyGroupMemberExtra(d,p,A){return v(this,arguments,function*(e,t,s,i=[0],n,a,c){let b=n?n.encode():"";g.modifyGroupMemberExtra(e,t,s,i,JSON.stringify(b),()=>{a()},N=>{c(N)})})}transferGroup(e,t,s=[0],i,n,a){let c=i?i.encode():"";g.transferGroup(e,t,s,JSON.stringify(c),()=>{n&&n()},d=>{a&&a(d)})}getFavGroups(){let e=g.getFavGroups();return JSON.parse(e)}isFavGroup(e){return g.isFavGroup(e)}setFavGroup(e,t,s,i){return v(this,null,function*(){g.setFavGroup(e,t,()=>{s&&s()},n=>{i&&i(n)})})}getUserSetting(e,t){return g.getUserSetting(e,t)}getUserSettings(e){let t=g.getUserSettings(e);return JSON.parse(t)}setUserSetting(e,t,s,i,n){return v(this,null,function*(){g.setUserSetting(e,t,s,()=>{i&&i()},a=>{n&&n(a)})})}modifyMyInfo(e,t,s){g.modifyMyInfo(e[0].type,e[0].value,()=>{t&&t()},i=>{s&&s(i)})}isGlobalSlient(){return g.isGlobalSlient()}setGlobalSlient(e,t,s){g.setGlobalSlient(e,()=>{t&&t()},i=>{s&&s(i)})}isHiddenNotificationDetail(){return g.isHiddenNotificationDetail()}setHiddenNotificationDetail(e,t,s){return v(this,null,function*(){g.setHiddenNotificationDetail(e,()=>{t&&t()},i=>{s&&s(i)})})}isHiddenGroupMemberName(e){return g.getUserSetting(I.GroupHideNickname,e)==="1"}setHiddenGroupMemberName(e,t,s,i){return v(this,null,function*(){g.setUserSetting(I.GroupHideNickname,e,t?"1":"0",()=>{s()},n=>{i(n)})})}isUserReceiptEnabled(){return g.isUserReceiptEnabled()}setUserEnableReceipt(e,t,s){return v(this,null,function*(){g.setUserReceiptEnable(e,()=>{t&&t()},i=>{s&&s(i)})})}joinChatroom(e,t,s){return v(this,null,function*(){g.joinChatroom(e,()=>{t&&t()},i=>{s&&s(i)})})}quitChatroom(e,t,s){return v(this,null,function*(){g.quitChatroom(e,()=>{t&&t()},i=>{s&&s(i)})})}getChatroomInfo(e,t,s,i){return v(this,null,function*(){g.getChatroomInfo(e,t,n=>{s&&s(Object.assign(new Oe,JSON.parse(n)))},n=>{i&&i(n)})})}getChatroomMemberInfo(e,t,s,i){return v(this,null,function*(){g.getChatroomMemberInfo(e,t,n=>{s&&s(Object.assign(new ns,JSON.parse(n)))},n=>{i&&i(n)})})}createChannel(e,t,s,i,n,a){g.createChannel(e,t,0,s,i,c=>{n&&n(Object.assign(new K,JSON.parse(c)))},c=>{a&&a(c)})}getChannelInfo(e,t){let s=g.getChannelInfo(e,t);return s===""?new as(e):Object.assign(new K,JSON.parse(s))}modifyChannelInfo(e,t,s,i,n){return v(this,null,function*(){g.modifyChannelInfo(e,t,s,()=>{i&&i()},a=>{n&&n(a)})})}searchChannel(e,t,s){g.searchChannel(e,i=>{if(t){let n=[];JSON.parse(i).forEach(c=>{n.push(Object.assign(new K,c))}),t(n)}},i=>{s&&s(i)})}isListenedChannel(e){return g.isListenedChannel(e)}listenChannel(e,t,s,i){return v(this,null,function*(){g.listenChannel(e,t,()=>{s()},n=>{i(n)})})}getMyChannels(){let e=g.getMyChannels();return JSON.parse(e)}getListenedChannels(){let e=g.getListenedChannels();return JSON.parse(e)}destoryChannel(e,t,s){return v(this,null,function*(){g.destoryChannel(e,()=>{t&&t()},i=>{s&&s(i)})})}getConversationList(e,t){var s=g.getConversationInfos(e,t);if(!s)return f("log","at wfc/proto/proto.min.js:1297","getConversationInfos return null"),[];let i=[];return JSON.parse(s).forEach(a=>{i.push(He.protoConversationToConversationInfo(a))}),i}getConversationInfo(e){let t=g.getConversationInfo(JSON.stringify(e));return He.protoConversationToConversationInfo(JSON.parse(t))}searchConversation(e,t=[],s=[]){let i=g.searchConversation(e,t,s),n=JSON.parse(i);var a=[];return n&&n.length>0&&n.forEach(c=>{a.push(ss.fromProtoConversationSearchResult(c))}),a}removeConversation(e,t=!1){return v(this,null,function*(){g.removeConversation(JSON.stringify(e),t)})}setConversationTop(e,t,s,i){g.setConversationTop(JSON.stringify(e),t,()=>{let n=l.getConversationInfo(e);l.eventEmitter.emit(M.ConversationInfoUpdate,n),s&&s()},n=>{i&&i(n)})}setConversationSlient(e,t,s,i){g.setConversationSlient(JSON.stringify(e),t,()=>{let n=l.getConversationInfo(e);l.eventEmitter.emit(M.ConversationInfoUpdate,n),s&&s()},n=>{i&&i(n)})}setConversationDraft(e,t=""){let s=l.getConversationInfo(e);s.draft!==t&&(g.setConversationDraft(JSON.stringify(e),t),s=l.getConversationInfo(e),l.eventEmitter.emit(M.ConversationInfoUpdate,s))}setConversationTimestamp(e,t){t=t+"",g.setConversationTimestamp(JSON.stringify(e),t);let s=l.getConversationInfo(e);l.eventEmitter.emit(M.ConversationInfoUpdate,s)}getUnreadCount(e=[],t=[0]){let s=g.getUnreadCount(e,t);return Object.assign(new qe,JSON.parse(s))}getConversationUnreadCount(e){let t=g.getConversationUnreadCount(JSON.stringify(e));return Object.assign(new qe,JSON.parse(t))}clearConversationUnreadStatus(e){f("log","at wfc/proto/proto.min.js:1390","clearConversationUnreadStatus",JSON.stringify(e)),g.clearUnreadStatus(JSON.stringify(e));let t=l.getConversationInfo(e);l.eventEmitter.emit(M.ConversationInfoUpdate,t)}markConversationAsUnread(e,t){let s=g.setLastReceivedMessageUnRead(JSON.stringify(e),"0","0");if(s){let i=l.getConversationInfo(e);l.eventEmitter.emit(M.ConversationInfoUpdate,i)}return s}getConversationRead(e){let t=g.getConversationRead(JSON.stringify(e)),s=JSON.parse(t),i=new Map;return s&&s.forEach(n=>{i.set(n.key,n.value)}),i}clearAllUnreadStatus(){g.clearAllUnreadStatus()}getConversationFirstUnreadMessageId(e){return g.getConversationFirstUnreadMessageId(JSON.stringify(e))}setMediaMessagePlayed(e){g.setMediaMessagePlayed(e)}setMessageLocalExtra(e,t){g.setMessageLocalExtra(e,t)}isMyFriend(e){return g.isMyFriend(e)}sendFriendRequest(e,t,s,i,n){return v(this,null,function*(){s=s||"",g.sendFriendRequest(e,t,s,()=>{i&&i()},a=>{n&&n(a)})})}getMessages(e,t,s=!0,i=20,n="",a=[]){let c=g.getMessages(JSON.stringify(e),a,t,s,i,n);var d=JSON.parse(c);let p=[];return d.map(A=>{let b=U.fromProtoMessage(A);b&&p.push(b)}),f("log","at wfc/proto/proto.min.js:1474","getMessages",p.length),p}getMessagesEx(e,t,s,i,n=!0,a=20,c=""){let d=g.getMessagesEx(e,t,s,i,n,a,c);var p=JSON.parse(d);let A=[];return p.map(b=>{let N=U.fromProtoMessage(b);N&&A.push(N)}),f("log","at wfc/proto/proto.min.js:1491","getMessages",A.length),A}getUserMessages(e,t,s,i=!0,n=20,a=[]){let c=g.getUserMessages(e,JSON.stringify(t),a,s,i,n);var d=JSON.parse(c);let p=[];return d.map(A=>{let b=U.fromProtoMessage(A);b&&p.push(b)}),f("log","at wfc/proto/proto.min.js:1506","getMessages",p.length),p}getUserMessagesEx(e,t,s,i,n=!0,a=20,c=[]){let d=g.getUserMessagesEx(e,t,s,c,i,n,a);var p=JSON.parse(d);let A=[];return p.map(b=>{let N=U.fromProtoMessage(b);N&&A.push(N)}),f("log","at wfc/proto/proto.min.js:1522","getMessages",A.length),A}getMessagesV2(e,t,s,i,n,a,c){g.getMessagesV2(JSON.stringify(e),t,s,i,n,...this._wrapperGetMessageCallbacks(a,c))}getMessagesExV2(e,t,s,i,n,a,c,d,p){g.getMessagesExV2(e,t,s,i,n,a,c,...this._wrapperGetMessageCallbacks(d,p))}getMessagesEx2V2(e,t,s,i,n,a,c,d,p){g.getMessagesEx2V2(e,t,s,i,n,a,c,...this._wrapperGetMessageCallbacks(d,p))}getMessagesByTimestampV2(e,t,s,i,n,a,c,d){g.getMessagesByTimestampV2(JSON.stringify(e),t,Fi(s),i,n,a,...this._wrapperGetMessageCallbacks(c,d))}getUserMessagesV2(e,t,s,i,n,a,c){g.getUserMessagesV2(e,JSON.stringify(t),s,i,n,...this._wrapperGetMessageCallbacks(a,c))}getUserMessagesExV2(e,t,s,i,n,a,c,d,p){g.getUserMessagesExV2(e,t,s,i,n,a,c,...this._wrapperGetMessageCallbacks(d,p))}_wrapperGetMessageCallbacks(e,t){return[i=>{var n=JSON.parse(i);let a=[];n.map(c=>{let d=U.fromProtoMessage(c);d&&a.push(d)}),e&&e(a)},i=>{t&&t(i)}]}getMessageById(e){let t=g.getMessage(e);return U.fromProtoMessage(JSON.parse(t))}getMessageByUid(e){f("log","at wfc/proto/proto.min.js:1577","getMesssageByUid",E.fromValue(e).toString());let t=g.getMessageByUid(E.fromValue(e).toString());return f("log","at wfc/proto/proto.min.js:1579","getMesssageByUid----",t),U.fromProtoMessage(JSON.parse(t))}searchMessage(e,t,s=""){let i=g.searchMessage(JSON.stringify(e),t,s),n=JSON.parse(i),a=[];return n&&n.length>0&&n.forEach(c=>{a.push(U.fromProtoMessage(c))}),a}searchMessageEx(e,t,s,i,n,a){let c=g.searchMessageEx(JSON.stringify(e),t,s,i,n,a),d=JSON.parse(c),p=[];return d&&d.length>0&&d.forEach(A=>{p.push(U.fromProtoMessage(A))}),p}searchMessageByTypes(e,t,s,i,n,a,c=""){s||(s=[]);let d=g.searchMessageByTypes(JSON.stringify(e),t,s,i,n,a,c),p=JSON.parse(d),A=[];return p&&p.length>0&&p.forEach(b=>{A.push(U.fromProtoMessage(b))}),A}searchMessageByTypesAndTimes(e,t,s,i,n,a,c,d,p){s||(s=[]);let A=g.searchMessageByTypesAndTimes(JSON.stringify(e),t,s,i.toString(),n.toString(),a,c,d,p),b=JSON.parse(A),N=[];return b&&b.length>0&&b.forEach(V=>{N.push(U.fromProtoMessage(V))}),N}searchMessageEx2(e,t,s,i,n,a,c){s||(s=[]);let d=g.searchMessageEx2(e,t,s,i,n,a,c),p=JSON.parse(d),A=[];return p&&p.length>0&&p.forEach(b=>{A.push(U.fromProtoMessage(b))}),A}sendConversationMessage(d,p){return v(this,arguments,function*(e,t,s=[],i,n,a,c){let A=new U;A.conversation=e,A.messageContent=t,l.sendMessageEx(A,s,i,n,a,c)})}sendMessage(e,t,s,i,n){return v(this,null,function*(){l.sendMessageEx(e,[],t,s,i,n)})}sendMessageEx(c){return v(this,arguments,function*(e,t=[],s,i,n,a){let d=JSON.stringify(e.conversation);e.content=yield e.messageContent.encode(),e.from=this.userId,f("log","at wfc/proto/proto.min.js:1673","--------------p",e.content);let p=JSON.stringify(e.content);g.sendMessage(d,p,t,0,A=>{let[b,N]=A;e.messageId=b,e.timestamp=E.fromValue(N).toNumber(),typeof s=="function"&&s(b,E.fromValue(N).toNumber())},A=>{let[b,N]=A;typeof i=="function"&&i(b,N)},A=>{let[b,N]=A;if(e.status=j.Sent,e.messageUid=E.fromValue(b),e.timestamp=E.fromValue(N).toNumber(),e.messageContent instanceof ie){let V=l.getMessageById(e.messageId);e.messageContent=V.messageContent}typeof n=="function"&&n(E.fromValue(b),E.fromValue(N).toNumber())},A=>{e.status=j.SendFailure,typeof a=="function"&&a(A)})})}recallMessage(e,t,s){return v(this,null,function*(){f("log","at wfc/proto/proto.min.js:1722","recall",e),g.recall(e.toString(),()=>{f("log","at wfc/proto/proto.min.js:1725","recall, s",e),t&&t(),this.onRecallMessage(this.getUserId(),e)},i=>{f("log","at wfc/proto/proto.min.js:1732","recall, f",e,i),s&&s()})})}deleteRemoteMessage(e,t,s){return v(this,null,function*(){f("log","at wfc/proto/proto.min.js:1740","deleteRemoteMessageByUid",e),g.deleteRemoteMessage(e.toString(),()=>{t&&t(),this.onDeleteRemoteMessage(e)},i=>{s&&s()})})}updateRemoteMessageContent(e,t,s,i,n,a){return v(this,null,function*(){f("log","at wfc/proto/proto.min.js:1756","updateRemoteMessageContent",e);let c=t.encode();g.updateRemoteMessageContent(e.toString(),JSON.stringify(c),s,i,()=>{n&&n()},d=>{a&&a(d)})})}deleteMessageById(e){let t=g.deleteMessage(e);return this.onMessageDeleted(e),t}watchOnlineState(e,t,s,i,n){g.watchOnlineState(e,t,s,a=>{let c=l._parseUserOnlineState(a);i&&i(c)},a=>{n&&n(a)})}unwatchOnlineState(e,t,s,i){g.unwatchOnlineState(e,t,()=>{s&&s()},n=>{i&&i()})}isCommercialServer(){return g.isCommercialServer()}isReceiptEnabled(){return g.isReceiptEnabled()}isGlobalDisableSyncDraft(){return g.isGlobalDisableSyncDraft()}isEnableUserOnlineState(){return g.isEnableUserOnlineState()}setDisableSyncDraft(e,t,s){this.setUserSetting(I.DisableSyncDraft,"",e?"1":"0",()=>t&&t(),i=>s&&s(i))}isDisableSyncDraft(){return this.getUserSetting(I.DisableSyncDraft,"")==="1"}getAuthorizedMediaUrl(e,t,s,i,n){g.getAuthorizedMediaUrl(e,t,s,a=>{let[c,d]=a;i&&i(c,d)},n)}isSupportBigFilesUpload(){return g.isSupportBigFilesUpload()}getUploadMediaUrl(e,t,s,i,n){g.getUploadMediaUrl(e,t,s,a=>{let[c,d,p,A]=a;i&&i(c,d,p,A)},n)}getConversationFileRecords(e,t,s,i,n,a){g.getConversationFiles(JSON.stringify(e),t,E.fromValue(s).toString(),i,c=>{let d=JSON.parse(c),p=[];d.forEach(A=>{p.push(this._objStrToFileRecordObj(A))}),n&&n(p)},c=>{a&&a(c)})}_objStrToFileRecordObj(e){let t=new os;return t.userId=e.userId,e.conversation?t.conversation=Object.assign(new z,e.conversation):t.conversation=new z(e.conversationType,e.target,e.line),t.messageUid=E.fromValue(e.messageUid),t.name=e.name,t.url=e.url,t.size=e.size,t.downloadCount=e.downloadCount,t.timestamp=e.timestamp,t}getMyFileRecords(e,t,s,i){g.getMyFiles(E.fromValue(e).toString(),t,n=>{let a=JSON.parse(n),c=[];a.forEach(d=>{c.push(this._objStrToFileRecordObj(d))}),s&&s(c)},n=>{i&&i(n)})}deleteFileRecord(e,t,s){g.deleteFileRecord(E.fromValue(e).toString(),()=>{t&&t()},i=>{s&&s(i)})}clearMessages(e){return v(this,null,function*(){g.clearMessages(JSON.stringify(e));let t=this.getConversationInfo(e);l.eventEmitter.emit(M.ConversationInfoUpdate,t)})}clearRemoteConversationMessages(e,t,s){return v(this,null,function*(){g.clearRemoteConversationMessages(JSON.stringify(e),()=>{t&&t()},i=>{s&&s(i)})})}clearMessagesByTime(e,t){return v(this,null,function*(){g.clearMessagesByTime(JSON.stringify(e),t);let s=this.getConversationInfo(e);l.eventEmitter.emit(M.ConversationInfoUpdate,s)})}insertMessage(e,t,s,i=!1,n=[],a=0){let c=t.encode();g.insertMessage(JSON.stringify(e),l.userId,JSON.stringify(c),s,i,n,a)}updateMessageContent(e,t){return v(this,null,function*(){let s=t.encode();g.updateMessage(e,JSON.stringify(s))})}updateMessageStatus(e,t){return v(this,null,function*(){g.updateMessageStatus(e,t)})}uploadMedia(e,t,s,i,n,a){return v(this,null,function*(){t.indexOf("base64,")>=0&&(t=t.substring(t.indexOf(",")+1)),g.uploadMedia(e,t,s,c=>{i&&i(c)},c=>{n&&n(c)},(c,d)=>{a&&a(c,d)})})}sendConferenceRequest(e,t,s,i,n,a){g.sendConferenceRequest(""+e,t,s,i,c=>{a&&a(0,c)},c=>{a&&a(c,null)},n===!0)}searchFiles(e,t,s,i,n,a,c){g.searchFiles(e,t?JSON.stringify(t):"",s,E.fromValue(i).toString(),n,d=>{let p=JSON.parse(d),A=[];p.forEach(b=>{A.push(this._objStrToFileRecordObj(b))}),a&&a(A)},d=>{c&&c(d)})}searchMyFiles(e,t,s,i,n){g.searchMyFiles(e,E.fromValue(t).toString(),s,a=>{let c=JSON.parse(a),d=[];c.forEach(p=>{d.push(this._objStrToFileRecordObj(p))}),i&&i(d)},a=>{n&&n(a)})}getAuthCode(e,t,s,i,n){g.getAuthCode(e,t,s,a=>{i&&i(a)},a=>{n&&n(a)})}configApplication(e,t,s,i,n,a,c){g.configApplication(e,t,s,i,n,()=>{a&&a()},d=>{c&&c(d)})}notify(e,t){g.notify(e,t)}clearAllNotification(){g.clearAllNotification()}setDeviceToken(e,t){g.setDeviceToken(e,t)}chooseFile(e,t,s){g.chooseFile(e,i=>{t&&t(i)},i=>{s&&s(i)})}_handleNativeEvent(e){let t=e.args,s=this._nativeEvent2WfcEvent[t[0]];s&&s(...t.slice(1))}_callNativeFunc(...e){}_preloadDefaultData(){let e=l.getIncommingFriendRequest(),t=new Set;e.forEach(p=>{t.add(p.target)}),e=l.getOutgoingFriendRequest(),e.forEach(p=>{t.add(p.target)}),l.getMyFriendList(!1).forEach(p=>t.add(p));let i=l.getConversationList([0,1,3],[0,1,2]),n=[],a=[];i.forEach(p=>{p.conversation.type===k.Single?t.add(p.conversation.target):p.conversation.type===k.Group?n.push(p.conversation.target):p.conversation.type===k.Channel&&a.push(p.conversation.target),p.lastMessage&&p.lastMessage.from&&t.add(p.lastMessage.from)});let c=Array.from(t);f("log","at wfc/proto/proto.min.js:2157","to preload userIds",c,t);for(let p=0;p<c.length/2e3;p++)l.getUserInfos(c.slice(2e3*p,(p+1)*2e3),""),f("log","at wfc/proto/proto.min.js:2160","to preload",c.slice(2e3*p,(p+1)*2e3));f("log","at wfc/proto/proto.min.js:2163","to preload groupIds",n),l.getGroupInfos(n,!1),n.forEach(p=>{l.getGroupMembers(p,!1)}),a.forEach(p=>{l.getChannelInfo(p)});let d=0;return d+=Math.round(c.length/2e3)*4,d+=Math.round(n.length/10)*2,d*1e3}_preloadGroupMemberUserInfos(e){for(let t=0;t<e.length/2e3;t++)l.getUserInfos(e.slice(2e3*t,(t+1)*2e3),""),f("log","at wfc/proto/proto.min.js:2184","to preload",e.slice(2e3*t,(t+1)*2e3))}},l=new ds,gs=class{constructor(){o(this,"eventEmitter",new Di.EventEmitter),l.eventEmitter={emit:(e,...t)=>{e===M.ConnectionStatusChanged||e===M.UserOnlineEvent?hs.eventEmitter.emit(e,...t):l.connectionStatus===L.ConnectionStatusConnected&&hs.eventEmitter.emit(e,...t)}}}init(e=[]){f("log","at wfc/client/wfc.js:47","wfc init"),l.init(e),f("log","at wfc/client/wfc.js:51","wfc init end")}registerMessageContent(e,t,s,i){l.registerMessageContent(e,t,s,i)}getClientId(){return l.getClientId()}useSM4(){l.useSM4()}connect(e,t){return l.connect(e,t)}setProxyInfo(e,t,s,i,n){l.setProxyInfo(e,t,s,i,n)}setDeviceToken(e,t){l.setDeviceToken(e,t)}disconnect(e=!1,t=!1){l.disconnect(e,t)}setPackageName(e){l.setPackageName(e)}getUserId(){return l.getUserId()}getServerDeltaTime(){return l.getServerDeltaTime()}screenShot(){return l.screenShot()}isLogin(){return l.isLogin()}getConnectionStatus(){return l.getConnectionStatus()}setBackupAddressStrategy(e){l.setBackupAddressStrategy(e)}setBackupAddress(e,t){l.setBackupAddress(e,t)}setProtoUserAgent(e){l.setProtoUserAgent(e)}addHttpHeader(e,t){l.addHttpHeader(e,t)}onAppResume(){l.onAppResume()}onAppSuspend(){l.onAppSuspend()}getMyGroupList(){return l.getMyGroupList()}getFavGroupList(){let e=l.getMyGroupList();return e.map(t=>((!t.portrait||t.portrait.startsWith(D.APP_SERVER))&&(t.portrait=this.defaultGroupPortrait(t)),t)),e}getUserDisplayName(e){let t=this.getUserInfo(e,!1);return t?t.friendAlias?t.friendAlias:t.displayName?t.displayName:"<"+e+">":"<"+e+">"}getGroupMemberDisplayName(e,t){let s=this.getUserInfo(t,!1,e);return s?s.groupAlias?s.groupAlias:s.friendAlias?s.friendAlias:s.displayName?s.displayName:"<"+t+">":"<"+t+">"}getUserDisplayNameEx(e){return e.friendAlias?e.friendAlias:e.displayName?e.displayName:"<"+e.uid+">"}getGroupMemberDisplayNameEx(e){return e.groupAlias?e.groupAlias:e.friendAlias?e.friendAlias:e.displayName?e.displayName:"<"+e.uid+">"}getUserInfo(e,t=!1,s=""){let i=l.getUserInfo(e,t,s);return(!i.portrait||i.portrait.startsWith(D.APP_SERVER))&&(i.portrait=this.defaultUserPortrait(i)),i}getUserInfoEx(e,t,s,i){l.getUserInfoEx(e,t,s,i)}getUserInfosEx(e,t,s){l.getUserInfosEx(e,i=>{i.forEach(n=>{(!n.portrait||n.portrait.startsWith(D.APP_SERVER))&&(n.portrait=this.defaultUserPortrait(n))}),t&&t(i)},i=>{s&&s(i)})}getUserInfos(e,t){let s=l.getUserInfos(e,t);return s.forEach(i=>{(!i.portrait||i.portrait.startsWith(D.APP_SERVER))&&(i.portrait=this.defaultUserPortrait(i))}),s}searchUser(e,t,s,i,n){return v(this,null,function*(){l.searchUser(e,t,s,(a,c)=>{c.forEach(d=>{(!d.portrait||d.portrait.startsWith(D.APP_SERVER))&&(d.portrait=this.defaultUserPortrait(d))}),i&&i(a,c)},n)})}searchFriends(e){let t=l.searchFriends(e);return t.forEach(s=>{(!s.portrait||s.portrait.startsWith(D.APP_SERVER))&&(s.portrait=this.defaultUserPortrait(s))}),t}searchGroups(e){let t=l.searchGroups(e);return t.forEach(s=>{let i=s.groupInfo;(!i.portrait||i.portrait.startsWith(D.APP_SERVER))&&(i.portrait=this.defaultGroupPortrait(i))}),t}getIncommingFriendRequest(){return l.getIncommingFriendRequest()}getOutgoingFriendRequest(){return l.getOutgoingFriendRequest()}getOneFriendRequest(e,t=!0){return l.getOneFriendRequest(e,t)}loadFriendRequestFromRemote(){l.loadFriendRequestFromRemote(E.ZERO)}getUnreadFriendRequestCount(){return l.getUnreadFriendRequestCount()}clearUnreadFriendRequestStatus(){l.clearUnreadFriendRequestStatus()}deleteFriend(e,t,s){return v(this,null,function*(){l.deleteFriend(e,t,s)})}handleFriendRequest(e,t,s,i,n){return v(this,null,function*(){l.handleFriendRequest(e,t,s,i,n)})}isBlackListed(e){return l.isBlackListed(e)}getBlackList(){return l.getBlackList()}setBlackList(e,t,s,i){l.setBlackList(e,t,s,i)}getMyFriendList(e=!1){return l.getMyFriendList(e)}getFriendList(e=!1){return l.getFriendList(e)}getFriendAlias(e){return l.getFriendAlias(e)}getFriendExtra(e){return l.getFriendExtra(e)}setFriendAlias(e,t,s,i){return v(this,null,function*(){l.setFriendAlias(e,t,s,i)})}createGroup(N,V,oe,As,vs){return v(this,arguments,function*(e,t,s,i,n,a=[],c="",d=[0],p,A,b){l.createGroup(e,t,s,i==null?"":i,n,a,c,d,p,A,b)})}setGroupManager(e,t,s,i,n,a,c){return v(this,null,function*(){l.setGroupManager(e,t,s,i,n,a,c)})}getGroupInfo(e,t=!1){let s=l.getGroupInfo(e,t);return(!s.portrait||s.portrait.startsWith(D.APP_SERVER))&&(s.portrait=this.defaultGroupPortrait(s)),s}getGroupInfos(e,t=!1){let s=l.getGroupInfos(e,t);return s.forEach(i=>{(!i.portrait||i.portrait.startsWith(D.APP_SERVER))&&(i.portrait=this.defaultGroupPortrait(i))}),s}getGroupInfoEx(e,t=!1,s,i){l.getGroupInfoEx(e,t,n=>{(!n.portrait||n.portrait.startsWith(D.APP_SERVER))&&(n.portrait=this.defaultGroupPortrait(n)),s&&s(n)},i)}addGroupMembers(e,t,s,i,n,a,c){l.addGroupMembers(e,t,s,i,n,a,c)}getGroupMemberIds(e,t=!1){return l.getGroupMemberIds(e,t)}getGroupMembers(e,t=!1){return l.getGroupMembers(e,t)}getGroupMembersByType(e,t){return l.getGroupMembersByType(e,t)}getGroupMembersEx(e,t=!1,s,i){l.getGroupMembersEx(e,t,s,i)}getGroupMember(e,t){return l.getGroupMember(e,t)}kickoffGroupMembers(e,t,s,i,n,a){l.kickoffGroupMembers(e,t,s,i,n,a)}muteGroupMembers(e,t,s=[],i=[],n,a,c){l.muteOrAllowGroupMembers(e,t,!1,s,i,n,a,c)}allowGroupMembers(e,t,s=[],i=[],n,a,c){l.muteOrAllowGroupMembers(e,t,!0,s,i,n,a,c)}quitGroup(e,t,s,i,n){return v(this,null,function*(){l.quitGroup(e,t,s,i,n)})}quitGroupEx(e,t,s,i,n,a){return v(this,null,function*(){l.quitGroupEx(e,t,s,i,n,a)})}dismissGroup(e,t,s,i,n){return v(this,null,function*(){l.dismissGroup(e,t,s,i,n)})}modifyGroupInfo(e,t,s,i,n,a,c){return v(this,null,function*(){l.modifyGroupInfo(e,t,s,i,n,a,c)})}modifyGroupAlias(e,t,s,i,n,a){return v(this,null,function*(){l.modifyGroupAlias(e,t,s,i,n,a)})}modifyGroupMemberAlias(e,t,s,i,n,a,c){return v(this,null,function*(){l.modifyGroupMemberAlias(e,t,s,i,n,a,c)})}modifyGroupMemberExtra(e,t,s,i,n,a,c){return v(this,null,function*(){l.modifyGroupMemberExtra(e,t,s,i,n,a,c)})}transferGroup(e,t,s,i,n,a){l.transferGroup(e,t,s,i,n,a)}getFavGroups(){return l.getFavGroups()}isFavGroup(e){return l.isFavGroup(e)}setFavGroup(e,t,s,i){return v(this,null,function*(){l.setFavGroup(e,t,s,i)})}getUserSetting(e,t){return l.getUserSetting(e,t)}getUserSettings(e){return l.getUserSettings(e)}setUserSetting(e,t,s,i,n){return v(this,null,function*(){l.setUserSetting(e,t,s,i,n)})}modifyMyInfo(e,t,s){l.modifyMyInfo(e,t,s)}isGlobalSlient(){return l.isGlobalSlient()}setGlobalSlient(e,t,s){l.setGlobalSlient(e,t,s)}isHiddenNotificationDetail(){return l.isHiddenNotificationDetail()}setHiddenNotificationDetail(e,t,s){return v(this,null,function*(){l.setHiddenNotificationDetail(e,t,s)})}isHiddenGroupMemberName(e){return l.isHiddenGroupMemberName(e)}setHiddenGroupMemberName(e,t,s,i){return v(this,null,function*(){l.setHiddenGroupMemberName(e,t,s,i)})}joinChatroom(e,t,s){return v(this,null,function*(){l.joinChatroom(e,t,s)})}quitChatroom(e,t,s){return v(this,null,function*(){l.quitChatroom(e,t,s)})}getChatroomInfo(e,t,s,i){return v(this,null,function*(){return l.getChatroomInfo(e,t,s,i)})}getChatroomMemberInfo(e,t,s,i){return v(this,null,function*(){l.getChatroomMemberInfo(e,t,s,i)})}createChannel(e,t,s,i,n,a){l.createChannel(e,t,s,i,n,a)}getChannelInfo(e,t){return l.getChannelInfo(e,t)}modifyChannelInfo(e,t,s,i,n){return v(this,null,function*(){l.modifyChannelInfo(e,t,s,i,n)})}searchChannel(e,t,s,i){l.searchChannel(e,s,i)}isListenedChannel(e){return l.isListenedChannel(e)}listenChannel(e,t,s,i){return v(this,null,function*(){l.listenChannel(e,t,s,i)})}getMyChannels(){return l.getMyChannels()}getListenedChannels(){return l.getListenedChannels()}destoryChannel(e,t,s){return v(this,null,function*(){l.destoryChannel(e,t,s)})}getConversationList(e,t){return l.getConversationList(e,t)}getConversationInfo(e){return l.getConversationInfo(e)}searchConversation(e,t=[0,1,2],s=[0,1,2]){return l.searchConversation(e,t,s)}removeConversation(e,t){return v(this,null,function*(){l.removeConversation(e,t)})}setConversationTop(e,t,s,i){l.setConversationTop(e,t,s,i)}setConversationSlient(e,t,s,i){l.setConversationSlient(e,t,s,i)}setConversationDraft(e,t=""){l.setConversationDraft(e,t)}setConversationTimestamp(e,t){l.setConversationTimestamp(e,t)}getUnreadCount(e=[0,1,2],t=[0]){return l.getUnreadCount(e,t)}getConversationUnreadCount(e){return l.getConversationUnreadCount(e)}clearConversationUnreadStatus(e){l.clearConversationUnreadStatus(e)}markConversationAsUnread(e,t){return l.markConversationAsUnread(e,t)}clearMessageUnreadStatus(e){l.clearMessageUnreadStatus(e)}clearAllUnreadStatus(){l.clearAllUnreadStatus()}setMediaMessagePlayed(e){l.setMediaMessagePlayed(e)}setMessageLocalExtra(e,t){l.setMessageLocalExtra(e,t)}isMyFriend(e){return l.isMyFriend(e)}getFavUsers(){return l.getFavUsers()}isFavUser(e){return l.isFavUser(e)}setFavUser(e,t,s,i){return v(this,null,function*(){l.setFavUser(e,t,s,i)})}sendFriendRequest(e,t,s,i,n){return v(this,null,function*(){l.sendFriendRequest(e,t,s,i,n)})}getMessages(e,t=0,s=!0,i=20,n=""){return l.getMessages(e,t,s,i,n)}getMessagesEx(e,t,s=0,i=!0,n=20,a="",c=[]){return l.getMessagesEx(e,t,c,s,i,n,a)}getMessagesEx2(e,t,s,i=0,n=!0,a=20,c=""){return l.getMessagesEx2(e,t,s,i,n,a,c)}getMessagesByTimestamp(e,t,s,i=!0,n=20,a=""){return l.getMessagesByTimestamp(e,t,s,i,n,a)}getUserMessages(e,t,s,i=!0,n=20){return l.getUserMessages(e,t,s,i,n)}getUserMessagesEx(e,t,s,i,n=!0,a=20,c=[]){return l.getUserMessagesEx(e,t,s,i,n,a,c)}getMessagesV2(e,t,s,i,n,a,c){l.getMessagesV2(e,t,s,i,n,a,c)}getMessagesExV2(e,t,s,i,n,a,c,d,p){l.getMessagesExV2(e,t,c,s,i,n,a,d,p)}getMessagesEx2V2(e,t,s,i,n,a,c,d,p){l.getMessagesEx2V2(e,t,s,i,n,a,c,d,p)}getMessagesByTimestampV2(e,t,s,i,n,a,c,d){l.getMessagesByTimestampV2(e,t,s,i,n,a,c,d)}getUserMessagesV2(e,t,s,i,n,a,c){l.getUserMessagesV2(e,t,s,i,n,a,c)}getUserMessagesExV2(e,t,s,i,n,a,c,d,p){l.getUserMessagesExV2(e,t,s,i,n,a,c,d,p)}getFirstUnreadMessageId(e){return l.getFirstUnreadMessageId(e)}loadRemoteMessages(e,t,s,i,n,a){l.loadRemoteMessages(e,t,s,i,n,a)}loadRemoteConversationMessages(e,t,s,i,n,a){l.loadRemoteMessages(e,t,s,i,n,a)}loadRemoteConversationMessagesEx(e,t,s,i,n,a,c){l.loadRemoteMessages(e,t,s,i,a,c,n)}loadRemoteLineMessages(e,t,s,i,n,a){l.loadRemoteLineMessages(e,t,s,i,n,a)}loadRemoteMessage(e,t,s){l.loadRemoteMessage(e,t,s)}getMessageById(e){return l.getMessageById(e)}getMessageByUid(e){return l.getMessageByUid(e)}searchMessage(e,t,s=""){return l.searchMessage(e,t,s)}searchMessageEx(e,t,s,i,n,a=""){return l.searchMessageEx(e,t,s,i,n,a)}searchMessageByTypes(e,t,s,i,n,a,c=""){return l.searchMessageByTypes(e,t,s,i,n,a,c)}searchMessageByTypesAndTimes(e,t,s,i,n,a,c,d,p=""){return l.searchMessageByTypesAndTimes(e,t,s,i,n,a,c,d,p)}searchMessageEx2(e,t,s,i,n,a,c,d=""){return l.searchMessageEx2(e,t,s,i,n,a,c,d)}sendConversationMessage(d,p){return v(this,arguments,function*(e,t,s=[],i=null,n=null,a=null,c=null){l.sendConversationMessage(e,t,s,i,n,a,c)})}sendMessage(e,t,s,i,n){return v(this,null,function*(){l.sendMessage(e,t,s,i,n)})}sendMessageEx(c){return v(this,arguments,function*(e,t=[],s,i,n,a){l.sendMessageEx(e,t,s,i,n,a)})}recallMessage(e,t,s){return v(this,null,function*(){l.recallMessage(e,t,s)})}deleteMessage(e){return l.deleteMessageById(e)}deleteRemoteMessageByUid(e,t,s){l.deleteRemoteMessage(e,t,s)}updateRemoteMessageContent(e,t,s,i,n,a){l.updateRemoteMessageContent(e,t,s,i,n,a)}clearMessages(e){return v(this,null,function*(){l.clearMessages(e)})}clearRemoteConversationMessages(e,t,s){return v(this,null,function*(){l.clearRemoteConversationMessages(e,t,s)})}insertMessage(e,t,s,i=!1,n=[],a=0){l.insertMessage(e,t,s,i,n,a)}updateMessageContent(e,t){return v(this,null,function*(){l.updateMessageContent(e,t)})}updateMessageStatus(e,t){return v(this,null,function*(){l.updateMessageStatus(e,t)})}uploadMedia(e,t,s,i,n,a){return v(this,null,function*(){l.uploadMedia(e,t,s,i,n,a)})}getVersion(){return l.getVersion()}getAuthorizedMediaUrl(e,t,s,i,n){l.getAuthorizedMediaUrl(e,t,s,i,n)}isSupportBigFilesUpload(){return l.isSupportBigFilesUpload()}getUploadMediaUrl(e,t,s,i,n){l.getUploadMediaUrl(e,t,s,i,n)}onForeground(){l.onForeground()}isReceiptEnabled(){return l.isReceiptEnabled()}isUserReceiptEnabled(){return l.isUserReceiptEnabled()}isCommercialServer(){return l.isCommercialServer()}isGlobalDisableSyncDraft(){return l.isGlobalDisableSyncDraft()}setDisableSyncDraft(e,t,s){l.setDisableSyncDraft(e,t,s)}isDisableSyncDraft(){return l.isDisableSyncDraft()}setUserEnableReceipt(e,t,s){l.setUserEnableReceipt(e,t,s)}getConversationRead(e){return l.getConversationRead(e)}getConversationFileRecords(e,t,s,i,n,a){l.getConversationFileRecords(e,t,s,i,n,a)}getMyFileRecords(e,t,s,i){l.getMyFileRecords(e,t,s,i)}deleteFileRecord(e,t,s){l.deleteFileRecord(e,t,s)}searchFiles(e,t,s,i,n,a,c){l.searchFiles(e,t,s,i,n,a,c)}searchMyFiles(e,t,s,i,n){l.searchMyFiles(e,t,s,i,n)}getHost(){return l.getHost()}getEncodedClientId(){return l.getEncodedClientId()}encodeData(e){return l.encodeData(e)}decodeData(e){return l.decodeData(e)}sendConferenceRequest(e,t,s,i,n){this.sendConferenceRequestEx(e,t,s,i,!1,n)}sendConferenceRequestEx(e,t,s,i,n,a){l.sendConferenceRequest(e,t,s,i,n,a)}isUserOnlineStateEnabled(){return l.isUserOnlineStateEnabled()}watchOnlineState(e,t,s,i,n){l.watchOnlineState(e,t,s,i,n)}unwatchOnlineState(e,t,s,i){l.unwatchOnlineState(e,t,s,i)}setMyCustomState(e,t,s,i){l.setMyCustomState(e,t,s,i)}getAuthCode(e,t,s,i,n){l.getAuthCode(e,t,s,i,n)}configApplication(e,t,s,i,n,a,c){l.configApplication(e,t,s,i,n,a,c)}notify(e,t){l.notify(e,t)}clearAllNotification(){l.clearAllNotification()}chooseFile(e,t,s){l.chooseFile(e,t,s)}_getStore(){return l._getStore()}attach(e){l.attach(e)}utf8_to_b64(e){return ki(unescape(encodeURIComponent(e)))}b64_to_utf8(e){return decodeURIComponent(escape(Gi(e)))}unescape(e){return(e+"===".slice((e.length+3)%4)).replace(/-/g,"+").replace(/_/g,"/")}escape(e){return e.replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}defaultUserPortrait(e){return`${D.APP_SERVER}/avatar?name=${encodeURIComponent(e.displayName)}`}defaultGroupPortrait(e){let t=this.getGroupMemberIds(e.target);t=t.slice(0,9);let s=l.getUserInfos(t,e.target),i={members:[]},n=!1;return s.forEach(a=>{a.portrait&&!a.portrait.startsWith(`${D.APP_SERVER}`)?i.members.push({avatarUrl:a.portrait}):i.members.push({name:a.displayName}),a instanceof Me&&(n=!0)}),s.length===0||n?null:(i=JSON.stringify(i,null,""),`${D.APP_SERVER}/avatar/group?request=${encodeURIComponent(i)}`)}},hs=new gs,h=hs,Ye=class extends O{constructor(e,t,s,i){super(u.VOIP_Multi_Call_Ongoing),o(this,"callId"),o(this,"initiator"),o(this,"audioOnly"),o(this,"targets"),this.callId=e,this.initiator=t,this.audioOnly=s,this.targets=i}digest(e){return h.getGroupMemberDisplayName(e.conversation.target,this.initiator)+" \u53D1\u8D77\u7684\u901A\u8BDD"}encode(){let e=super.encode();e.content=this.callId;let t={initiator:this.initiator,audioOnly:this.audioOnly?1:0,targets:this.targets};return e.binaryContent=h.utf8_to_b64(JSON.stringify(t)),e}decode(e){super.decode(e),this.callId=e.content;let t=h.b64_to_utf8(e.binaryContent),s=JSON.parse(t);this.initiator=s.initiator,this.audioOnly=s.audioOnly===1,this.targets=s.targets}},Hi="/static/image/av/add.png",qi={container:{"":{width:"750rpx",flex:1,display:"flex",paddingTop:"132rpx",flexDirection:"column",justifyContent:"space-between",backgroundColor:"rgb(41,41,41)"}},"content-container":{"":{width:"750rpx",flex:1,position:"relative",display:"flex",flexDirection:"row",flexWrap:"wrap",justifyContent:"center",alignItems:"flex-start"}},"participant-container":{"":{display:"flex",width:"250rpx",height:"250rpx",position:"relative",flexDirection:"column",justifyContent:"center",alignItems:"center"}},"participant-item":{".participant-container ":{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",width:"250rpx",height:"250rpx",paddingTop:"2rpx",paddingRight:"2rpx",paddingBottom:"2rpx",paddingLeft:"2rpx"}},avatar:{"":{width:"250rpx",height:"250rpx","//borderRadius":"1px"}},desc:{".participant-item ":{position:"absolute",left:0,backgroundColor:"#D3D3D3",bottom:0,height:"40rpx",paddingTop:5,paddingRight:5,paddingBottom:5,paddingLeft:5},"":{color:"#FFFFFF",fontSize:15,paddingTop:5,paddingRight:0,paddingBottom:5,paddingLeft:0}},"duration-action-container":{"":{width:"750rpx",height:"400rpx",position:"absolute",left:0,bottom:0,display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",paddingBottom:20}},actions:{".duration-action-container ":{width:"750rpx",display:"flex",flexDirection:"row",flexWrap:"wrap",justifyContent:"space-around"}},"action-container":{"":{width:"750rpx",height:"200rpx",position:"absolute",bottom:0,left:0,display:"flex",flexDirection:"row",justifyContent:"space-around",paddingBottom:20}},action:{"":{width:"250rpx",display:"flex",flexDirection:"column",alignItems:"center",fontSize:12,color:"#FFFFFF",paddingTop:10,paddingRight:0,paddingBottom:10,paddingLeft:0}},"action-img":{"":{width:60,height:60}},video:{"":{width:"250rpx",height:"250rpx","//padding":"1px"},".me":{transform:"scaleX(-1)"}},"voip-title-container":{"":{position:"absolute",left:0,top:0,width:"750rpx",display:"flex",marginTop:"44rpx",paddingTop:0,paddingRight:"40rpx",paddingBottom:0,paddingLeft:"40rpx",flexDirection:"row",alignItems:"center",justifyContent:"space-between",height:"88rpx"}},title:{".voip-title-container ":{position:"absolute",left:0,top:0,width:"750rpx",height:"88rpx",display:"flex",justifyContent:"center",alignItems:"center"}},"webrtc-tip":{"":{position:"absolute",color:"#FF0000",left:0,top:0,zIndex:999}}},Qi={name:"Multi",data(){return{wfc:getApp().wfc,session:null,audioOnly:!1,callState:1,initiatorUserInfo:null,participantUserInfos:[],groupMemberUserInfos:[],startTimestamp:0,currentTimestamp:0,videoInputDeviceIndex:0,broadcastMultiCallOngoingTimer:0,autoPlayInterval:0,showWebrtcTip:!1,ringAudio:null,setupVideoViewsTimer:0}},onLoad(r){f("log","at pages/voip/Multi.nvue:147","voip/Multi onLoad",r),this.session=JSON.parse(r.session),this.callState=this.session.state,this.audioOnly=this.session.audioOnly,this.session.connectedTime&&(this.startTimestamp=this.session.connectedTime)},methods:{switchVideoType(r,e){let t=le.NONE;t=le.SMALL_STREAM,F.setParticipantVideoType(this.session.callId,r,e,t)},setupSessionCallback(){let r=new Ie;r.didChangeState=e=>{f("log","at pages/voip/Multi.nvue:173","didChangeState",typeof e,e,this.callState),this.callState=e,e===H.STATUS_CONNECTED?(this.onVoipConnected(),D.ENABLE_MULTI_CALL_AUTO_JOIN&&this.selfUserInfo.uid===this.session.initiator&&(this.broadcastMultiCallOngoingTimer=setInterval(this.broadcastMultiCallOngoing,1e3))):e===H.STATUS_IDLE&&this.timer&&clearInterval(this.timer)},r.didChangeMode=e=>{this.audioOnly=e},r.didCreateLocalVideoTrack=e=>{},r.didParticipantConnected=(e,t)=>{},r.didReceiveRemoteVideoTrack=(e,t)=>{f("log","at pages/voip/Multi.nvue:200","didReceiveRemoteVideoTrack",e,t),this.participantUserInfos.forEach(s=>{s.uid===e&&(s._state=H.STATUS_CONNECTED)}),this.callState===H.STATUS_CONNECTED&&setTimeout(()=>{this.$nextTick(()=>{f("log","at pages/voip/Multi.nvue:211","setRemoteVideoView",e,this.$refs["remoteVideo-"+e][0].ref),F.setRemoteVideoView(this.session.callId,e,this.$refs["remoteVideo-"+e][0].ref,!1)})},1e3)},r.didParticipantJoined=(e,t)=>{let s=F.getParticipantProfile(this.session.callId,e,t),i=this.wfc.getUserInfo(e);i._isVideoMuted=s.videoMuted,i._isAudioMuted=s.audioMuted,i._isAudience=s.audience,this.participantUserInfos.push(i),f("log","at pages/voip/Multi.nvue:226","didParticipantJoined",i,this.callState)},r.didParticipantLeft=e=>{f("log","at pages/voip/Multi.nvue:230","didParticipantLeft",e,this.participantUserInfos.length),this.participantUserInfos=this.participantUserInfos.filter(t=>t.uid!==e)},r.didCallEndWithReason=e=>{f("log","at pages/voip/Multi.nvue:235","callEndWithReason",e),this.session=null},r.didVideoMuted=(e,t)=>{f("log","at pages/voip/Multi.nvue:240","didVideoMuted",e,t),e===this.wfc.getUserId()?this.selfUserInfo._isVideoMuted=t:this.participantUserInfos.forEach(s=>{s.uid===e&&(s._isVideoMuted=t)}),this.setupVideoViews()},r.didMediaLostPacket=(e,t)=>{t>6&&f("log","at pages/voip/Multi.nvue:255","\u60A8\u7684\u7F51\u7EDC\u4E0D\u597D")},r.didUserMediaLostPacket=(e,t,s,i)=>{if(s>10)if(i){let n=this.participantUserInfos.filter(a=>a.uid===e);n&&n.length>0&&f("log","at pages/voip/Multi.nvue:266",n[0].displayName,"\u7F51\u7EDC\u4E0D\u597D")}else f("log","at pages/voip/Multi.nvue:269","\u60A8\u7684\u7F51\u7EDC\u4E0D\u597D")},r.didChangeInitiator=e=>{this.initiatorUserInfo=this.wfc.getUserInfo(e),this.selfUserInfo.uid===e&&(this.broadcastMultiCallOngoingTimer||(this.broadcastMultiCallOngoingTimer=setInterval(this.broadcastMultiCallOngoing,200)))},getApp().avengineKit.sessionCallback=r},answer(){return v(this,null,function*(){if(!(yield _s(this.session.audioOnly))){f("log","at pages/voip/Multi.nvue:287","no permission, hangup"),this.hangup();return}F.answerCall(this.session.callId,!1)})},hangup(){F.endCall(this.session.callId),this.session=null},switchCamera(){this.session&&F.switchCamera(this.session.callId)},mute(){let r=!this.session.audioMuted;this.session.audioMuted=r,this.selfUserInfo._isAudioMuted=r,F.muteAudio(this.session.callId,r)},muteVideo(){let r=!this.session.videoMuted;this.session.videoMuted=r,this.selfUserInfo._isVideoMuted=r,this.session._selfVideoViewSet=!1,F.muteVideo(this.session.callId,r)},down2voice(){this.session.downgrade2Voice(this.session.callId)},screenShare(){},invite(){let r=t=>{let s=t.map(i=>i.uid);f("log","at pages/voip/Multi.nvue:331","picked users",s),F.inviteNewParticipant(this.session.callId,s)},e=getApp().store.getGroupMemberUserInfos(this.session.conversation.target,!1);getApp().$pickUsers({users:e,initialCheckedUsers:[...this.participantUserInfos,this.selfUserInfo],uncheckableUsers:[...this.participantUserInfos,this.selfUserInfo],confirmTitle:"\u786E\u5B9A",successCB:r})},userName(r){let e;return r.groupAlias?e=r.groupAlias:r.friendAlias?e=r.friendAlias:r.displayName?e=r.displayName:e=r.name,e},setupVideoViews(){f("log","at pages/voip/Multi.nvue:360","setupVideoViews",this.audioOnly),this.audioOnly||this.$nextTick(()=>{let r=!0;this.$refs.localVideo&&!this.session._selfVideoViewSet?(F.setLocalVideoView(this.session.callId,this.$refs.localVideo.ref),this.session._selfVideoViewSet=!0):r=!1,this.participantUserInfos.forEach(e=>{!e._isVideoMuted&&this.$refs["remoteVideo-"+e.uid]&&this.$refs["remoteVideo-"+e.uid][0]&&!e._videoViewSet?(F.setRemoteVideoView(this.session.callId,e.uid,this.$refs["remoteVideo-"+e.uid][0].ref,!1),e._videoViewSet=!0):(f("log","at pages/voip/Multi.nvue:377",`${e.uid} isVideoMuted or not connected`),r=!1)}),r?this.setupVideoViewsTimer&&(clearTimeout(this.setupVideoViewsTimer),this.setupVideoViewsTimer=0):(f("log","at pages/voip/Multi.nvue:388","allVideoViews are not ready, setTimeout"),clearTimeout(this.setupVideoViewsTimer),this.setupVideoViewsTimer=setTimeout(this.setupVideoViews,100))})},onVoipConnected(){this.timer||(this.startTimestamp||(this.startTimestamp=new Date().getTime()),this.timer=setInterval(()=>{this.currentTimestamp=new Date().getTime()},1e3)),this.setupVideoViews()},timestampFormat(r){r=~~(r/1e3);let e="",t=~~(r/3600);e=t>0?(t<10?"0":"")+t+":":"";let s=~~(r%3600/60);e+=(s<10?"0":"")+s+":";let i=~~(r%60);return e+=(i<10?"0":"")+i,e},broadcastMultiCallOngoing(){if(this.callState===H.STATUS_CONNECTED){let r=this.participantUserInfos.map(t=>t.uid).filter(t=>t!==this.selfUserInfo.uid),e=new Ye(this.session.callId,this.session.initiator,this.session.audioOnly,r);h.sendConversationMessage(this.session.conversation,e)}},minimize(){let r=getApp().avengineKit.checkOverlayPermission();f("log","at pages/voip/Multi.nvue:431","overlayPermission granted",r),r?(getApp().avengineKit.minimize(this.session.callId),uni.navigateBack({delta:1,fail:e=>{f("log","at pages/voip/Multi.nvue:437","nav back to err",e)}})):uni.showToast({title:"\u9700\u8981\u60AC\u6D6E\u7A97\u6743\u9650",icon:"none"})}},mounted(){let r=F.currentCallSession();if(f("log","at pages/voip/Multi.nvue:451","voip/Multi mounted",r),!r||r.state===0){f("log","at pages/voip/Multi.nvue:453","av call already hangup"),uni.navigateBack({delta:1,fail:s=>{f("log","at pages/voip/Multi.nvue:457","nav back to conversationView err",s)}});return}if(this.session=r,!(F.startConference!==void 0)){let s=window.location.host;if(s.indexOf("wildfirechat.cn")===-1&&s.indexOf("localhost")===-1){for(let i of D.ICE_SERVERS)if(i[0].indexOf("turn.wildfirechat.net")>=0){this.showWebrtcTip=!0,setTimeout(()=>{this.showWebrtcTip=!1},10*1e3);break}}}this.setupSessionCallback(),r&&(f("log","at pages/voip/Multi.nvue:483","current session",r.state,r),this.session=r,this.callState=r.state);let t=F.getParticipantProfiles(this.session.callId);for(let s of t){let i=this.wfc.getUserInfo(s.userId);i._isVideoMuted=s.videoMuted,i._isAudioMuted=s.audioMuted,i._isAudience=s.audience,i._state=s.state,this.participantUserInfos.push(i)}this.callState===H.STATUS_CONNECTED&&this.onVoipConnected()},onBackPress(r){return f("log","at pages/voip/Multi.nvue:504","conferencePage, onBackPress",r),r.from!=="navigateBack"},beforeUnmount(){this.broadcastMultiCallOngoingTimer&&clearInterval(this.broadcastMultiCallOngoingTimer)},computed:{duration(){if(this.currentTimestamp<=0)return"00:00";let r=this.currentTimestamp-this.startTimestamp;return this.timestampFormat(r)},selfUserInfo(){return this.wfc.getUserInfo(this.wfc.getUserId(),!1)},computedParticipantSizeStyle(){let r,e;return this.participantUserInfos.length<4?(r="375rpx",e="375rpx"):(r="250rpx",e="250rpx"),{width:r,maxWidth:r,height:e,maxHeight:e}}}};function Yi(r,e,t,s,i,n){return(0,m.openBlock)(),(0,m.createElementBlock)("scroll-view",{scrollY:!0,showScrollbar:!0,enableBackToTop:!0,bubble:"true",style:{flexDirection:"column"}},[(0,m.createElementVNode)("div",{style:{flex:"1",display:"flex","flex-direction":"column"}},[i.session?((0,m.openBlock)(),(0,m.createElementBlock)("div",{key:0,class:"container"},[(0,m.createElementVNode)("div",{class:"content-container"},[(0,m.createElementVNode)("div",{class:"participant-container",style:(0,m.normalizeStyle)(n.computedParticipantSizeStyle)},[i.audioOnly||i.callState!==4||n.selfUserInfo._isVideoMuted?((0,m.openBlock)(),(0,m.createElementBlock)("div",{key:0,class:"participant-item",style:(0,m.normalizeStyle)(n.computedParticipantSizeStyle)},[(0,m.createElementVNode)("u-image",{class:"avatar",style:(0,m.normalizeStyle)(n.computedParticipantSizeStyle),src:n.selfUserInfo.portrait},null,12,["src"]),(0,m.createElementVNode)("u-text",{class:"desc"},"\u6211")],4)):((0,m.openBlock)(),(0,m.createElementBlock)("UIKit-Video-CallView",{key:1,style:(0,m.normalizeStyle)(n.computedParticipantSizeStyle),class:"video me",ref:"localVideo"},null,4))],4),((0,m.openBlock)(!0),(0,m.createElementBlock)(m.Fragment,null,(0,m.renderList)(i.participantUserInfos,a=>((0,m.openBlock)(),(0,m.createElementBlock)("div",{key:a.uid,class:"participant-container",style:(0,m.normalizeStyle)(n.computedParticipantSizeStyle)},[i.audioOnly||i.callState!==4||a._isVideoMuted||a._state!==4?((0,m.openBlock)(),(0,m.createElementBlock)("div",{key:0,style:(0,m.normalizeStyle)(n.computedParticipantSizeStyle),class:"participant-item"},[(0,m.createElementVNode)("u-image",{class:"avatar",style:(0,m.normalizeStyle)(n.computedParticipantSizeStyle),src:a.portrait,alt:a},null,12,["src","alt"]),(0,m.createElementVNode)("u-text",{class:"desc"},(0,m.toDisplayString)(n.userName(a)),1)],4)):((0,m.openBlock)(),(0,m.createElementBlock)("UIKit-Video-CallView",{key:1,class:"video",style:(0,m.normalizeStyle)(n.computedParticipantSizeStyle),ref_for:!0,ref:"remoteVideo-"+a.uid,onClick:c=>n.switchVideoType(a.uid,a._isScreenSharing)},null,12,["onClick"]))],4))),128))]),i.callState===2?((0,m.openBlock)(),(0,m.createElementBlock)("div",{key:0,class:"action-container"},[(0,m.createElementVNode)("div",{class:"action"},[(0,m.createElementVNode)("u-image",{onClick:e[0]||(e[0]=(...a)=>n.answer&&n.answer(...a)),class:"action-img",src:Os}),(0,m.createElementVNode)("u-text",{class:"desc"},"\u63A5\u542C")]),i.audioOnly?(0,m.createCommentVNode)("",!0):((0,m.openBlock)(),(0,m.createElementBlock)("div",{key:0,class:"action"},[(0,m.createElementVNode)("u-image",{onClick:e[1]||(e[1]=(...a)=>n.down2voice&&n.down2voice(...a)),class:"action-img",src:Us}),(0,m.createElementVNode)("u-text",{class:"desc"},"\u5207\u6362\u5230\u8BED\u97F3\u804A\u5929")])),(0,m.createElementVNode)("div",{class:"action"},[(0,m.createElementVNode)("u-image",{onClick:e[2]||(e[2]=(...a)=>n.hangup&&n.hangup(...a)),class:"action-img",src:_e}),(0,m.createElementVNode)("u-text",{class:"desc"},"\u6302\u65AD")])])):(0,m.createCommentVNode)("",!0),i.callState===1||i.callState===3?((0,m.openBlock)(),(0,m.createElementBlock)("div",{key:1,class:"action-container"},[(0,m.createElementVNode)("div",{class:"action"},[(0,m.createElementVNode)("u-image",{onClick:e[3]||(e[3]=(...a)=>n.hangup&&n.hangup(...a)),class:"action-img",src:_e}),(0,m.createElementVNode)("u-text",{class:"desc"},"\u6302\u65AD")])])):(0,m.createCommentVNode)("",!0),i.callState===4?((0,m.openBlock)(),(0,m.createElementBlock)("div",{key:2,class:"duration-action-container"},[(0,m.createElementVNode)("u-text",{class:"desc"},(0,m.toDisplayString)(n.duration),1),(0,m.createElementVNode)("div",{class:"actions"},[(0,m.createElementVNode)("div",{class:"action"},[i.session.audioMuted?((0,m.openBlock)(),(0,m.createElementBlock)("u-image",{key:1,onClick:e[5]||(e[5]=(...a)=>n.mute&&n.mute(...a)),class:"action-img",src:Ts})):((0,m.openBlock)(),(0,m.createElementBlock)("u-image",{key:0,onClick:e[4]||(e[4]=(...a)=>n.mute&&n.mute(...a)),class:"action-img",src:ws})),(0,m.createElementVNode)("u-text",{class:"desc"},"\u9759\u97F3")]),i.audioOnly?(0,m.createCommentVNode)("",!0):((0,m.openBlock)(),(0,m.createElementBlock)("div",{key:0,class:"action"},[(0,m.createElementVNode)("u-image",{onClick:e[6]||(e[6]=(...a)=>n.switchCamera&&n.switchCamera(...a)),class:"action-img",src:"//static/image/av/av_camera.png"}),(0,m.createElementVNode)("u-text",{class:"desc"},"\u5207\u6362\u6444\u50CF\u5934")])),i.session.audioOnly?(0,m.createCommentVNode)("",!0):((0,m.openBlock)(),(0,m.createElementBlock)("div",{key:1,class:"action"},[i.session.videoMuted?((0,m.openBlock)(),(0,m.createElementBlock)("u-image",{key:1,onClick:e[8]||(e[8]=(...a)=>n.muteVideo&&n.muteVideo(...a)),class:"action-img",src:Ns})):((0,m.openBlock)(),(0,m.createElementBlock)("u-image",{key:0,onClick:e[7]||(e[7]=(...a)=>n.muteVideo&&n.muteVideo(...a)),class:"action-img",src:Ms})),(0,m.createElementVNode)("u-text",{class:"desc"},"\u5173\u95ED\u6444\u50CF\u5934")])),(i.audioOnly,(0,m.createCommentVNode)("",!0)),(0,m.createElementVNode)("div",{class:"action"},[(0,m.createElementVNode)("u-image",{onClick:e[10]||(e[10]=(...a)=>n.hangup&&n.hangup(...a)),class:"action-img",src:_e}),(0,m.createElementVNode)("u-text",{class:"desc"},"\u6302\u65AD")])])])):(0,m.createCommentVNode)("",!0),(0,m.createElementVNode)("div",{class:"voip-title-container"},[(0,m.createElementVNode)("div",{class:"title"},[i.callState===4?((0,m.openBlock)(),(0,m.createElementBlock)("u-text",{key:0,class:"desc"},(0,m.toDisplayString)(n.duration),1)):(0,m.createCommentVNode)("",!0)]),(0,m.createElementVNode)("u-image",{onClick:e[11]||(e[11]=(...a)=>n.minimize&&n.minimize(...a)),style:{width:"30px",height:"30px"},src:Is}),i.callState===4?((0,m.openBlock)(),(0,m.createElementBlock)("u-image",{key:0,onClick:e[12]||(e[12]=(...a)=>n.invite&&n.invite(...a)),style:{width:"30px",height:"30px"},src:Hi})):(0,m.createCommentVNode)("",!0)])])):(0,m.createCommentVNode)("",!0)])])}var We=Es(Qi,[["render",Yi],["styles",[qi]]]);var Cs=plus.webview.currentWebview();if(Cs){let r=parseInt(Cs.id),e="pages/voip/Multi",t={};try{t=JSON.parse(Cs.__query__)}catch(i){}We.mpType="page";let s=Vue.createPageApp(We,{$store:getApp({allowDefault:!0}).$store,__pageId:r,__pagePath:e,__pageQuery:t});s.provide("__globalStyles",Vue.useCssStyles([...__uniConfig.styles,...We.styles||[]])),s.mount("#root")}})();
/**
 * @license
 * Copyright 2009 The Closure Library Authors
 * Copyright 2020 Daniel Wirtz / The long.js Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 */
