"use weex:vue";

if (typeof Promise !== 'undefined' && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor
    return this.then(
      value => promise.resolve(callback()).then(() => value),
      reason => promise.resolve(callback()).then(() => {
        throw reason
      })
    )
  }
};

if (typeof uni !== 'undefined' && uni && uni.requireGlobal) {
  const global = uni.requireGlobal()
  ArrayBuffer = global.ArrayBuffer
  Int8Array = global.Int8Array
  Uint8Array = global.Uint8Array
  Uint8ClampedArray = global.Uint8ClampedArray
  Int16Array = global.Int16Array
  Uint16Array = global.Uint16Array
  Int32Array = global.Int32Array
  Uint32Array = global.Uint32Array
  Float32Array = global.Float32Array
  Float64Array = global.Float64Array
  BigInt64Array = global.BigInt64Array
  BigUint64Array = global.BigUint64Array
};


(()=>{var I=Object.create;var b=Object.defineProperty;var j=Object.getOwnPropertyDescriptor;var x=Object.getOwnPropertyNames;var W=Object.getPrototypeOf,A=Object.prototype.hasOwnProperty;var P=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports);var C=(t,e,o,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let s of x(e))!A.call(t,s)&&s!==o&&b(t,s,{get:()=>e[s],enumerable:!(r=j(e,s))||r.enumerable});return t};var _=(t,e,o)=>(o=t!=null?I(W(t)):{},C(e||!t||!t.__esModule?b(o,"default",{value:t,enumerable:!0}):o,t));var c=P((L,v)=>{v.exports=Vue});var M=_(c());function a(t,e,...o){uni.__log__?uni.__log__(t,e,...o):console[t].apply(console,[...o,e])}var k=(t,e)=>{let o=t.__vccOpts||t;for(let[r,s]of e)o[r]=s;return o};var u,f,i,w;function h(t,e){i=t,w=t.wfc,f=e,u={toast:U,openUrl:V,getAuthCode:T,config:N,chooseContacts:O,close:$}}var S=t=>{let e=t;try{e=JSON.parse(t)}catch(o){a("error","at pages/workspace/bridgeServerImpl.js:37","parse ws data error",o);return}a("log","at pages/workspace/bridgeServerImpl.js:41","wf-op-request",e),e.type==="wf-op-request"&&(u[e.handlerName]?u[e.handlerName](e.args,e.appUrl,e.requestId):a("log","at pages/workspace/bridgeServerImpl.js:46","wf-op-request, unknown handlerName",e.handlerName))},V=t=>{a("log","at pages/workspace/bridgeServerImpl.js:52","openUrl",t);let e;e="/pages/workspace/WorkspaceWebViewPage",i.$navigateToPage(`${e}?url=${encodeURIComponent(t)}`)},T=(t,e,o)=>{let r=e.substring(e.indexOf("//")+2);r=r.substring(0,r.indexOf("/")),r.indexOf(":")>0&&(r=r.substring(0,r.indexOf(":"))),w.getAuthCode(t.appId,t.appType,r,s=>{a("log","at pages/workspace/bridgeServerImpl.js:65","authCode",s),d("getAuthCode",e,o,0,s)},s=>{a("log","at pages/workspace/bridgeServerImpl.js:68","getAuthCode error",s),d("getAuthCode",e,o,s)})},N=(t,e)=>{w.configApplication(t.appId,t.appType,t.timestamp,t.nonceStr,t.signature,()=>{a("log","at pages/workspace/bridgeServerImpl.js:76","config success"),y("ready",e)},o=>{a("log","at pages/workspace/bridgeServerImpl.js:80","config failed"),y("error",e,o)})},O=(t,e,o)=>{a("log","at pages/workspace/bridgeServerImpl.js:88","xxxx ooo",i.store.state.contact),i.$pickUsers({users:i.store.state.contact.friendList,confirmTitle:"\u786E\u5B9A",successCB:r=>{a("log","at pages/workspace/bridgeServerImpl.js:94","picked user",r),d("chooseContacts",e,o,0,r)}})},$=(t,e)=>{uni.navigateBack({delta:1,fail:o=>{a("log","at pages/workspace/bridgeServerImpl.js:105","nav back err",o)}})},U=t=>{uni.showToast({title:t,icon:"none"})};function d(t,e,o,r,s){let p={type:"wf-op-response",handlerName:t,appUrl:e,requestId:o,args:{code:r,data:s}};a("log","at pages/workspace/bridgeServerImpl.js:128","send response",p),f.evalJs(`__messageFromUni('${JSON.stringify(p)}')`)}function y(t,e,o){let r={type:"wf-op-event",handlerName:t,appUrl:e,args:o};a("log","at pages/workspace/bridgeServerImpl.js:139","send event",r),f.evalJs(`__messageFromUni('${JSON.stringify(r)}')`)}var n=_(c()),q={name:"WorkspacePage",data(){return{url:""}},onLoad(t){a("log","at pages/workspace/WorkspaceWebViewPage.nvue:17","onLoad option url",t.url),this.url=decodeURIComponent(t.url)},created(){},mounted(){a("log","at pages/workspace/WorkspaceWebViewPage.nvue:25","init ",getApp().wfc,this.$refs.webview),h(getApp(),this.$refs.webview)},methods:{onPostMessage(t){let e=t.detail.data[0];a("log","at pages/workspace/WorkspaceWebViewPage.nvue:32","onWebViewMessage data",e.obj),S(e.obj)},onReceiveTitle(t){let e=t.detail.title;uni.setNavigationBarTitle({title:e})}}};function B(t,e,o,r,s,p){return(0,n.openBlock)(),(0,n.createElementBlock)("scroll-view",{scrollY:!0,showScrollbar:!0,enableBackToTop:!0,bubble:"true",style:{flexDirection:"column"}},[(0,n.createElementVNode)("u-web-view",{ref:"webview",style:{flex:"1"},src:s.url,"on:onPostMessage":e[0]||(e[0]=(...g)=>p.onPostMessage&&p.onPostMessage(...g)),onReceivedtitle:e[1]||(e[1]=(...g)=>p.onReceiveTitle&&p.onReceiveTitle(...g))},null,40,["src"])])}var l=k(q,[["render",B]]);var m=plus.webview.currentWebview();if(m){let t=parseInt(m.id),e="pages/workspace/WorkspaceWebViewPage",o={};try{o=JSON.parse(m.__query__)}catch(s){}l.mpType="page";let r=Vue.createPageApp(l,{$store:getApp({allowDefault:!0}).$store,__pageId:t,__pagePath:e,__pageQuery:o});r.provide("__globalStyles",Vue.useCssStyles([...__uniConfig.styles,...l.styles||[]])),r.mount("#root")}})();
