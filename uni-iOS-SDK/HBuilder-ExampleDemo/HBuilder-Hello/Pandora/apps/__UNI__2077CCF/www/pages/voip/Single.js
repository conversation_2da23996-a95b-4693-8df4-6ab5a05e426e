"use weex:vue";

if (typeof Promise !== 'undefined' && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor
    return this.then(
      value => promise.resolve(callback()).then(() => value),
      reason => promise.resolve(callback()).then(() => {
        throw reason
      })
    )
  }
};

if (typeof uni !== 'undefined' && uni && uni.requireGlobal) {
  const global = uni.requireGlobal()
  ArrayBuffer = global.ArrayBuffer
  Int8Array = global.Int8Array
  Uint8Array = global.Uint8Array
  Uint8ClampedArray = global.Uint8ClampedArray
  Int16Array = global.Int16Array
  Uint16Array = global.Uint16Array
  Int32Array = global.Int32Array
  Uint32Array = global.Uint32Array
  Float32Array = global.Float32Array
  Float64Array = global.Float64Array
  BigInt64Array = global.BigInt64Array
  BigUint64Array = global.BigUint64Array
};


(()=>{var _=Object.create;var j=Object.defineProperty;var W=Object.getOwnPropertyDescriptor;var z=Object.getOwnPropertyNames;var X=Object.getPrototypeOf,q=Object.prototype.hasOwnProperty;var $=(i,e)=>()=>(e||i((e={exports:{}}).exports,e),e.exports);var ee=(i,e,t,a)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of z(e))!q.call(i,n)&&n!==t&&j(i,n,{get:()=>e[n],enumerable:!(a=W(e,n))||a.enumerable});return i};var U=(i,e,t)=>(t=i!=null?_(X(i)):{},ee(e||!i||!i.__esModule?j(t,"default",{value:i,enumerable:!0}):t,i));var T=(i,e,t)=>new Promise((a,n)=>{var r=p=>{try{m(t.next(p))}catch(S){n(S)}},c=p=>{try{m(t.throw(p))}catch(S){n(S)}},m=p=>p.done?a(p.value):Promise.resolve(p.value).then(r,c);m((t=t.apply(i,e)).next())});var D=$((Se,J)=>{J.exports=Vue});var Ee=U(D());function Q(i){return weex.requireModule(i)}function o(i,e,...t){uni.__log__?uni.__log__(i,e,...t):console[i].apply(console,[...t,e])}var L=(i,e)=>{let t=i.__vccOpts||i;for(let[a,n]of e)t[a]=n;return t};var te=Object.defineProperty,ie=(i,e,t)=>e in i?te(i,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):i[e]=t,u=(i,e,t)=>(ie(i,typeof e!="symbol"?e+"":e,t),t),d=class w{static getWFCPlatform(){if(w.platform>0)return w.platform;let e=uni.getSystemInfoSync();return o("log","at config.js:85","systemInfo",e),e.osName==="ios"||e.platform==="ios"?w.platform=e.deviceType!=="phone"?8:1:e.osName==="android"||e.platform==="android"?w.platform=e.deviceType!=="phone"?9:2:w.platform=0,w.platform}static config(e){Object.keys(e).forEach(t=>{w[t]=e[t]})}static urlRedirect(e){return e}static emojiBaseUrl(){return"https://static.wildfirechat.net/twemoji/assets/"}static stickerBaseUrl(){return"https://static.wildfirechat.net/sticker/"}};u(d,"ENABLE_AUTO_LOGIN",!0);u(d,"ENABLE_MULTI_VOIP_CALL",!0);u(d,"ENABLE_SINGLE_VOIP_CALL",!0);u(d,"DEFAULT_PORTRAIT_URL","data:image/png;base64,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");u(d,"DEFAULT_ORGANIZATION_PORTRAIT_URL","data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAABQCAYAAACOEfKtAAAACXBIWXMAAAsTAAALEwEAmpwYAAABlUlEQVR4nO2aIW7DQBBFc409UJRD9AoFJr1DeUADSrogoDws8EslloKCLeUEOcDWEzWtii133d2xH/h0ZD/9Pzs72lVSMBRGM1gBL/zJQAAUAK1kinCgAGg4UH4nASIsABoOVPkoEmEtBGDcN5PodFgvA+Dm+WMSfUFcDMDm/WK79jObANgC0HAgETZ6oDhEjENEnMKMMWIONAbplkHauIlwlTPuwprhMuFhd74vFHJpccuEDeus6UHHfXMHXcJVVTswARCACQcGIpwqaDP0QAHQcKAYY4wxpkLFJQ3Sp8M6+1ONp5fX3ztublUHMP64xYuqBZgyfpiXmgAUAA0Higibh35FDxQADQc6jltkjAkATDjQb9wiEQ6+AY5dGqQZ1MwKcHu8DNLj23nwz9ZeMyvA7toP0vb4/W5lDjUBeAWg4UARYRf9KtIDewB2ONBv3CIR7gHY4UC/cYtEuAdghwP/Z00UndTMskwY+worzaBmFoAoADBlNgIOFACtZGvBgQKg4UD5PeGJsABoOFDlo0iEVQbgDWanvolAkB8PAAAAAElFTkSuQmCC");u(d,"DEFAULT_DEPARTMENT_PORTRAIT_URL","data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAABQCAYAAACOEfKtAAAAAXNSR0IArs4c6QAAAIRlWElmTU0AKgAAAAgABQESAAMAAAABAAEAAAEaAAUAAAABAAAASgEbAAUAAAABAAAAUgEoAAMAAAABAAIAAIdpAAQAAAABAAAAWgAAAAAAAABIAAAAAQAAAEgAAAABAAOgAQADAAAAAQABAACgAgAEAAAAAQAAAFCgAwAEAAAAAQAAAFAAAAAAwtohTAAAAAlwSFlzAAALEwAACxMBAJqcGAAAAVlpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IlhNUCBDb3JlIDYuMC4wIj4KICAgPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4KICAgICAgPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIKICAgICAgICAgICAgeG1sbnM6dGlmZj0iaHR0cDovL25zLmFkb2JlLmNvbS90aWZmLzEuMC8iPgogICAgICAgICA8dGlmZjpPcmllbnRhdGlvbj4xPC90aWZmOk9yaWVudGF0aW9uPgogICAgICA8L3JkZjpEZXNjcmlwdGlvbj4KICAgPC9yZGY6UkRGPgo8L3g6eG1wbWV0YT4KGV7hBwAAAjlJREFUeAHt28FOwkAQgOGt8UF4Cg0nOJjo1bfgEfRkPekj8BZeOGjigRuRcNeTJ30NsW0g2UzatTNTpE3+JiS7dGba/dgAW0oIbAgggAACCCCAAAIIIIAAAgMTyI59vlcPq2lxDuVDtT3fjnNVwoGCTw9UV1N2us3CnSZhF5sbcjpP6QNgNajri7NWg/v4/A7vxaMv20lfTmSo5wGg85UDEECngDOdGQigU8CZzgwE0CngTGcGAugUcKYzAwF0CjjTmYEAOgWc6b25nPX0unYO5TjpfQBcZtvawY9+QhgV7zHL2r08mRYoLvXnl4+retp06r/u5UPEyQ0ggE4BZzozEECngDOdGQigU8CZ3smtHdbbM1LnXnyJnmZZmBRfsu9TcZZ9Xd4W0tVKxHp7RuP496+s8baPxrq7HflfAW33dwVYHe/lZrwfd9vjN8aVK5ES7xA1Gw9q2MGHiAEtTgEw1jC0ATSgxSkAxhqGNoAGtDgFwFjD0AbQgBanABhrGNoAGtDiFNXKIbHmTf4AlFp7DqVmjBa3tUu5xjVv+UoUvwBN4uJRO4/asjmUmvK8q74WsEqaz85ri8knF5uvsNi0+0vCUGrKMfIeKEWUfQCVYDIcQCmi7AOoBJPhAEoRZR9AJZgMB1CKKPsAKsFkOIBSRNkHUAkmwwGUIso+gEowGQ6gFFH2AVSCyXDT5azZ/E3WcfeHUlMOVAvY9JcEWVfTH0pNzZiIRQABBBBAAAEEEEAAAQQQ6K/ALz1vZTdxNVa5AAAAAElFTkSuQmCC");u(d,"DEFAULT_VIDEO_POSTER","data:image/png;base64,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");u(d,"APP_SERVER","https://app.wildfirechat.net");u(d,"ORGANIZATION_SERVER","https://org.wildfirechat.cn");u(d,"WORKSPACE_URL","https://open.wildfirechat.cn/work.html");u(d,"IM_SERVER_HOST","wildfirechat.net");u(d,"QR_CODE_PREFIX_PC_SESSION","wildfirechat://pcsession/");u(d,"ICE_SERVERS",[{uri:"turn:turn.wildfirechat.net:3478",userName:"wfchat",password:"wfchat123"}]);u(d,"LANGUAGE","zh_CN");u(d,"ENABLE_MULTI_CALL_AUTO_JOIN",!0);u(d,"ENABLE_PTT",!1);u(d,"ENABLE_VOIP",!0);u(d,"SDK_PLATFORM_WINDOWS",3);u(d,"SDK_PLATFORM_OSX",4);u(d,"SDK_PLATFORM_WEB",5);u(d,"SDK_PLATFORM_WX",6);u(d,"AMR_TO_MP3_SERVER_ADDRESS",d.APP_SERVER+"/amr2mp3?path=");u(d,"FILE_HELPER_ID","wfc_file_transfer");u(d,"RECALL_REEDIT_TIME_LIMIT",60);u(d,"platform",-1);var F=d;var ae=Object.defineProperty,se=(i,e,t)=>e in i?ae(i,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):i[e]=t,l=(i,e,t)=>(se(i,typeof e!="symbol"?e+"":e,t),t),v=class{};l(v,"STATUS_IDLE",0);l(v,"STATUS_OUTGOING",1);l(v,"STATUS_INCOMING",2);l(v,"STATUS_CONNECTING",3);l(v,"STATUS_CONNECTED",4);var E=class{};l(E,"NONE",0);l(E,"BIG_STREAM",1);l(E,"SMALL_STREAM",2);var V=class{didCallEndWithReason(e){o("log","at wfc/av/engine/callSessionCallback.js:12","didCallEndWithReason",e)}didChangeState(e){o("log","at wfc/av/engine/callSessionCallback.js:24","didChangeState",e)}didParticipantJoined(e,t=!1){o("log","at wfc/av/engine/callSessionCallback.js:33","didParticipantJoined",e,t)}didParticipantConnected(e,t=!1){o("log","at wfc/av/engine/callSessionCallback.js:43","didParticipantConnected",e,t)}didParticipantLeft(e,t,a=!1){o("log","at wfc/av/engine/callSessionCallback.js:53","didParticipantLeft",e,t,a)}didChangeMode(e){o("log","at wfc/av/engine/callSessionCallback.js:61","didChangeMode",e)}didChangeInitiator(e){o("log","at wfc/av/engine/callSessionCallback.js:69","didChangeInitiator",e)}didCreateLocalVideoTrack(e){o("log","at wfc/av/engine/callSessionCallback.js:77","didCreateLocalVideoTrack",e)}didReceiveRemoteVideoTrack(e,t){o("log","at wfc/av/engine/callSessionCallback.js:86","didReceiveRemoteVideoTrack",e,t)}didRemoveRemoteVideoTrack(e){o("log","at wfc/av/engine/callSessionCallback.js:94","didRemoveRemoteVideoTrack",e)}didAudioDeviceChanged(e){o("log","at wfc/av/engine/callSessionCallback.js:99","didAudioDeviceChanged",e)}didError(e){o("log","at wfc/av/engine/callSessionCallback.js:103","didError",e)}didGetStats(e){}didVideoMuted(e,t){o("log","at wfc/av/engine/callSessionCallback.js:116","didVideoMuted",e,t)}didMuteStateChanged(e){o("log","at wfc/av/engine/callSessionCallback.js:125","didMuteStateChanged",e)}didMediaLostPacket(e,t,a=!1){o("log","at wfc/av/engine/callSessionCallback.js:135","didMediaLostPacket",e,t,a)}didUserMediaLostPacket(e,t,a,n,r=!1){o("log","at wfc/av/engine/callSessionCallback.js:147","didUserMediaLostPacket",e,t,a,n,r)}didChangeType(e,t,a=!1){o("log","at wfc/av/engine/callSessionCallback.js:158","didChangeType",e,t,a)}didReportAudioVolume(e,t){}onRequestChangeMode(e){o("log","at wfc/av/engine/callSessionCallback.js:176","onRequestChangeMode",e)}},C=class{};l(C,"Single",0);l(C,"Group",1);l(C,"ChatRoom",2);l(C,"Channel",3);l(C,"SecretChat",5);var g=class{constructor(){l(this,"callId"),l(this,"title"),l(this,"desc"),l(this,"initiator"),l(this,"inviter"),l(this,"state"),l(this,"startTime"),l(this,"connectedTime"),l(this,"endTime"),l(this,"conversation"),l(this,"audioOnly"),l(this,"endReason"),l(this,"conference"),l(this,"audience"),l(this,"advanced"),l(this,"multiCall"),l(this,"videoMuted"),l(this,"audioMuted")}};l(g,"kWFAVEngineStateIdle",0);l(g,"kWFAVEngineStateOutgoing",1);l(g,"kWFAVEngineStateIncomming",2);l(g,"kWFAVEngineStateConnecting",3);l(g,"kWFAVEngineStateConnected",4);l(g,"kWFAVCallEndReasonUnknown",0);l(g,"kWFAVCallEndReasonBusy",1);l(g,"kWFAVCallEndReasonSignalError",2);l(g,"kWFAVCallEndReasonHangup",3);l(g,"kWFAVCallEndReasonMediaError",4);l(g,"kWFAVCallEndReasonRemoteHangup",5);l(g,"kWFAVCallEndReasonOpenCameraFailure",6);l(g,"kWFAVCallEndReasonTimeout",7);l(g,"kWFAVCallEndReasonAcceptByOtherClient",8);l(g,"kWFAVCallEndReasonAllLeft",9);l(g,"kWFAVCallEndReasonRemoteBusy",10);l(g,"kWFAVCallEndReasonRemoteTimeout",11);l(g,"kWFAVCallEndReasonRemoteNetworkError",12);l(g,"kWFAVCallEndReasonRoomDestroyed",13);l(g,"kWFAVCallEndReasonRoomNotExist",14);l(g,"kWFAVCallEndReasonRoomParticipantsFull",15);var N=class{constructor(){l(this,"innerAudioContext")}onReceiveCall(e){o("log","at wfc/av/engine/avengineCallback.js:8","onReceiveCall",e),typeof e=="string"&&(e=Object.assign(new g,JSON.parse(e)));let t;e.conversation.type===C.Single?t="/pages/voip/Single":e.conversation.type===C.Group&&(t="/pages/voip/Multi"),t+=`?session=${JSON.stringify(e)}`,t&&uni.navigateTo({url:t,success:a=>{o("log","at wfc/av/engine/avengineCallback.js:24",`navigate to ${t} success`),a.eventChannel.emit("options",{callSession:e})},fail:a=>{o("log","at wfc/av/engine/avengineCallback.js:30",`navigate to ${t} error`,a)}})}shouldStartRing(e){h.innerAudioContext=uni.createInnerAudioContext(),h.innerAudioContext.src=e?"/static/audios/incoming_call_ring.mp3":"/static/audios/outgoing_call_ring.mp3",h.innerAudioContext.autoplay=!0,h.innerAudioContext.loop=!0,h.innerAudioContext.play(),h.innerAudioContext.onPlay(()=>{o("log","at wfc/av/engine/avengineCallback.js:43","\u5F00\u59CB\u64AD\u653E")}),h.innerAudioContext.onError(t=>{o("error","at wfc/av/engine/avengineCallback.js:46","\u64AD\u653E\u54CD\u94C3\u5931\u8D25",t)})}shouldStopRing(){o("log","at wfc/av/engine/avengineCallback.js:51","shouldStopRing"),h.innerAudioContext.stop(),h.innerAudioContext.destroy(),h.innerAudioContext=null}didCallEnded(e,t){o("log","at wfc/av/engine/avengineCallback.js:59","didCallEnded",e,t);let a=getCurrentPages(),n="pages/voip/Single",r="pages/voip/Multi",c="pages/voip/conference/ConferencePage",m=a[a.length-1].route;(m===n||m===r||m===c)&&uni.navigateBack({delta:1,fail:p=>{o("log","at wfc/av/engine/avengineCallback.js:70","nav back to conversationView err",p)}})}},h=new N,k=class{constructor(){l(this,"userId"),l(this,"callExtra"),l(this,"state"),l(this,"joinTime",0),l(this,"acceptTime",0),l(this,"audioMuted",!1),l(this,"videoMuted",!1),l(this,"audience",!1),l(this,"screenSharing",!1)}},A=F.ENABLE_VOIP?Q("wf-uni-wfc-avclient"):null,P=class{constructor(){l(this,"avengineCallback",h),l(this,"sessionCallback")}isAVEngineKitEnable(){return!!A}init(){A.initAVEngineKit(),plus.globalEvent.addEventListener("wfc-av-event",e=>{f._handleNativeAVEngineEvent(e)}),plus.globalEvent.addEventListener("wfc-av-session-event",e=>{f._handleNativeCallSessionEvent(e)})}_handleNativeAVEngineEvent(e){let t=e.args;if(this.avengineCallback){let a=this.avengineCallback[t[0]];a&&a(...t.slice(1))}else o("warn","at wfc/av/engine/avengineKit.js:47","_handleNativeAVEngineEvent avengineCallback is null",t)}_handleNativeCallSessionEvent(e){let t=e.args;if(t[0]!=="didReportAudioVolume"&&o("log","at wfc/av/engine/avengineKit.js:54","_handleNativeCallSessionEvent",t),t[0]==="resumeVoipPage"){let a=this.currentCallSession();a&&this._resumeVoipPage(a);return}if(this.sessionCallback){let a=this.sessionCallback[t[0]];a&&(t[0]==="didChangeState"||t[0]==="didCallEndWithReason"?a(Number(t[1])):a(...t.slice(1)))}else o("warn","at wfc/av/engine/avengineKit.js:76","_handleNativeCallSessionEvent sessionCallback is null",t)}_resumeVoipPage(e){o("log","at wfc/av/engine/avengineKit.js:81","_resumeVoipPage",e);let t;e.conference?t="/pages/voip/conference/ConferencePage":e.conversation.type===C.Single?t="/pages/voip/Single":e.conversation.type===C.Group&&(t="/pages/voip/Multi"),t+=`?session=${JSON.stringify(e)}`,t&&uni.navigateTo({url:t,success:a=>{o("log","at wfc/av/engine/avengineKit.js:95",`navigate to ${t} success`)},fail:a=>{o("log","at wfc/av/engine/avengineKit.js:98",`navigate to ${t} error`,a)}})}setSessionCallback(e){this.sessionCallback=e}startSingleCall(e,t){let a=A.startSingleCall(e,t);return a?Object.assign(new g,JSON.parse(a)):null}startMultiCall(e,t,a){let n=A.startMultiCall(e,t,a);return n?Object.assign(new g,JSON.parse(n)):null}startConference(e,t,a,n,r,c,m,p,S=!1,O=""){let I=A.startConference(e,t,a,n,r,c,m,p,S,O);return I?Object.assign(new g,JSON.parse(I)):null}joinConference(e,t,a,n,r,c,m,p,S,O,I=""){let x=A.joinConference(e,t,a,n,r,c,m,p,S,O,I);return x?Object.assign(new g,JSON.parse(x)):null}leaveConference(e,t=!1){A.leaveConference(e,t)}kickoffParticipant(e,t,a,n){A.kickoffParticipant(e,t,()=>{a&&a()},r=>{n&&n(r)})}setParticipantVideoType(e,t,a,n){o("log","at wfc/av/engine/avengineKit.js:207","setParticipantVideoType",t,a,n),A.setParticipantVideoType(e,t,a,n)}isSupportMultiCall(){return A.isSupportMultiCall()}isSupportConference(){return A.isSupportConference()}setVideoProfile(e,t=!1){A.setVideoProfile(e,t)}addICEServer(e,t,a){A.addICEServer(e,t,a)}currentCallSession(){let e=A.currentCallSession();return e===""?null:Object.assign(new g,JSON.parse(e))}answerCall(e,t){A.answerCall(e,t)}endCall(e){A.endCall(e)}muteVideo(e,t){A.muteVideo(e,t)}muteAudio(e,t){A.muteAudio(e,t)}switchAudience(e,t){return A.switchAudience(e,t)}downgrade2Voice(e){A.downgrade2Voice(e)}inviteNewParticipant(e,t){A.inviteNewParticipant(e,t)}setLocalVideoView(e,t){A.setLocalVideoView(e,t)}setRemoteVideoView(e,t,a,n=!1){A.setRemoteVideoView(e,t,n,a)}getParticipantProfiles(e){let t=A.getParticipantProfiles(e);if(!t)return[];let a=[];return JSON.parse(t).map(r=>{a.push(Object.assign(new k,r))}),a}getParticipantProfile(e,t,a){let n=A.getParticipantProfile(e,t,a);return n?Object.assign(new k,JSON.parse(n)):null}getMyProfile(e){let t=A.getMyProfile(e);return t?Object.assign(new k,JSON.parse(t)):null}checkOverlayPermission(){return A.checkOverlayPermission()}minimize(e,t=""){A.minimize(e,t),this.sessionCallback=null}setSpeakerOn(e,t){A.setSpeakerOn(e,t)}switchCamera(e){A.switchCamera(e)}},f=new P;var G="/static/image/av/av_minimize.png";var y;function ne(){var i=0,e=plus.ios.import("PHPhotoLibrary"),t=e.authorizationStatus();return t===0?i=null:t==3?i=1:i=0,plus.ios.deleteObject(e),i}function oe(){var i=0,e=plus.ios.import("AVCaptureDevice"),t=e.authorizationStatusForMediaType("vide");return t===0?i=null:t==3?i=1:i=0,plus.ios.deleteObject(e),i}function le(){var i=0,e=plus.ios.import("CLLocationManager"),t=e.locationServicesEnabled(),a=e.authorizationStatus();return t?a===0?i=null:a===3||a===4?i=1:i=0:i=2,plus.ios.deleteObject(e),i}function re(){var i=0,e=plus.ios.import("UIApplication"),t=e.sharedApplication(),a=0;if(t.currentUserNotificationSettings){var n=t.currentUserNotificationSettings();a=n.plusGetAttribute("types"),a==0?(i=0,o("log","at common/permission.js:63","\u63A8\u9001\u6743\u9650\u6CA1\u6709\u5F00\u542F")):(i=1,o("log","at common/permission.js:66","\u5DF2\u7ECF\u5F00\u542F\u63A8\u9001\u529F\u80FD!")),plus.ios.deleteObject(n)}else a=t.enabledRemoteNotificationTypes(),a==0?(i=3,o("log","at common/permission.js:73","\u63A8\u9001\u6743\u9650\u6CA1\u6709\u5F00\u542F!")):(i=4,o("log","at common/permission.js:76","\u5DF2\u7ECF\u5F00\u542F\u63A8\u9001\u529F\u80FD!"));return plus.ios.deleteObject(t),plus.ios.deleteObject(e),i}function ce(){var i=0,e=plus.ios.import("CNContactStore"),t=e.authorizationStatusForEntityType(0);return t===0?i=null:t==3?i=1:i=0,plus.ios.deleteObject(e),i}function Ae(){var i=null,e=plus.ios.import("AVAudioSession"),t=e.sharedInstance(),a=t.recordPermission();return o("log","at common/permission.js:104","permissionStatus:"+a),a===1970168948?i=null:a===1735552628?i=1:i=0,plus.ios.deleteObject(e),i}function ge(){var i=null,e=plus.ios.import("EKEventStore"),t=e.authorizationStatusForEntityType(0);return t==3?(i=1,o("log","at common/permission.js:122","\u65E5\u5386\u6743\u9650\u5DF2\u7ECF\u5F00\u542F")):o("log","at common/permission.js:124","\u65E5\u5386\u6743\u9650\u6CA1\u6709\u5F00\u542F"),plus.ios.deleteObject(e),i}function de(){var i=null,e=plus.ios.import("EKEventStore"),t=e.authorizationStatusForEntityType(1);return t==3?(i=1,o("log","at common/permission.js:136","\u5907\u5FD8\u5F55\u6743\u9650\u5DF2\u7ECF\u5F00\u542F")):o("log","at common/permission.js:138","\u5907\u5FD8\u5F55\u6743\u9650\u6CA1\u6709\u5F00\u542F"),plus.ios.deleteObject(e),i}function ue(i){let e=[];for(let t of i){let a=new Promise((n,r)=>{switch(t){case"push":n(re());break;case"location":n(le());break;case"record":n(Ae());break;case"camera":n(oe());break;case"album":n(ne());break;case"contact":n(ce());break;case"calendar":n(ge());break;case"memo":n(de());break;default:n(0);break}});e.push(a)}return new Promise((t,a)=>{Promise.all(e).then(n=>{let r=1;for(let c of n)if(c!==1&&c!=null){r=c;break}t(r)},n=>{a(n)})})}function pe(i){return new Promise((e,t)=>{plus.android.requestPermissions(i,function(a){for(var n=0,r=0;r<a.granted.length;r++){var c=a.granted[r];o("log","at common/permission.js:206","\u5DF2\u83B7\u53D6\u7684\u6743\u9650\uFF1A"+c),n=1}for(var r=0;r<a.deniedPresent.length;r++){var m=a.deniedPresent[r];o("log","at common/permission.js:211","\u62D2\u7EDD\u672C\u6B21\u7533\u8BF7\u7684\u6743\u9650\uFF1A"+m),n=0}for(var r=0;r<a.deniedAlways.length;r++){var p=a.deniedAlways[r];o("log","at common/permission.js:216","\u6C38\u4E45\u62D2\u7EDD\u7533\u8BF7\u7684\u6743\u9650\uFF1A"+p),n=-1}e(n)},function(a){o("log","at common/permission.js:222","result error: "+a.message),e({code:a.code,message:a.message})})})}function fe(){if(B.isIOS){var i=plus.ios.import("UIApplication"),e=i.sharedApplication(),t=plus.ios.import("NSURL"),a=t.URLWithString("app-settings:");e.openURL(a),plus.ios.deleteObject(a),plus.ios.deleteObject(t),plus.ios.deleteObject(e)}else{var n=plus.android.importClass("android.content.Intent"),r=plus.android.importClass("android.provider.Settings"),c=plus.android.importClass("android.net.Uri"),m=plus.android.runtimeMainActivity(),p=new n;p.setAction(r.ACTION_APPLICATION_DETAILS_SETTINGS);var S=c.fromParts("package",m.getPackageName(),null);p.setData(S),m.startActivity(p)}}var B={get isIOS(){return typeof y=="boolean"?y:y=uni.getSystemInfoSync().platform==="ios"},requestIOS:ue,requestAndroid:pe,gotoAppSetting:fe};function H(i){return T(this,null,function*(){let e=["record"],t=["android.permission.RECORD_AUDIO"];return i||(e.push("camera"),t.push("android.permission.CAMERA")),(B.isIOS?yield B.requestIOS(e):yield B.requestAndroid(t))!==1?(uni.showModal({content:"\u9700\u8981\u76F8\u5173\u6743\u9650",confirmText:"\u8BBE\u7F6E",success:n=>{n.confirm&&B.gotoAppSetting()}}),!1):!0})}var b="/static/image/av/av_hang_up.png",K="/static/image/av/av_video_answer.png",Y="/static/image/av/av_mute.png",Z="/static/image/av/av_mute_hover.png";var s=U(D());var me={container:{"":{width:"750rpx",flex:1,display:"flex",flexDirection:"column",position:"relative",backgroundColor:"rgb(41,41,41)"}},"content-container":{"":{width:"750rpx",flex:1,display:"flex",flexDirection:"column",position:"relative"}},"action-container":{"":{width:"750rpx",height:"200rpx",position:"absolute",bottom:0,left:0,display:"flex",flexDirection:"row",justifyContent:"space-around",paddingBottom:20}},action:{".action-container ":{flex:1,display:"flex",flexDirection:"column",alignItems:"center",fontSize:12}},avatar:{"":{width:60,height:60,borderRadius:3},".remote-media-container ":{width:200,height:200,borderRadius:5}},"action-img":{"":{width:60,height:60}},"remote-media-container":{"":{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",width:"750rpx",flex:1}},desc:{"":{color:"#FFFFFF",fontSize:15,paddingTop:5,paddingRight:0,paddingBottom:5,paddingLeft:0}},"local-media-container":{"":{position:"absolute",height:200,display:"flex",justifyContent:"center",alignItems:"center"}},localVideo:{"":{width:150,height:200},".me":{transform:"scaleX(-1)"}},video:{"":{width:"750rpx",flex:1}},"voip-title-container":{"":{position:"absolute",left:0,top:0,width:"750rpx",display:"flex",marginTop:"44rpx",paddingTop:0,paddingRight:"40rpx",paddingBottom:0,paddingLeft:"40rpx",flexDirection:"row",alignItems:"center",height:"88rpx"}},title:{".voip-title-container ":{position:"absolute",left:0,top:0,width:"750rpx",height:"88rpx",display:"flex",justifyContent:"center",alignItems:"center"}},"webrtc-tip":{"":{position:"absolute",color:"#FF0000",left:0,top:0,zIndex:999}}},ve={name:"Single",data(){return{wfc:getApp().wfc,session:null,audioOnly:!1,audioMuted:!1,videoMuted:!1,callState:0,startTimestamp:0,currentTimestamp:0,showWebrtcTip:!1,ringAudio:null,targetUserId:null}},onLoad(i){o("log","at pages/voip/Single.nvue:138","voip/Single onLoad",i),this.session=JSON.parse(i.session),this.targetUserId=this.session.conversation.target,this.callState=Number(this.session.state),this.audioOnly=this.session.audioOnly,this.session.connectedTime&&(this.startTimestamp=this.session.connectedTime)},onUnload(){},methods:{switchVideoType(){if(!this.session)return;let i=this.participantUserInfo.uid,e=E.NONE;e=E.SMALL_STREAM,o("log","at pages/voip/Single.nvue:166","setParticipantVideoType",i,e),f.setParticipantVideoType(this.session.callId,i,!1,e)},setupSessionCallback(){let i=new V;i.didChangeState=e=>{o("log","at pages/voip/Single.nvue:174","didChangeState",typeof e,e,this.callState),this.callState=e,e===v.STATUS_CONNECTED?this.onVoipConnected():e===v.STATUS_IDLE&&this.timer&&clearInterval(this.timer),o("log","at pages/voip/Single.nvue:185","callState change",e,this.callState)},i.didChangeMode=e=>{this.audioOnly=e},i.didCreateLocalVideoTrack=()=>{o("log","at pages/voip/Single.nvue:193","didCreateLocalVideoTrack")},i.didReceiveRemoteVideoTrack=e=>{o("log","at pages/voip/Single.nvue:200","didReceiveRemoteVideoTrack",e),this.$refs.remoteVideo&&f.setRemoteVideoView(this.session.callId,e,this.$refs.remoteVideo.ref),f.isSupportConference()&&f.setParticipantVideoType(this.session.callId,e,!1,E.BIG_STREAM)},i.didCallEndWithReason=e=>{o("log","at pages/voip/Single.nvue:210","callEndWithReason",e),this.session=null},i.didVideoMuted=(e,t)=>{o("log","at pages/voip/Single.nvue:215","didVideoMuted",e,t),e===this.wfc.getUserId()?(this.videoMuted=t,this.$nextTick(()=>{f.setLocalVideoView(this.session.callId,this.$refs.localVideo.ref)})):t||this.$nextTick(()=>{f.setRemoteVideoView(this.session.callId,this.targetUserId,this.$refs.remoteVideo.ref)})},i.didMediaLostPacket=(e,t)=>{t>6&&o("log","at pages/voip/Single.nvue:232","\u60A8\u7684\u7F51\u7EDC\u4E0D\u597D")},i.didUserMediaLostPacket=(e,t,a,n)=>{a>10&&(n?o("log","at pages/voip/Single.nvue:241","\u5BF9\u65B9\u7F51\u7EDC\u4E0D\u597D"):o("log","at pages/voip/Single.nvue:243","\u60A8\u7684\u7F51\u7EDC\u4E0D\u597D"))},i.didParticipantConnected=e=>{o("log","at pages/voip/Single.nvue:249","didParticipantConnected",e)},i.didReportAudioVolume=(e,t)=>{},o("log","at pages/voip/Single.nvue:256","single setSessionCallback"),getApp().avengineKit.setSessionCallback(i)},answer(){return T(this,null,function*(){if(o("log","at pages/voip/Single.nvue:266","answer"),!(yield H(this.session.audioOnly))){o("log","at pages/voip/Single.nvue:268","no permission, hangup");return}f.answerCall(this.session.callId,!1)})},hangup(){o("log","at pages/voip/Single.nvue:275","hangup");let i=this.session.callId;f.endCall(i),this.session=null},switchCamera(){this.session&&f.switchCamera(this.session.callId)},mute(){this.audioMuted=!this.audioMuted,f.muteAudio(this.session.callId,this.audioMuted)},muteVideo(){this.videoMuted=!this.videoMuted,f.muteVideo(this.session.callId,this.videoMuted)},down2voice(){f.downgrade2Voice(this.session.callId)},screenShare(){},onVoipConnected(){this.timer||(this.startTimestamp||(this.startTimestamp=new Date().getTime()),this.timer=setInterval(()=>{this.currentTimestamp=new Date().getTime()},1e3)),this.audioOnly||setTimeout(()=>{o("log","at pages/voip/Single.nvue:318","setLocalVideoView",this.wfc.getUserId(),this.$refs.localVideo.ref),f.setLocalVideoView(this.session.callId,this.$refs.localVideo.ref),f.setRemoteVideoView(this.session.callId,this.targetUserId,this.$refs.remoteVideo.ref),o("log","at pages/voip/Single.nvue:321","setRemoteVideoView",this.targetUserId,this.$refs.remoteVideo.ref)},100)},timestampFormat(i){i=~~(i/1e3);let e="",t=~~(i/3600);e=t>0?(t<10?"0":"")+t+":":"";let a=~~(i%3600/60);e+=(a<10?"0":"")+a+":";let n=~~(i%60);return e+=(n<10?"0":"")+n,e},minimize(){let i=getApp().avengineKit.checkOverlayPermission();o("log","at pages/voip/Single.nvue:340","overlayPermission granted",i),i?(getApp().avengineKit.minimize(this.session.callId),uni.navigateBack({delta:1,fail:e=>{o("log","at pages/voip/Single.nvue:346","nav back to err",e)}})):uni.showToast({title:"\u9700\u8981\u60AC\u6D6E\u7A97\u6743\u9650",icon:"none"})}},mounted(){let i=f.currentCallSession();if(o("log","at pages/voip/Single.nvue:360","voip/Single mounted",i),!i||i.state===0){o("log","at pages/voip/Single.nvue:362","av call already hangup"),uni.navigateBack({delta:1,fail:e=>{o("log","at pages/voip/Single.nvue:366","nav back to conversationView err",e)}});return}this.session=i,this.setupSessionCallback(),this.callState===v.STATUS_CONNECTED&&this.onVoipConnected()},onBackPress(i){return o("log","at pages/voip/Single.nvue:406","conferencePage, onBackPress",i),i.from!=="navigateBack"},beforeUnmount(){},computed:{participantUserInfo(){let i=this.session.conversation;return this.wfc.getUserInfo(i.target,!1)},selfUserInfo(){return this.wfc.getUserInfo(this.wfc.getUserId(),!1)},duration(){if(this.currentTimestamp<=0)return"00:00";let i=this.currentTimestamp-this.startTimestamp;return this.timestampFormat(i)},computedLocalMediaContainerStyle(){return!this.audioOnly&&this.callState===v.STATUS_CONNECTED?{top:0,right:0,paddingTop:"88rpx"}:{top:0,left:0,marginLeft:"20px",paddingTop:"80rpx"}},computedLocalAvatarStyle(){return this.callState===v.STATUS_CONNECTED&&this.videoMuted?{width:"150px",height:"200px",borderRadius:"5px",marginRight:"5px"}:{width:"60px",height:"60px",borderRadius:"3px"}}}};function he(i,e,t,a,n,r){return(0,s.openBlock)(),(0,s.createElementBlock)("scroll-view",{scrollY:!0,showScrollbar:!0,enableBackToTop:!0,bubble:"true",style:{flexDirection:"column"}},[(0,s.createElementVNode)("div",{style:{flex:"1",display:"flex"}},[n.session?((0,s.openBlock)(),(0,s.createElementBlock)("div",{key:0,class:"container"},[n.audioOnly?((0,s.openBlock)(),(0,s.createElementBlock)("div",{key:0,class:"content-container"},[(0,s.createElementVNode)("div",{class:"local-media-container",style:(0,s.normalizeStyle)(r.computedLocalMediaContainerStyle)},[(0,s.createElementVNode)("u-image",{class:"avatar",src:r.selfUserInfo.portrait},null,8,["src"])],4),(0,s.createElementVNode)("div",{class:"remote-media-container"},[(0,s.createElementVNode)("u-image",{class:"avatar",src:r.participantUserInfo.portrait},null,8,["src"]),(0,s.createElementVNode)("u-text",{class:"desc"},(0,s.toDisplayString)(r.participantUserInfo.displayName),1),n.callState===1?((0,s.openBlock)(),(0,s.createElementBlock)("u-text",{key:0,class:"desc"},"\u7B49\u5F85\u5BF9\u65B9\u63A5\u542C")):n.callState===2?((0,s.openBlock)(),(0,s.createElementBlock)("u-text",{key:1,class:"desc"},"\u9080\u8BF7\u4F60\u8BED\u97F3\u804A\u5929")):n.callState===3?((0,s.openBlock)(),(0,s.createElementBlock)("u-text",{key:2,class:"desc"},"\u63A5\u542C\u4E2D...")):(0,s.createCommentVNode)("",!0)])])):((0,s.openBlock)(),(0,s.createElementBlock)("div",{key:1,class:"content-container"},[(0,s.createElementVNode)("div",{class:"remote-media-container"},[n.callState===4?((0,s.openBlock)(),(0,s.createElementBlock)("UIKit-Video-CallView",{key:0,onClick:e[0]||(e[0]=c=>r.switchVideoType()),ref:"remoteVideo",class:"video"},null,512)):(0,s.createCommentVNode)("",!0),n.callState!==4?((0,s.openBlock)(),(0,s.createElementBlock)("div",{key:1,class:"flex-column flex-justify-center flex-align-center"},[(0,s.createElementVNode)("u-image",{class:"avatar",src:r.participantUserInfo.portrait},null,8,["src"]),(0,s.createElementVNode)("u-text",{class:"desc"},(0,s.toDisplayString)(r.participantUserInfo.displayName),1),n.callState===1?((0,s.openBlock)(),(0,s.createElementBlock)("u-text",{key:0,class:"desc"},"\u7B49\u5F85\u5BF9\u65B9\u63A5\u542C")):n.callState===2?((0,s.openBlock)(),(0,s.createElementBlock)("u-text",{key:1,class:"desc"},"\u9080\u8BF7\u4F60\u89C6\u9891\u804A\u5929")):n.callState===3?((0,s.openBlock)(),(0,s.createElementBlock)("u-text",{key:2,class:"desc"},"\u63A5\u542C\u4E2D...")):(0,s.createCommentVNode)("",!0)])):(0,s.createCommentVNode)("",!0)]),(0,s.createElementVNode)("div",{class:"local-media-container",style:(0,s.normalizeStyle)(r.computedLocalMediaContainerStyle)},[n.callState===4&&!n.videoMuted?((0,s.openBlock)(),(0,s.createElementBlock)("UIKit-Video-CallView",{key:0,ref:"localVideo",class:"localVideo"},null,512)):(0,s.createCommentVNode)("",!0),n.callState!==4||n.videoMuted?((0,s.openBlock)(),(0,s.createElementBlock)("u-image",{key:1,style:(0,s.normalizeStyle)(r.computedLocalAvatarStyle),src:r.selfUserInfo.portrait},null,12,["src"])):(0,s.createCommentVNode)("",!0)],4)])),n.callState===2?((0,s.openBlock)(),(0,s.createElementBlock)("div",{key:2,class:"action-container"},[(0,s.createElementVNode)("div",{class:"action"},[(0,s.createElementVNode)("u-image",{onClick:e[1]||(e[1]=(...c)=>r.hangup&&r.hangup(...c)),class:"action-img",src:b})]),(0,s.createElementVNode)("div",{class:"action"},[(0,s.createElementVNode)("u-image",{onClick:e[2]||(e[2]=(...c)=>r.answer&&r.answer(...c)),class:"action-img",src:K})])])):(0,s.createCommentVNode)("",!0),n.callState===1?((0,s.openBlock)(),(0,s.createElementBlock)("div",{key:3,class:"action-container"},[(0,s.createElementVNode)("div",{class:"action"},[(0,s.createElementVNode)("u-image",{onClick:e[3]||(e[3]=(...c)=>r.hangup&&r.hangup(...c)),class:"action-img",src:b})])])):(0,s.createCommentVNode)("",!0),n.callState===4?((0,s.openBlock)(),(0,s.createElementBlock)("div",{key:4,class:"action-container"},[(0,s.createElementVNode)("div",{class:"action"},[n.audioMuted?((0,s.openBlock)(),(0,s.createElementBlock)("u-image",{key:1,onClick:e[5]||(e[5]=(...c)=>r.mute&&r.mute(...c)),class:"action-img",src:Z})):((0,s.openBlock)(),(0,s.createElementBlock)("u-image",{key:0,onClick:e[4]||(e[4]=(...c)=>r.mute&&r.mute(...c)),class:"action-img",src:Y})),(0,s.createElementVNode)("u-text",{class:"desc"},"\u9759\u97F3")]),(n.audioOnly,(0,s.createCommentVNode)("",!0)),(n.audioOnly,(0,s.createCommentVNode)("",!0)),n.audioOnly?(0,s.createCommentVNode)("",!0):((0,s.openBlock)(),(0,s.createElementBlock)("div",{key:2,class:"action"},[(0,s.createElementVNode)("u-image",{onClick:e[9]||(e[9]=(...c)=>r.switchCamera&&r.switchCamera(...c)),class:"action-img",src:"//static/image/av/av_camera.png"}),(0,s.createElementVNode)("u-text",{class:"desc"},"\u5207\u6362\u6444\u50CF\u5934")])),(n.audioOnly,(0,s.createCommentVNode)("",!0)),(0,s.createElementVNode)("div",{class:"action"},[(0,s.createElementVNode)("u-image",{onClick:e[11]||(e[11]=(...c)=>r.hangup&&r.hangup(...c)),class:"action-img",src:b}),(0,s.createElementVNode)("u-text",{class:"desc"},"\u6302\u65AD")])])):(0,s.createCommentVNode)("",!0),(0,s.createElementVNode)("div",{class:"voip-title-container"},[(0,s.createElementVNode)("div",{class:"title"},[n.callState===4?((0,s.openBlock)(),(0,s.createElementBlock)("u-text",{key:0,class:"desc"},(0,s.toDisplayString)(r.duration),1)):(0,s.createCommentVNode)("",!0)]),(0,s.createElementVNode)("u-image",{onClick:e[12]||(e[12]=(...c)=>r.minimize&&r.minimize(...c)),style:{width:"30px",height:"30px"},src:G})])])):(0,s.createCommentVNode)("",!0)])])}var R=L(ve,[["render",he],["styles",[me]]]);var M=plus.webview.currentWebview();if(M){let i=parseInt(M.id),e="pages/voip/Single",t={};try{t=JSON.parse(M.__query__)}catch(n){}R.mpType="page";let a=Vue.createPageApp(R,{$store:getApp({allowDefault:!0}).$store,__pageId:i,__pagePath:e,__pageQuery:t});a.provide("__globalStyles",Vue.useCssStyles([...__uniConfig.styles,...R.styles||[]])),a.mount("#root")}})();
