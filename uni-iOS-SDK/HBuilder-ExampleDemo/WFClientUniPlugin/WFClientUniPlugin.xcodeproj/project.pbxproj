// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		B70B07E8284A311E00B60AD9 /* NullUserInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = B70B07E6284A311E00B60AD9 /* NullUserInfo.h */; };
		B70B07E9284A311E00B60AD9 /* NullUserInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = B70B07E7284A311E00B60AD9 /* NullUserInfo.m */; };
		B70B07EC284A32A100B60AD9 /* NullGroupInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = B70B07EA284A32A100B60AD9 /* NullGroupInfo.h */; };
		B70B07ED284A32A100B60AD9 /* NullGroupInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = B70B07EB284A32A100B60AD9 /* NullGroupInfo.m */; };
		B758174F2882F3F500F75C26 /* WFChatClient.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = B758174E2882F3F500F75C26 /* WFChatClient.xcframework */; };
		B7E6DBA42844F51500A24AE8 /* ClientModule.h in Headers */ = {isa = PBXBuildFile; fileRef = B7E6DBA22844F51500A24AE8 /* ClientModule.h */; };
		B7E6DBA52844F51500A24AE8 /* ClientModule.m in Sources */ = {isa = PBXBuildFile; fileRef = B7E6DBA32844F51500A24AE8 /* ClientModule.m */; };
		B7E6DBA82844F55800A24AE8 /* ClientProxy.h in Headers */ = {isa = PBXBuildFile; fileRef = B7E6DBA62844F55800A24AE8 /* ClientProxy.h */; };
		B7E6DBA92844F55800A24AE8 /* ClientProxy.m in Sources */ = {isa = PBXBuildFile; fileRef = B7E6DBA72844F55800A24AE8 /* ClientProxy.m */; };
		B7FC21A528B5152D001E7FA5 /* Bugly.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B7FC21A428B5152D001E7FA5 /* Bugly.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		9759EDBE245048A40076CC51 /* WFClientUniPlugin.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = WFClientUniPlugin.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		9759EDC2245048A40076CC51 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		B70B07E6284A311E00B60AD9 /* NullUserInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = NullUserInfo.h; sourceTree = "<group>"; };
		B70B07E7284A311E00B60AD9 /* NullUserInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = NullUserInfo.m; sourceTree = "<group>"; };
		B70B07EA284A32A100B60AD9 /* NullGroupInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = NullGroupInfo.h; sourceTree = "<group>"; };
		B70B07EB284A32A100B60AD9 /* NullGroupInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = NullGroupInfo.m; sourceTree = "<group>"; };
		B758174E2882F3F500F75C26 /* WFChatClient.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = WFChatClient.xcframework; path = ../WF_SDK/WFChatClient.xcframework; sourceTree = "<group>"; };
		B7E6DBA22844F51500A24AE8 /* ClientModule.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ClientModule.h; sourceTree = "<group>"; };
		B7E6DBA32844F51500A24AE8 /* ClientModule.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ClientModule.m; sourceTree = "<group>"; };
		B7E6DBA62844F55800A24AE8 /* ClientProxy.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ClientProxy.h; sourceTree = "<group>"; };
		B7E6DBA72844F55800A24AE8 /* ClientProxy.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ClientProxy.m; sourceTree = "<group>"; };
		B7FC21A428B5152D001E7FA5 /* Bugly.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Bugly.framework; path = ../WF_SDK/Bugly.framework; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		9759EDBB245048A40076CC51 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B7FC21A528B5152D001E7FA5 /* Bugly.framework in Frameworks */,
				B758174F2882F3F500F75C26 /* WFChatClient.xcframework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		9759EDB4245048A40076CC51 = {
			isa = PBXGroup;
			children = (
				9759EDC0245048A40076CC51 /* WFClientUniPlugin */,
				9759EDBF245048A40076CC51 /* Products */,
				B7B996E92882863700AAA555 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		9759EDBF245048A40076CC51 /* Products */ = {
			isa = PBXGroup;
			children = (
				9759EDBE245048A40076CC51 /* WFClientUniPlugin.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		9759EDC0245048A40076CC51 /* WFClientUniPlugin */ = {
			isa = PBXGroup;
			children = (
				B70B07F0284ADB2D00B60AD9 /* model */,
				9759EDC2245048A40076CC51 /* Info.plist */,
				B7E6DBA22844F51500A24AE8 /* ClientModule.h */,
				B7E6DBA32844F51500A24AE8 /* ClientModule.m */,
				B7E6DBA62844F55800A24AE8 /* ClientProxy.h */,
				B7E6DBA72844F55800A24AE8 /* ClientProxy.m */,
			);
			path = WFClientUniPlugin;
			sourceTree = "<group>";
		};
		B70B07F0284ADB2D00B60AD9 /* model */ = {
			isa = PBXGroup;
			children = (
				B70B07E6284A311E00B60AD9 /* NullUserInfo.h */,
				B70B07E7284A311E00B60AD9 /* NullUserInfo.m */,
				B70B07EA284A32A100B60AD9 /* NullGroupInfo.h */,
				B70B07EB284A32A100B60AD9 /* NullGroupInfo.m */,
			);
			path = model;
			sourceTree = "<group>";
		};
		B7B996E92882863700AAA555 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				B7FC21A428B5152D001E7FA5 /* Bugly.framework */,
				B758174E2882F3F500F75C26 /* WFChatClient.xcframework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		9759EDB9245048A40076CC51 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B70B07EC284A32A100B60AD9 /* NullGroupInfo.h in Headers */,
				B7E6DBA82844F55800A24AE8 /* ClientProxy.h in Headers */,
				B70B07E8284A311E00B60AD9 /* NullUserInfo.h in Headers */,
				B7E6DBA42844F51500A24AE8 /* ClientModule.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		9759EDBD245048A40076CC51 /* WFClientUniPlugin */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9759EDC6245048A40076CC51 /* Build configuration list for PBXNativeTarget "WFClientUniPlugin" */;
			buildPhases = (
				9759EDB9245048A40076CC51 /* Headers */,
				9759EDBA245048A40076CC51 /* Sources */,
				9759EDBB245048A40076CC51 /* Frameworks */,
				9759EDBC245048A40076CC51 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = WFClientUniPlugin;
			productName = DCTestUniPlugin;
			productReference = 9759EDBE245048A40076CC51 /* WFClientUniPlugin.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		9759EDB5245048A40076CC51 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1140;
				ORGANIZATIONNAME = DCloud;
				TargetAttributes = {
					9759EDBD245048A40076CC51 = {
						CreatedOnToolsVersion = 11.4.1;
					};
				};
			};
			buildConfigurationList = 9759EDB8245048A40076CC51 /* Build configuration list for PBXProject "WFClientUniPlugin" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 9759EDB4245048A40076CC51;
			productRefGroup = 9759EDBF245048A40076CC51 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				9759EDBD245048A40076CC51 /* WFClientUniPlugin */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		9759EDBC245048A40076CC51 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		9759EDBA245048A40076CC51 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B7E6DBA92844F55800A24AE8 /* ClientProxy.m in Sources */,
				B70B07ED284A32A100B60AD9 /* NullGroupInfo.m in Sources */,
				B70B07E9284A311E00B60AD9 /* NullUserInfo.m in Sources */,
				B7E6DBA52844F51500A24AE8 /* ClientModule.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		9759EDC4245048A40076CC51 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		9759EDC5245048A40076CC51 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		9759EDC7245048A40076CC51 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = Y8356M2VAP;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Frameworks",
					"\"$(SRCROOT)/../WF_SDK\"",
				);
				HEADER_SEARCH_PATHS = "\"$(SRCROOT)/../../SDK/inc\"/**";
				INFOPLIST_FILE = WFClientUniPlugin/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				OTHER_CFLAGS = "-fno-objc-msgsend-selector-stubs";
				OTHER_CPLUSPLUSFLAGS = "$(OTHER_CFLAGS)";
				PRODUCT_BUNDLE_IDENTIFIER = cn.wildfirechat.WFClientUniPlugin;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		9759EDC8245048A40076CC51 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = Y8356M2VAP;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Frameworks",
					"\"$(SRCROOT)/../WF_SDK\"",
				);
				HEADER_SEARCH_PATHS = "\"$(SRCROOT)/../../SDK/inc\"/**";
				INFOPLIST_FILE = WFClientUniPlugin/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				OTHER_CFLAGS = "-fno-objc-msgsend-selector-stubs";
				OTHER_CPLUSPLUSFLAGS = "$(OTHER_CFLAGS)";
				PRODUCT_BUNDLE_IDENTIFIER = cn.wildfirechat.WFClientUniPlugin;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		9759EDB8245048A40076CC51 /* Build configuration list for PBXProject "WFClientUniPlugin" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9759EDC4245048A40076CC51 /* Debug */,
				9759EDC5245048A40076CC51 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9759EDC6245048A40076CC51 /* Build configuration list for PBXNativeTarget "WFClientUniPlugin" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9759EDC7245048A40076CC51 /* Debug */,
				9759EDC8245048A40076CC51 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 9759EDB5245048A40076CC51 /* Project object */;
}
