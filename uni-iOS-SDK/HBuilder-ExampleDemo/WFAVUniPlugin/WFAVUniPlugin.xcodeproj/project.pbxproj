// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		B72583772B0714E700C021C0 /* WebRTC.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = B72583762B0714E700C021C0 /* WebRTC.xcframework */; };
		B725837B2B0714F300C021C0 /* WFAVEngineKit.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = B725837A2B0714F300C021C0 /* WFAVEngineKit.xcframework */; };
		B72583802B07299500C021C0 /* WFAVModule.h in Headers */ = {isa = PBXBuildFile; fileRef = B725837E2B07299500C021C0 /* WFAVModule.h */; };
		B72583812B07299500C021C0 /* WFAVModule.m in Sources */ = {isa = PBXBuildFile; fileRef = B725837F2B07299500C021C0 /* WFAVModule.m */; };
		B72583842B0729C500C021C0 /* WFAVProxy.h in Headers */ = {isa = PBXBuildFile; fileRef = B72583822B0729C500C021C0 /* WFAVProxy.h */; };
		B72583852B0729C500C021C0 /* WFAVProxy.m in Sources */ = {isa = PBXBuildFile; fileRef = B72583832B0729C500C021C0 /* WFAVProxy.m */; };
		B72583882B072C5B00C021C0 /* WFAVRtcView.h in Headers */ = {isa = PBXBuildFile; fileRef = B72583862B072C5B00C021C0 /* WFAVRtcView.h */; };
		B72583892B072C5B00C021C0 /* WFAVRtcView.m in Sources */ = {isa = PBXBuildFile; fileRef = B72583872B072C5B00C021C0 /* WFAVRtcView.m */; };
		B73276662B08A1E8006E652A /* WFChatClient.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = B73276652B08A1E8006E652A /* WFChatClient.xcframework */; };
		B785BFF72B1041110054071C /* WFAVFloatingWindow.m in Sources */ = {isa = PBXBuildFile; fileRef = B785BFF52B1041110054071C /* WFAVFloatingWindow.m */; };
		B785BFF82B1041110054071C /* WFAVFloatingWindow.h in Headers */ = {isa = PBXBuildFile; fileRef = B785BFF62B1041110054071C /* WFAVFloatingWindow.h */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		B72583622B07139800C021C0 /* WFAVUniPlugin.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = WFAVUniPlugin.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B72583762B0714E700C021C0 /* WebRTC.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = WebRTC.xcframework; path = ../WF_SDK/WebRTC.xcframework; sourceTree = "<group>"; };
		B725837A2B0714F300C021C0 /* WFAVEngineKit.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = WFAVEngineKit.xcframework; path = ../WF_SDK/WFAVEngineKit.xcframework; sourceTree = "<group>"; };
		B725837E2B07299500C021C0 /* WFAVModule.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WFAVModule.h; sourceTree = "<group>"; };
		B725837F2B07299500C021C0 /* WFAVModule.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WFAVModule.m; sourceTree = "<group>"; };
		B72583822B0729C500C021C0 /* WFAVProxy.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WFAVProxy.h; sourceTree = "<group>"; };
		B72583832B0729C500C021C0 /* WFAVProxy.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WFAVProxy.m; sourceTree = "<group>"; };
		B72583862B072C5B00C021C0 /* WFAVRtcView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WFAVRtcView.h; sourceTree = "<group>"; };
		B72583872B072C5B00C021C0 /* WFAVRtcView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WFAVRtcView.m; sourceTree = "<group>"; };
		B73276652B08A1E8006E652A /* WFChatClient.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = WFChatClient.xcframework; path = ../WF_SDK/WFChatClient.xcframework; sourceTree = "<group>"; };
		B785BFF52B1041110054071C /* WFAVFloatingWindow.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = WFAVFloatingWindow.m; sourceTree = "<group>"; };
		B785BFF62B1041110054071C /* WFAVFloatingWindow.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WFAVFloatingWindow.h; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		B725835F2B07139800C021C0 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B725837B2B0714F300C021C0 /* WFAVEngineKit.xcframework in Frameworks */,
				B72583772B0714E700C021C0 /* WebRTC.xcframework in Frameworks */,
				B73276662B08A1E8006E652A /* WFChatClient.xcframework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		B72583582B07139700C021C0 = {
			isa = PBXGroup;
			children = (
				B72583642B07139800C021C0 /* WFAVUniPlugin */,
				B72583632B07139800C021C0 /* Products */,
				B72583752B0714E700C021C0 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		B72583632B07139800C021C0 /* Products */ = {
			isa = PBXGroup;
			children = (
				B72583622B07139800C021C0 /* WFAVUniPlugin.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		B72583642B07139800C021C0 /* WFAVUniPlugin */ = {
			isa = PBXGroup;
			children = (
				B725837E2B07299500C021C0 /* WFAVModule.h */,
				B725837F2B07299500C021C0 /* WFAVModule.m */,
				B72583822B0729C500C021C0 /* WFAVProxy.h */,
				B72583832B0729C500C021C0 /* WFAVProxy.m */,
				B72583862B072C5B00C021C0 /* WFAVRtcView.h */,
				B72583872B072C5B00C021C0 /* WFAVRtcView.m */,
				B785BFF62B1041110054071C /* WFAVFloatingWindow.h */,
				B785BFF52B1041110054071C /* WFAVFloatingWindow.m */,
			);
			path = WFAVUniPlugin;
			sourceTree = "<group>";
		};
		B72583752B0714E700C021C0 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				B73276652B08A1E8006E652A /* WFChatClient.xcframework */,
				B725837A2B0714F300C021C0 /* WFAVEngineKit.xcframework */,
				B72583762B0714E700C021C0 /* WebRTC.xcframework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		B725835D2B07139800C021C0 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B72583882B072C5B00C021C0 /* WFAVRtcView.h in Headers */,
				B72583802B07299500C021C0 /* WFAVModule.h in Headers */,
				B72583842B0729C500C021C0 /* WFAVProxy.h in Headers */,
				B785BFF82B1041110054071C /* WFAVFloatingWindow.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		B72583612B07139800C021C0 /* WFAVUniPlugin */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B72583692B07139800C021C0 /* Build configuration list for PBXNativeTarget "WFAVUniPlugin" */;
			buildPhases = (
				B725835D2B07139800C021C0 /* Headers */,
				B725835E2B07139800C021C0 /* Sources */,
				B725835F2B07139800C021C0 /* Frameworks */,
				B72583602B07139800C021C0 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = WFAVUniPlugin;
			productName = WFAVUniPlugin;
			productReference = B72583622B07139800C021C0 /* WFAVUniPlugin.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		B72583592B07139700C021C0 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					B72583612B07139800C021C0 = {
						CreatedOnToolsVersion = 15.0.1;
					};
				};
			};
			buildConfigurationList = B725835C2B07139700C021C0 /* Build configuration list for PBXProject "WFAVUniPlugin" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = B72583582B07139700C021C0;
			productRefGroup = B72583632B07139800C021C0 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				B72583612B07139800C021C0 /* WFAVUniPlugin */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		B72583602B07139800C021C0 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		B725835E2B07139800C021C0 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B785BFF72B1041110054071C /* WFAVFloatingWindow.m in Sources */,
				B72583892B072C5B00C021C0 /* WFAVRtcView.m in Sources */,
				B72583852B0729C500C021C0 /* WFAVProxy.m in Sources */,
				B72583812B07299500C021C0 /* WFAVModule.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		B72583672B07139800C021C0 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		B72583682B07139800C021C0 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		B725836A2B07139800C021C0 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = Y8356M2VAP;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				GENERATE_INFOPLIST_FILE = YES;
				HEADER_SEARCH_PATHS = "\"$(SRCROOT)/../../SDK/inc\"/**";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				PRODUCT_BUNDLE_IDENTIFIER = cn.wildfirechat.WFAVUniPlugin;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		B725836B2B07139800C021C0 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = Y8356M2VAP;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				GENERATE_INFOPLIST_FILE = YES;
				HEADER_SEARCH_PATHS = "\"$(SRCROOT)/../../SDK/inc\"/**";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				PRODUCT_BUNDLE_IDENTIFIER = cn.wildfirechat.WFAVUniPlugin;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		B725835C2B07139700C021C0 /* Build configuration list for PBXProject "WFAVUniPlugin" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B72583672B07139800C021C0 /* Debug */,
				B72583682B07139800C021C0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B72583692B07139800C021C0 /* Build configuration list for PBXNativeTarget "WFAVUniPlugin" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B725836A2B07139800C021C0 /* Debug */,
				B725836B2B07139800C021C0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = B72583592B07139700C021C0 /* Project object */;
}
