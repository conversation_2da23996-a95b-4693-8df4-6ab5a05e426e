{"name": "野火IM SDK", "id": "wf-uni-wfc-client", "version": "0.1.2", "description": "野火IM 原生插件", "_dp_type": "nativeplugin", "_dp_nativeplugin": {"android": {"plugins": [{"type": "module", "name": "wf-uni-wfc-client", "class": "cn.wildfirechat.uni.client.ClientModule"}], "hooksClass": "cn.wildfirechat.uni.client.ClientUniAppHookProxy", "integrateType": "aar", "dependencies": ["androidx.lifecycle:lifecycle-extensions:2.2.0"], "abis": ["arm64-v8a", "armeabi-v7a", "x86"], "minSdkVersion": 21, "compileOptions": {"sourceCompatibility": "1.8", "targetCompatibility": "1.8"}, "useAndroidX": true}, "ios": {"plugins": [{"type": "module", "name": "wf-uni-wfc-client", "class": "WFClientModule"}], "frameworks": ["Bugly.framework", "WFClientUniPlugin.xcframework", "WFChatClient.xcframework"], "embedFrameworks": ["WFChatClient.xcframework"], "validArchitectures": ["arm64"], "hooksClass": "WFClientProxy", "integrateType": "framework", "deploymentTarget": "11.0"}}}