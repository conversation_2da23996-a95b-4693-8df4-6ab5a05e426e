<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/Common.h</key>
		<data>
		LNpNtEeIWFWNqfp83OzxnGqlCOk=
		</data>
		<key>Headers/WFCCAddGroupeMemberNotificationContent.h</key>
		<data>
		jvLO++pfRhcWd1SGd2Rl9MD72yI=
		</data>
		<key>Headers/WFCCArticlesMessageContent.h</key>
		<data>
		OCeFgvYBsjpkMD0TktQKQhsKhd4=
		</data>
		<key>Headers/WFCCCallAddParticipantMessageContent.h</key>
		<data>
		Gp2DUYZvy4oTOkc/ESfeRb7RiWE=
		</data>
		<key>Headers/WFCCCallByeMessageContent.h</key>
		<data>
		lLIN2Wba1CnarrOaKTCj0jdpAo4=
		</data>
		<key>Headers/WFCCCallStartMessageContent.h</key>
		<data>
		TkaXEIURlWRSAUSWH4JFT/k6lpo=
		</data>
		<key>Headers/WFCCCardMessageContent.h</key>
		<data>
		Hl6e1zMZnHARRV/0pP22uW0zNlc=
		</data>
		<key>Headers/WFCCChangeGroupNameNotificationContent.h</key>
		<data>
		sk8T7Vb28CU/13dFzOBH9oDMrac=
		</data>
		<key>Headers/WFCCChangeGroupPortraitNotificationContent.h</key>
		<data>
		RV7X/R5zu1nk7gWMG51Rau9bu18=
		</data>
		<key>Headers/WFCCChannelInfo.h</key>
		<data>
		J/5lIOlldCotTh7EdP/hQUDYzB4=
		</data>
		<key>Headers/WFCCChannelMenu.h</key>
		<data>
		3Qvl119TweX+H7N+gpdmlNsk1pU=
		</data>
		<key>Headers/WFCCChannelMenuEventMessageContent.h</key>
		<data>
		KGX0HpyfUYU/+5LBf7Y7PWNyDf0=
		</data>
		<key>Headers/WFCCChatroomInfo.h</key>
		<data>
		D5lCwDH3GJCnM1X7Ug3CnX5RXsw=
		</data>
		<key>Headers/WFCCChatroomMemberInfo.h</key>
		<data>
		uAzguqEQAeOcOzpYPuX+FidG9dc=
		</data>
		<key>Headers/WFCCCompositeMessageContent.h</key>
		<data>
		ygnyDZtDTn9hq0Ar+RmKc95ysAQ=
		</data>
		<key>Headers/WFCCConferenceInviteMessageContent.h</key>
		<data>
		7U7eIKenVwUFlrh6L5QWBsQm5os=
		</data>
		<key>Headers/WFCCConversation.h</key>
		<data>
		Xg2lkJiRwe5r5WjZQjOaY9wYaA4=
		</data>
		<key>Headers/WFCCConversationInfo.h</key>
		<data>
		ySFM+XEYbg8zv0Bn3psq1zV9T0k=
		</data>
		<key>Headers/WFCCConversationSearchInfo.h</key>
		<data>
		hojxK+qnxQcVKX2z8pVoB85zK/A=
		</data>
		<key>Headers/WFCCCreateGroupNotificationContent.h</key>
		<data>
		yKDBCXekBAKB97vuPrGqvQqz/Ls=
		</data>
		<key>Headers/WFCCDeleteMessageContent.h</key>
		<data>
		JgLZw5Iosxii47IRTkbYseNa010=
		</data>
		<key>Headers/WFCCDeliveryReport.h</key>
		<data>
		Mx9wvAK8IjC+EQwbZuqQBf12O3A=
		</data>
		<key>Headers/WFCCDictionary.h</key>
		<data>
		tyKSRT1vYkFUD8CKdLWQR+AayPk=
		</data>
		<key>Headers/WFCCDismissGroupNotificationContent.h</key>
		<data>
		4OSaSDIkdb6xBrHm+MLR2oFBzKU=
		</data>
		<key>Headers/WFCCDomainInfo.h</key>
		<data>
		cW1JstDhOj1rfqEhqdBj71VYvKA=
		</data>
		<key>Headers/WFCCEnterChannelChatMessageContent.h</key>
		<data>
		oxI3DtPGppGSlY1l0JgHcU2eMF8=
		</data>
		<key>Headers/WFCCEnums.h</key>
		<data>
		qNHY+LulPY/c3tPfoonhObTr1r0=
		</data>
		<key>Headers/WFCCFileMessageContent.h</key>
		<data>
		5wbl0fY5k0YAcEfHt+/grdcw+w0=
		</data>
		<key>Headers/WFCCFileRecord.h</key>
		<data>
		4aIaA33n6bUCX8O6J0xxmsa1Pos=
		</data>
		<key>Headers/WFCCFriend.h</key>
		<data>
		7KThvPXsIvxHuOLJPEinuIxJnt0=
		</data>
		<key>Headers/WFCCFriendAddedMessageContent.h</key>
		<data>
		3iB08uqoiC7mBgsHek5V1uqUPcc=
		</data>
		<key>Headers/WFCCFriendGreetingMessageContent.h</key>
		<data>
		+nFxkjIxc004ir8x96f1ektvbrQ=
		</data>
		<key>Headers/WFCCFriendRequest.h</key>
		<data>
		c1jYJ4ka4ZInQuhTF6YyGMnqU74=
		</data>
		<key>Headers/WFCCGroupInfo.h</key>
		<data>
		96/Vhp003W4SR9FKbeUw4RixYb4=
		</data>
		<key>Headers/WFCCGroupJoinTypeNotificationContent.h</key>
		<data>
		Qtr37R+sD09xV6fgIKZwO8rApSg=
		</data>
		<key>Headers/WFCCGroupMember.h</key>
		<data>
		hBtY1tjk9IiNXPU6TZHTOHhrhN8=
		</data>
		<key>Headers/WFCCGroupMemberAllowNotificationContent.h</key>
		<data>
		JyLiP6wWLIjkbcmVzri8+YDqUyA=
		</data>
		<key>Headers/WFCCGroupMemberMuteNotificationContent.h</key>
		<data>
		TivBTQtIvTtvcL2NdvqF74AapeQ=
		</data>
		<key>Headers/WFCCGroupMuteNotificationContent.h</key>
		<data>
		qLFbHvFV3B0PW18/X9IzTDyK+04=
		</data>
		<key>Headers/WFCCGroupPrivateChatNotificationContent.h</key>
		<data>
		7sAJpuAG5CohYN3Is3k0A53OsF0=
		</data>
		<key>Headers/WFCCGroupSearchInfo.h</key>
		<data>
		3p1LsaEdwpLPBTQdRKkGcrYKVOo=
		</data>
		<key>Headers/WFCCGroupSetManagerNotificationContent.h</key>
		<data>
		77um+nAswlRD90DRTWYSNdQe1XY=
		</data>
		<key>Headers/WFCCGroupSettingsNotificationContent.h</key>
		<data>
		KjNqfUSKMt61fsrMugG2tnr0awo=
		</data>
		<key>Headers/WFCCIMService.h</key>
		<data>
		HVFbofdTKg6x10CH6ipDq7G1upw=
		</data>
		<key>Headers/WFCCImageMessageContent.h</key>
		<data>
		s3kEIc5J2OZUc4sRCNQ87Yt53n4=
		</data>
		<key>Headers/WFCCJoinCallRequestMessageContent.h</key>
		<data>
		xzAvi1X3CA9lt04ULjpeFB7daJA=
		</data>
		<key>Headers/WFCCJsonSerializer.h</key>
		<data>
		JyGwg9hovXqu5d48RAAU60KhStM=
		</data>
		<key>Headers/WFCCKickoffGroupMemberNotificationContent.h</key>
		<data>
		DvOzmLjA8M5RNq7pLEDD/nJmLTc=
		</data>
		<key>Headers/WFCCKickoffGroupMemberVisibleNotificationContent.h</key>
		<data>
		eH/rmf/flDrTAdqjgs3+WNZd5bE=
		</data>
		<key>Headers/WFCCLeaveChannelChatMessageContent.h</key>
		<data>
		DL8WjZgqYS9l/OvtU0AvYHPaMik=
		</data>
		<key>Headers/WFCCLinkMessageContent.h</key>
		<data>
		jOg9HYwkWXRPEXURI+GbDN5jCPU=
		</data>
		<key>Headers/WFCCLocationMessageContent.h</key>
		<data>
		1Ly+2JUo9EsqAewScYTP5JKSu/A=
		</data>
		<key>Headers/WFCCMediaMessageContent.h</key>
		<data>
		dKL+vn0PcsLdH2KYp2FrEQjKVrE=
		</data>
		<key>Headers/WFCCMessage.h</key>
		<data>
		fAupfxwHH1mU/yWc9RxqVNcou3s=
		</data>
		<key>Headers/WFCCMessageContent.h</key>
		<data>
		ts0Q3p2wCt5/RGJYerWAVLLsMNg=
		</data>
		<key>Headers/WFCCModifyGroupAliasNotificationContent.h</key>
		<data>
		xEi8aY2nCX0jEEJfsGwz5knD7KA=
		</data>
		<key>Headers/WFCCModifyGroupExtraNotificationContent.h</key>
		<data>
		J11Ewm5umPZvVVeszmXP2g51NxU=
		</data>
		<key>Headers/WFCCModifyGroupMemberExtraNotificationContent.h</key>
		<data>
		rvJSIEXlU01q/ndzXoOgKR6dH1s=
		</data>
		<key>Headers/WFCCMultiCallOngoingMessageContent.h</key>
		<data>
		rPce0YbjYgHWcEDWp13gv00C7Gs=
		</data>
		<key>Headers/WFCCNetworkService.h</key>
		<data>
		hd+L+d6hS490ulG7MwVSl8ODyeg=
		</data>
		<key>Headers/WFCCNotDeliveredMessageContent.h</key>
		<data>
		oyuBhzo8KFD+LG1HZC7IqpKsAjk=
		</data>
		<key>Headers/WFCCNotificationMessageContent.h</key>
		<data>
		23T1DmQe0mNYLQJRU0XeoKGklM0=
		</data>
		<key>Headers/WFCCPCLoginRequestMessageContent.h</key>
		<data>
		k/r3Yv0yVSbq6FU8T2CLxq6b6z4=
		</data>
		<key>Headers/WFCCPCOnlineInfo.h</key>
		<data>
		XHYyRIsilrjHZNVPPS4a7PxHNG4=
		</data>
		<key>Headers/WFCCPTTSoundMessageContent.h</key>
		<data>
		7stUp2OKzIWTx4Ayf7A0r+XECrg=
		</data>
		<key>Headers/WFCCPTextMessageContent.h</key>
		<data>
		kEio/hGlyJShUFOyrmLfaDmW4W4=
		</data>
		<key>Headers/WFCCProtocol.h</key>
		<data>
		jPpd/6CLqGMng5+HetFH0QrZn6w=
		</data>
		<key>Headers/WFCCQuitGroupNotificationContent.h</key>
		<data>
		izkDAFN61r74fJuQzwzvb6WTrGs=
		</data>
		<key>Headers/WFCCQuitGroupVisibleNotificationContent.h</key>
		<data>
		MNjEZtRR3RODbTrJ1wN84iTWVi4=
		</data>
		<key>Headers/WFCCQuoteInfo.h</key>
		<data>
		ERBNbAyfOp3vf/yfH+xFXvRHVfE=
		</data>
		<key>Headers/WFCCRawMessageContent.h</key>
		<data>
		WXiU7KJa9MfydsGPUvEbQSkLbrw=
		</data>
		<key>Headers/WFCCReadReport.h</key>
		<data>
		RLnJXSMYZRuIrADwxk5m1TIhc2M=
		</data>
		<key>Headers/WFCCRecallMessageContent.h</key>
		<data>
		tf4uFEBvokiIXp5TyVpuJAFcLwU=
		</data>
		<key>Headers/WFCCRichNotificationMessageContent.h</key>
		<data>
		HpaBViVQ5Mr3Ar1gUKE0eiwkkqU=
		</data>
		<key>Headers/WFCCSecretChatInfo.h</key>
		<data>
		mBV4K2IicEM4D2l15yHmYUI+Nwc=
		</data>
		<key>Headers/WFCCSoundMessageContent.h</key>
		<data>
		0HKp6UzDYPNtMEsF5Le0HHTdyCw=
		</data>
		<key>Headers/WFCCStartSecretChatMessageContent.h</key>
		<data>
		uy8dt0OafAkux/C2TQSRVlrC/Mo=
		</data>
		<key>Headers/WFCCStickerMessageContent.h</key>
		<data>
		RAl4w/bqWh+smqw9zyeBe9/c3H4=
		</data>
		<key>Headers/WFCCStreamingTextGeneratedMessageContent.h</key>
		<data>
		woFKNIgjsc8naNTMxv42sLuuYhg=
		</data>
		<key>Headers/WFCCStreamingTextGeneratingMessageContent.h</key>
		<data>
		85FlvdNUDtADujETMUtI3O3xsTI=
		</data>
		<key>Headers/WFCCTextMessageContent.h</key>
		<data>
		uwVXSj/YvO4q1d6NgcSSoyKtEgU=
		</data>
		<key>Headers/WFCCThingsDataContent.h</key>
		<data>
		H0kbM+U+UuGJRdrAD80JPThvhEg=
		</data>
		<key>Headers/WFCCThingsLostEventContent.h</key>
		<data>
		dcQoEIqU6LrlKnTkods0HxE6IZo=
		</data>
		<key>Headers/WFCCTipNotificationMessageContent.h</key>
		<data>
		OUWpXw5oLCcRrZzKMinv4QWyeog=
		</data>
		<key>Headers/WFCCTransferGroupOwnerNotificationContent.h</key>
		<data>
		uPQxlwZhswwfQUa2jsCbTnS3GaI=
		</data>
		<key>Headers/WFCCTypingMessageContent.h</key>
		<data>
		V5jsf9t7lOaQcn4ZbE+Gs0z3RN8=
		</data>
		<key>Headers/WFCCUnknownMessageContent.h</key>
		<data>
		mp0JrMWcEog34JuOo8tTwVYxbOM=
		</data>
		<key>Headers/WFCCUnreadCount.h</key>
		<data>
		qQ9rtAub4TjppdUgobFrqZgF3S8=
		</data>
		<key>Headers/WFCCUserInfo.h</key>
		<data>
		hKbTUBb3e458uNUCvQfNsR/gIKc=
		</data>
		<key>Headers/WFCCUserOnlineState.h</key>
		<data>
		myA+bGrEbvSQvNI9uqAOakbK91w=
		</data>
		<key>Headers/WFCCUtilities.h</key>
		<data>
		u+c0kIEjg98+zbsS706mne3lYoQ=
		</data>
		<key>Headers/WFCCVideoMessageContent.h</key>
		<data>
		xqitZmCxwt/yGMV+5vCzjpLRiWQ=
		</data>
		<key>Headers/WFCChatClient.h</key>
		<data>
		YTmzHdMi4ejNPlcRcOgf3GlkmxI=
		</data>
		<key>Info.plist</key>
		<data>
		yC8caN5BiVGPmIFtm61zrvTAJXs=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/Common.h</key>
		<dict>
			<key>hash2</key>
			<data>
			OUz6QgVKd+5TOOTzJbqUGALWGfkJndXC7brw3xJJ3pM=
			</data>
		</dict>
		<key>Headers/WFCCAddGroupeMemberNotificationContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			EpsJAfExMfDRxNnkv78FsRq19MxhZOOa7zAXkJWjhqc=
			</data>
		</dict>
		<key>Headers/WFCCArticlesMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			eorX+dyEZxC/KyOBe/GEDyXrvHZZuhiwWYPaavCA2Lo=
			</data>
		</dict>
		<key>Headers/WFCCCallAddParticipantMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			AlXCupdVJRWmVnjglrGyavED8Yz+sbIqoYS0GBu/vbI=
			</data>
		</dict>
		<key>Headers/WFCCCallByeMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			4mJFazKyranTM2fv8KTUwvpIaQmp3IE4H1Vd6yHGz7k=
			</data>
		</dict>
		<key>Headers/WFCCCallStartMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			rsMetvs+P6KcPYwKQIE83DDG2zu1ydb0QMmEcvvfmpo=
			</data>
		</dict>
		<key>Headers/WFCCCardMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ZoU9cqNIFYZOPzL6ndiJPN2s7f+BhiB9xBaL4eXipKM=
			</data>
		</dict>
		<key>Headers/WFCCChangeGroupNameNotificationContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			1EWF3W/5k+s0yQUIo+/STDR/vYjq7hCAPZs7haV/GxY=
			</data>
		</dict>
		<key>Headers/WFCCChangeGroupPortraitNotificationContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			VeQkDgvx4xzLQAUvHOZX+DP0pe5iFNa+W8tVeUHXBIg=
			</data>
		</dict>
		<key>Headers/WFCCChannelInfo.h</key>
		<dict>
			<key>hash2</key>
			<data>
			6WeFt0dSRLGSnbn64ICrnGtZNphvudqH/V7qBn7feq8=
			</data>
		</dict>
		<key>Headers/WFCCChannelMenu.h</key>
		<dict>
			<key>hash2</key>
			<data>
			SkZwdrq3TA0/ZErc4eU/x4ym30IS7nxEdFxUl4CXjLg=
			</data>
		</dict>
		<key>Headers/WFCCChannelMenuEventMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			kqR96xTnrwWJmDLxvCP1HsTHLjq9ABAJpUPhY1Ofxow=
			</data>
		</dict>
		<key>Headers/WFCCChatroomInfo.h</key>
		<dict>
			<key>hash2</key>
			<data>
			iGzH+D/CP1f+3wl2eHqSQ9bsMEdrhI/58jAUiFwDl3o=
			</data>
		</dict>
		<key>Headers/WFCCChatroomMemberInfo.h</key>
		<dict>
			<key>hash2</key>
			<data>
			zxOmgf7enEISucSuoB9IsV/o9VzSHNlp5Zmg/9/pPpY=
			</data>
		</dict>
		<key>Headers/WFCCCompositeMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			CM2kbabygG2hCZmfD2Jk6Z99D1Yn8nRXo+K7m+eju1E=
			</data>
		</dict>
		<key>Headers/WFCCConferenceInviteMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			i8Y2mtcByVLP4UMiRKgNlmQqCT7bykO4g0ktXi6pmcM=
			</data>
		</dict>
		<key>Headers/WFCCConversation.h</key>
		<dict>
			<key>hash2</key>
			<data>
			jd69mtim8Cq3R7mScjM5ryetKb/6+r0Jqk/i4dupGHE=
			</data>
		</dict>
		<key>Headers/WFCCConversationInfo.h</key>
		<dict>
			<key>hash2</key>
			<data>
			3f+ILGU/FlBtVp22NTlznEAdS12YcE1WyJTFv7dq1V0=
			</data>
		</dict>
		<key>Headers/WFCCConversationSearchInfo.h</key>
		<dict>
			<key>hash2</key>
			<data>
			+2AEC1ivfDfqhL/uNJFq5bOOvu6Qadi3lGxt6CEvQYw=
			</data>
		</dict>
		<key>Headers/WFCCCreateGroupNotificationContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			pY7DoXVZyiYgFnGGEJb78fCgHMnUy1ObDFXeiAZof50=
			</data>
		</dict>
		<key>Headers/WFCCDeleteMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			TpgwOudamzEDDSNDNggq0kwmq5Ekq/6GaYxWm9ZgOFs=
			</data>
		</dict>
		<key>Headers/WFCCDeliveryReport.h</key>
		<dict>
			<key>hash2</key>
			<data>
			OWY6aKmCkIe/yDO9vQaWTp/aV+Y7lbOLdxoykWcyeRY=
			</data>
		</dict>
		<key>Headers/WFCCDictionary.h</key>
		<dict>
			<key>hash2</key>
			<data>
			gW9qatoKB4pZEuLaDnanhd2YRA8GDovWdsUtbjHtSyo=
			</data>
		</dict>
		<key>Headers/WFCCDismissGroupNotificationContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			yaQ1yXmDH4B6pvTWMTMvD0f6MEioOBwVzOZS8cC93ug=
			</data>
		</dict>
		<key>Headers/WFCCDomainInfo.h</key>
		<dict>
			<key>hash2</key>
			<data>
			9dTddUQIhN8t7WIrLra4TexL7x/tVyBLJDh9Hmz9Qy0=
			</data>
		</dict>
		<key>Headers/WFCCEnterChannelChatMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			SVqeTZMQkETc7rzmwNlbMZhOVwV2HOcrrBW9VEWX4nw=
			</data>
		</dict>
		<key>Headers/WFCCEnums.h</key>
		<dict>
			<key>hash2</key>
			<data>
			YIv49UhmyEim+Jj4uTU0+9/bH88b7wdcSP4UiUwvo9s=
			</data>
		</dict>
		<key>Headers/WFCCFileMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Ke5naJ1rrOtdXPhmBi8Vs9tpTDUsZJxlzB7VcIg73AY=
			</data>
		</dict>
		<key>Headers/WFCCFileRecord.h</key>
		<dict>
			<key>hash2</key>
			<data>
			XwwSIhCpYldnFXUQc5XmQ/A1nR8leevRi5By0He+ECk=
			</data>
		</dict>
		<key>Headers/WFCCFriend.h</key>
		<dict>
			<key>hash2</key>
			<data>
			gHJ0WNEnSAHVNBZsBDweyX67wAwVb4+gl4kGrMDdp5A=
			</data>
		</dict>
		<key>Headers/WFCCFriendAddedMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			VgBKXgCr2B3Sn6STGs8v9TrrI9SePwHkd0fj11L+JoI=
			</data>
		</dict>
		<key>Headers/WFCCFriendGreetingMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			sdlJyHijoC7vh+e19I37KCEe8UrSHdSTsKyIMcplRmY=
			</data>
		</dict>
		<key>Headers/WFCCFriendRequest.h</key>
		<dict>
			<key>hash2</key>
			<data>
			01sNgzdBzWU0dGsP/HexiSsqXQSkxYSD09Ie1sPwoJc=
			</data>
		</dict>
		<key>Headers/WFCCGroupInfo.h</key>
		<dict>
			<key>hash2</key>
			<data>
			yv3HF/PGRpX5iccbi0fiJG8mwChwQsKdgLzhVP8IdaE=
			</data>
		</dict>
		<key>Headers/WFCCGroupJoinTypeNotificationContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			UWXVAFchCctfR1zw4Z58MLM5ktwA3TVUziHrAkMz1BY=
			</data>
		</dict>
		<key>Headers/WFCCGroupMember.h</key>
		<dict>
			<key>hash2</key>
			<data>
			qjxJ2oQGZjuzDeywVker6TOv0ZWCiWCBXidQMW0FTKA=
			</data>
		</dict>
		<key>Headers/WFCCGroupMemberAllowNotificationContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			2/m6Yd2/ZdGmiaQON/XjzuUTrcj/4o4Q18sKj7W48pE=
			</data>
		</dict>
		<key>Headers/WFCCGroupMemberMuteNotificationContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			XowKDr3PicQyindTuBp3s8Isf3m6toxGK8yo0DYVUvw=
			</data>
		</dict>
		<key>Headers/WFCCGroupMuteNotificationContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			I0vaBMST9PHfXlQ7n7THkN3IfD1q5orzXPvJD3Y/p5A=
			</data>
		</dict>
		<key>Headers/WFCCGroupPrivateChatNotificationContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			uaqdJhtVebaqAGbG6p4KxdgvyxAJLe4xfPslLpTYnLY=
			</data>
		</dict>
		<key>Headers/WFCCGroupSearchInfo.h</key>
		<dict>
			<key>hash2</key>
			<data>
			MbFwI4GeIC1sZHtxQuWRW1n/iw7C5fC9W8/vChToadU=
			</data>
		</dict>
		<key>Headers/WFCCGroupSetManagerNotificationContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			tijnFDgdln12dG2BqpOiWDVKuT4PjjRrVVNtY9ZIf+M=
			</data>
		</dict>
		<key>Headers/WFCCGroupSettingsNotificationContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			LRh2YozvXUyuyZfZDBx+KYZ5ALMg4IWrPF2uVAACBaA=
			</data>
		</dict>
		<key>Headers/WFCCIMService.h</key>
		<dict>
			<key>hash2</key>
			<data>
			mBkCL5a6IYUWcNarNs1Vg0ZksuzmRnNga1hfGrsDgvw=
			</data>
		</dict>
		<key>Headers/WFCCImageMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			FOZ1hIIJG3HxNJkKL8cq64oulkGMDE01xaijw8l9yu4=
			</data>
		</dict>
		<key>Headers/WFCCJoinCallRequestMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			zAu3dFjJzIl7b4I7P0EfZDoZqHbcZp4kaKlNTTHvGR0=
			</data>
		</dict>
		<key>Headers/WFCCJsonSerializer.h</key>
		<dict>
			<key>hash2</key>
			<data>
			KHLxniBuW1A6P/7NDiBSA33RF5FkBdUPjW3ILs3gdNg=
			</data>
		</dict>
		<key>Headers/WFCCKickoffGroupMemberNotificationContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			VLESbz/gJhDUmVs8OF992uc8EQ8ft8lQjN63t18TztE=
			</data>
		</dict>
		<key>Headers/WFCCKickoffGroupMemberVisibleNotificationContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			g3ZRN9hG636KMqyF7BuAl+peP3ryebf/LNfEPt3ZquA=
			</data>
		</dict>
		<key>Headers/WFCCLeaveChannelChatMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			W1ziobFnL+1IGb7kMr/mJuaeAfbYb4JsVNsoxy2nNzM=
			</data>
		</dict>
		<key>Headers/WFCCLinkMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			je04qlCJ87zmGhkaSwh+Y3mcZIWt8jDy233GgimsPQQ=
			</data>
		</dict>
		<key>Headers/WFCCLocationMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			IMZf3SSoWodHaydvsD6VhKdTsqbEfZSFIH+6uyDYfPk=
			</data>
		</dict>
		<key>Headers/WFCCMediaMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ohbLG9MuRZtoapfSoVeBwmN8Q6N071RFZurZw+SgUEY=
			</data>
		</dict>
		<key>Headers/WFCCMessage.h</key>
		<dict>
			<key>hash2</key>
			<data>
			b32LtXrfugMv9vxXe/tA/rk41xt+e8ObTwX5OVIcw3Y=
			</data>
		</dict>
		<key>Headers/WFCCMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ZUosxN4ZrEZc5LvPK9LAXH0j6xRGHvXUiSuO7B7SP8I=
			</data>
		</dict>
		<key>Headers/WFCCModifyGroupAliasNotificationContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			0EgehauC2ez48MrpabNN0yz2isB2ZIxFJGouIKcNMKE=
			</data>
		</dict>
		<key>Headers/WFCCModifyGroupExtraNotificationContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			wllrfca9rDDL+h5vY/49hXRSEkV6fkHjT1d+3t2mrqI=
			</data>
		</dict>
		<key>Headers/WFCCModifyGroupMemberExtraNotificationContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			vvdI3gmsn22GewXsrtCwLW8H/vprxgNdgsFRkBW601U=
			</data>
		</dict>
		<key>Headers/WFCCMultiCallOngoingMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			FUXfJtGBWghjBX7GTYXvOE21kR3q4Ap1FbiyXkntgl4=
			</data>
		</dict>
		<key>Headers/WFCCNetworkService.h</key>
		<dict>
			<key>hash2</key>
			<data>
			u7/nKUxTVnTIJOvcoRo63P7OVmQ604HPdR9G4rcjlpo=
			</data>
		</dict>
		<key>Headers/WFCCNotDeliveredMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			gHNvnH4GdODwGySG/8DFUw7aL9ab69LGBMuYqQ2YXks=
			</data>
		</dict>
		<key>Headers/WFCCNotificationMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			YUZRfLun/R7I0bqHw4Mrl7KnnhfKaj0/wnbM0IDC9Pg=
			</data>
		</dict>
		<key>Headers/WFCCPCLoginRequestMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			TAI09QlP7msC4SY0HrdIsyX1ootM+izGqQAdB3vRpqY=
			</data>
		</dict>
		<key>Headers/WFCCPCOnlineInfo.h</key>
		<dict>
			<key>hash2</key>
			<data>
			u41qwzHVYIrR+jZEvDimNA4YkNTwlYa6DGH8BaLK9ks=
			</data>
		</dict>
		<key>Headers/WFCCPTTSoundMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			uzOi0HhJUOs0f4ktkgNc4xjCD1dTn8n5lEzoTc4jjSs=
			</data>
		</dict>
		<key>Headers/WFCCPTextMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			XvZRKc8mf2/ZkoBHyDwnMLrng068LcZ13kzwSReKeOA=
			</data>
		</dict>
		<key>Headers/WFCCProtocol.h</key>
		<dict>
			<key>hash2</key>
			<data>
			9o0gXvEi64Dd55DhgP6FPY/rZQg1vSIgfz3rXFnYHFg=
			</data>
		</dict>
		<key>Headers/WFCCQuitGroupNotificationContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			nmH0DF0aPGko6SbGYUf8+s+RFrQsJmvARGD34QAJxG8=
			</data>
		</dict>
		<key>Headers/WFCCQuitGroupVisibleNotificationContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			CuwdqTD5998kNuu0JcxS5Az+EmbfWjJReUuabz/Khas=
			</data>
		</dict>
		<key>Headers/WFCCQuoteInfo.h</key>
		<dict>
			<key>hash2</key>
			<data>
			HAwGIa4WqeAMr18p0GnQZlAtK7s9E3ihLD0Jaid98Vo=
			</data>
		</dict>
		<key>Headers/WFCCRawMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			KIz5yhX9/j8m8xgWvw+ELYb4Ic+t52rEdDvg8i7jMqY=
			</data>
		</dict>
		<key>Headers/WFCCReadReport.h</key>
		<dict>
			<key>hash2</key>
			<data>
			rb7Q1EBA8r69zD0qiFTZn39sDs/oDiE441mqfdkiXh4=
			</data>
		</dict>
		<key>Headers/WFCCRecallMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			4R32Hfzp8TJRJ/Tk6B9zLNlUHExhecCwQWQnkFuEiiI=
			</data>
		</dict>
		<key>Headers/WFCCRichNotificationMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			AWLzzGhyJma3YPMkATkkyqg5HlibEtPPGzwBDvPwZIU=
			</data>
		</dict>
		<key>Headers/WFCCSecretChatInfo.h</key>
		<dict>
			<key>hash2</key>
			<data>
			nI0vyOBAZvUzP0U37E9A9srzyBkpsjrTB2ATuVj7Kg0=
			</data>
		</dict>
		<key>Headers/WFCCSoundMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			QcRbqxV27KMf/X8BQkrEn5jlEKg+5y5/wOzuxWAvS9w=
			</data>
		</dict>
		<key>Headers/WFCCStartSecretChatMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			zVx5tl6i3NxpMLBX31K56SRqqeaytZORhC155BAReX0=
			</data>
		</dict>
		<key>Headers/WFCCStickerMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			u+ZA58dpp4fGpgt9h83RjYiUjhMSYvcikS6w3S8O0co=
			</data>
		</dict>
		<key>Headers/WFCCStreamingTextGeneratedMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			j4yUn7cF4oon36TRIsHKKtGVyfUaRWA4px3T3G2cf1I=
			</data>
		</dict>
		<key>Headers/WFCCStreamingTextGeneratingMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			UItFJ9Sz9qIpBgv7yiyg/qqUecTV+QWquwxxpU8d9eg=
			</data>
		</dict>
		<key>Headers/WFCCTextMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			3eXlmFboM0t2OueHk+0BWwrcTz69Xm7kS4s4YVqHW+Y=
			</data>
		</dict>
		<key>Headers/WFCCThingsDataContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			YGvz8OLe1OD+lFQ17Y+uoAWaWx9KaiSEfPXtlZzobCk=
			</data>
		</dict>
		<key>Headers/WFCCThingsLostEventContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			AO38+ub8YBjHe/S3FcFXi6APYTBz1x1s7KpA+3PIXTA=
			</data>
		</dict>
		<key>Headers/WFCCTipNotificationMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			90RsFbgpcU6nG7UDzgXhe8VMRY4UDWpJbOLhyerU9pQ=
			</data>
		</dict>
		<key>Headers/WFCCTransferGroupOwnerNotificationContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			YGI11q4MRfksjgvqvIs6M7cOwRuQOimeTS4UtJ9BTKQ=
			</data>
		</dict>
		<key>Headers/WFCCTypingMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			LkZ8mliAFqDgG1lgsFcc/956jRa+dWiMwfgkUzl/lnk=
			</data>
		</dict>
		<key>Headers/WFCCUnknownMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			NpelLyOoJaPjiony8BiFENJKfUGX9/CmDoPkAxalSRw=
			</data>
		</dict>
		<key>Headers/WFCCUnreadCount.h</key>
		<dict>
			<key>hash2</key>
			<data>
			WXsnUNjKVv7ZLm292UKOidK0hrONKryavW9IZyJLUoQ=
			</data>
		</dict>
		<key>Headers/WFCCUserInfo.h</key>
		<dict>
			<key>hash2</key>
			<data>
			3WCjD1cFMpZKuQCFJLf1y6zkEAba2E+SNp/g2GR/y4s=
			</data>
		</dict>
		<key>Headers/WFCCUserOnlineState.h</key>
		<dict>
			<key>hash2</key>
			<data>
			4q1EGCs8+qY9p74Q4Tmib7ZisLHSjDkJ5kd4tlvKoXE=
			</data>
		</dict>
		<key>Headers/WFCCUtilities.h</key>
		<dict>
			<key>hash2</key>
			<data>
			DORRwQpFPlDe7v7nLakyPNjn5Q4wnncxMPctNDELjCM=
			</data>
		</dict>
		<key>Headers/WFCCVideoMessageContent.h</key>
		<dict>
			<key>hash2</key>
			<data>
			dAzsmak0E5VmmiaDL6mlrhniCf97j7d3ksK8KMHFwmU=
			</data>
		</dict>
		<key>Headers/WFCChatClient.h</key>
		<dict>
			<key>hash2</key>
			<data>
			SN/V2LZZOrL/mx0ZsSQ1aoO1aV8DmyBEVVjt+B9o4Wk=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
