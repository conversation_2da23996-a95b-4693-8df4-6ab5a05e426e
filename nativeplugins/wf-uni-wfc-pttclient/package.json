{"name": "野火IM pttClient", "id": "wf-uni-wfc-pttclient", "version": "0.1.2", "description": "野火IM 对讲插件", "_dp_type": "nativeplugin", "_dp_nativeplugin": {"android": {"plugins": [{"type": "module", "name": "wf-uni-wfc-pttclient", "class": "cn.wildfirechat.uni.pttclient.PttClientModule"}], "hooksClass": "cn.wildfirechat.uni.pttclient.PttClientUniAppHookProxy", "integrateType": "aar", "abis": ["arm64-v8a", "armeabi-v7a", "x86"], "minSdkVersion": 21, "compileOptions": {"sourceCompatibility": "1.8", "targetCompatibility": "1.8"}, "useAndroidX": true}, "ios": {"plugins": [{"type": "module", "name": "wf-uni-wfc-pttclient", "class": "WFPttClientModule"}], "frameworks": ["WFPttClientUniPlugin.xcframework", "PttClient.xcframework"], "embedFrameworks": ["PttClient.xcframework"], "validArchitectures": ["arm64"], "hooksClass": "WFPttClientProxy", "integrateType": "framework", "deploymentTarget": "11.0"}}}