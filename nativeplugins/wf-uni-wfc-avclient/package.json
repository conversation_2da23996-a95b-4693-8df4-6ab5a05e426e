{"name": "野火IM AVClient", "id": "wf-uni-wfc-avclient", "version": "0.1.0", "description": "野火IM 音视频原生插件", "_dp_type": "nativeplugin", "_dp_nativeplugin": {"android": {"plugins": [{"type": "module", "name": "wf-uni-wfc-avclient", "class": "cn.wildfirechat.uni.av.AVEngineKitModule"}, {"type": "component", "name": "WF-UIKit-Video-CallView", "class": "cn.wildfirechat.uni.av.AVEngineKitVideoCallComponent"}], "hooksClass": "cn.wildfirechat.uni.av.AVEngineKitUniAppHookProxy", "integrateType": "aar", "dependencies": [], "abis": ["arm64-v8a", "armeabi-v7a", "x86"], "minSdkVersion": 21, "compileOptions": {"sourceCompatibility": "1.8", "targetCompatibility": "1.8"}, "useAndroidX": true}, "ios": {"plugins": [{"type": "module", "name": "wf-uni-wfc-avclient", "class": "WFAVModule"}, {"type": "component", "name": "UIKit-Video-CallView", "class": "WFAVRtcView"}], "frameworks": ["WebRTC.xcframework", "WFAVEngineKit.xcframework", "WFChatClient.xcframework", "WFAVUniPlugin.xcframework"], "embedFrameworks": ["WebRTC.xcframework", "WFAVEngineKit.xcframework", "WFChatClient.xcframework"], "validArchitectures": ["arm64"], "hooksClass": "WFAVProxy", "integrateType": "framework", "deploymentTarget": "11.0"}}}