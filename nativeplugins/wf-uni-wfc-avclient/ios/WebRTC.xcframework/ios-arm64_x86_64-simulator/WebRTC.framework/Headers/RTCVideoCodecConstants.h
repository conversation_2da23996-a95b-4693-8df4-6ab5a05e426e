/*
 *  Copyright 2018 The WebRTC project authors. All Rights Reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS.  All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */

#import <Foundation/Foundation.h>

#import <WebRTC/RTCMacros.h>

RTC_EXTERN NSString* const kRTCVideoCodecVp8Name;
RTC_EXTERN NSString* const kRTCVideoCodecVp9Name;
RTC_EXTERN NSString* const kRTCVideoCodecAv1Name;
